# syntax=docker.pupuvip.com:5000/docker/dockerfile:1-labs
ARG NODE_VER=20-pnpm8
FROM harbor.pupuvip.com:5000/library/node:${NODE_VER}-builder as builder

ARG ENV
ARG BUILD_ENV
ARG PROJECT_NAME
ARG TEMP_TOKEN
ARG GIT_BRANCH
ARG GIT_COMMIT_ID
ARG GIT_MASTER_COMMIT_ID
ARG VERSION
ARG BUILD_USER_ID
ARG BUILD_USER_NAME
# NFS上传用参数，只需要修改APP_BUILD_PATH即可
ENV APP_BUILD_PATH=/usr/src/app/build
ARG NFS_MOUNT_CMD
ARG NFS_PATH
ARG IS_RELEASE
ARG SERVICE_ALIAS
ARG SFTP_TARGET

# 切换工作目录
WORKDIR /usr/src/app
COPY . .

# 安装node_modules
# COPY package*.json ./
# COPY pnpm-lock.yaml ./
# COPY .pnpmfile.cjs ./
ARG PNPM_VER=8.15.9
RUN npm install pnpm@${PNPM_VER} -g

RUN package_json_md5=`md5sum package.json | cut -c -32`\
  && lock_json_md5=`md5sum pnpm-lock.yaml | cut -c -32`\
  && cache_file_name="${PROJECT_NAME}_${ENV}_cache_${package_json_md5}_${lock_json_md5}.tar.gz"\
  && echo "缓存文件名:${cache_file_name}"\
  && rsync -a <EMAIL>::build_cache/${cache_file_name} cache.tar.gz && tar -zxpf cache.tar.gz node_modules/ \
  && echo "-----直接使用缓存, 不进行pnpm install-----" \
  || (echo "-----未找到缓存, 进行pnpm install-----" \
  && pnpm install \
  && echo "-----开始上传缓存-----" \
  && tar -zcpf cache.tar.gz node_modules/ && rsync -a cache.tar.gz <EMAIL>::build_cache/${cache_file_name} \
  && echo "-----缓存上传完成-----")

RUN export NODE_OPTIONS=--max-old-space-size=4096 && ENV=$ENV GIT_BRANCH=$GIT_BRANCH GIT_COMMIT_ID=$GIT_COMMIT_ID GIT_MASTER_COMMIT_ID=$GIT_MASTER_COMMIT_ID PROJECT_VERSION=$VERSION BUILD_USER_ID=$BUILD_USER_ID BUILD_USER_NAME=$BUILD_USER_NAME pnpm run build:$BUILD_ENV

RUN echo "SFTP_TARGET: ${SFTP_TARGET}"

ENV SFTP_TARGET=$SFTP_TARGET
ENV ENV=$ENV
ENV VERSION=$VERSION

# 设置脚本可执行权限
RUN chmod +x pudo/rclone_upload.sh
#执行脚本上传静态资源文件
RUN SFTP_TARGET=$SFTP_TARGET ENV=$ENV SERVICE_ALIAS=$SERVICE_ALIAS VERSION=$VERSION pudo/rclone_upload.sh