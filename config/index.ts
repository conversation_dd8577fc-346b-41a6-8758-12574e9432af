import LocalConfig from './local'
import DevConfig from './dev'
import TestConfig from './itTest'
import ItPreConfig from './itPre'
import ProdConfig from './prod'

const ENV = process.env.BUILD_ENV || 'test'
const BUILD_VERSION = process.env.BUILD_VERSION || ''

const configs = {
  dev: DevConfig,
  test: TestConfig,
  itPre: ItPreConfig,
  prod: ProdConfig,
}

const config = {
  ...(configs[ENV] || LocalConfig),
  BUILD_VERSION,
}

export default config
