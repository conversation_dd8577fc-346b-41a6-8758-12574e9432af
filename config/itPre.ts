import base from './base'

const { publicName, projectName, loginUrl, dmsConfig } = base

export default {
  projectName,
  environment: 'itPre',
  loginUrl,
  baseRoute: publicName,
  // 企业微信
  appid: 'ww9dbeb2758faf497c',
  agentid: '1000082',
  tenantId: 82,
  authUrl: `${window.location.protocol}//dhr.pre.pupufz.com/admin/account/wechat_work`,
  adminState: 'admin_wechat',
  // 请求前缀
  serverUrl: `${window.location.protocol}//dhrapi.pre.pupufz.com/admin/contract/v0.1`,
  // 基础服务请求前缀
  basicServerUrl: `${window.location.protocol}//dhrapi.pre.pupufz.com`,
  ucAppCode: '41013',
  dmsConfig,
}
