#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# 获取commitId
commit_id=$(git rev-parse HEAD)

# 将__CFBundleIdentifier转换为小写
lowercase_bundle_identifier=$(echo "$__CFBundleIdentifier" | tr '[:upper:]' '[:lower:]')

# 存在VSCODE_GIT_EDITOR_NODE表示vscode内置git工具
# lowercase_bundle_identifier包含sourcetree表示sourcetree环境
if [ -n "$VSCODE_GIT_EDITOR_NODE" ] || [[ "$lowercase_bundle_identifier" == *sourcetree* ]]; then
  pnpm exec mf-core updateCheck -c $commit_id -d
else
  exec < /dev/tty
  pnpm exec mf-core updateCheck -c $commit_id
fi
