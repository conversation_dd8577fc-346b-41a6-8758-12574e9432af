@import "./global-variable.less";

html,
body {
  font-weight: @bodyFontWeight;
}

[class^="homePage"] {
  width: 1200px;
  margin: 0 auto;
}

.@{ant-prefix}-design-pro.@{ant-prefix}-pro-basicLayout {
  .@{ant-prefix}-layout-sider {
    &.@{ant-prefix}-pro-sider {
      background-color: @sideBgColor;
    }

    .@{ant-prefix}-layout-sider-children {

      // 顶部logo
      .@{ant-prefix}-pro-sider-logo {
        color: @sideLogoFontColor;

        .pupulogo {
          color: @logColor;
        }
      }

      // 菜单
      .@{ant-prefix}-menu {
        &.@{ant-prefix}-menu-dark {
          background-color: @sideBgColor;
        }

        .@{ant-prefix}-menu-item {
          &.@{ant-prefix}-menu-item-active {
            background-color: @sideActiveColor;
          }

          .@{ant-prefix}-pro-menu-item {
            .@{ant-prefix}icon {
              font-size: @sideIconSize;
              color: @sideFontColor;
            }

            .@{ant-prefix}-pro-menu-item-title {
              color: @sideFontColor;
            }
          }

          &.@{ant-prefix}-menu-item-selected {
            background-color: @sideSelectedColor;
            border-right: 3px solid @sideSelectedBorderColor;

            .@{ant-prefix}-pro-menu-item {
              .@{ant-prefix}icon {
                color: @sideSelectedFontColor;
              }

              .@{ant-prefix}-pro-menu-item-title {
                color: @sideSelectedFontColor;
              }
            }
          }
        }

        .@{ant-prefix}-menu-submenu {
          &.@{ant-prefix}-menu-submenu-active {
            background-color: @sideActiveColor;
          }

          &.@{ant-prefix}-menu-submenu-open {
            border-right: @sideSelectedSubBorderWidth solid @sideSelectedSubBorderColor;
          }

          // &.@{ant-prefix}-menu-submenu-selected {
          // }
          .@{ant-prefix}-pro-menu-item {
            .@{ant-prefix}icon {
              font-size: @sideIconSize;
              color: @sideFontColor;
            }

            .@{ant-prefix}-pro-menu-item-title {
              color: @sideFontColor;
            }
          }

          .@{ant-prefix}-menu-submenu-arrow {
            &:before {
              background: @sideFontColor;
            }

            &:after {
              background: @sideFontColor;
            }
          }

          &.@{ant-prefix}-menu-submenu-open {
            .@{ant-prefix}-menu-submenu-arrow {
              &:before {
                background: @sideFontColor;
              }

              &:after {
                background: @sideFontColor;
              }
            }
          }

          .@{ant-prefix}-menu-sub {
            background-color: @sideSubBgColor !important;
          }
        }
      }
    }
  }

  // a {
  //   color: @primaryColor;
  // }
  .@{ant-prefix}-switch {
    &.@{ant-prefix}-switch-checked {
      .@{ant-prefix}-switch-handle {
        left: calc(57%);
      }
    }
  }
}

.disabledClick {
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: not-allowed !important;
}

.operateLabel:hover {
  text-decoration: underline !important;
}

:global {
  #app {
    #microRoot {
      &.contract-app {
        .@{ant-prefix}-input-affix-wrapper.@{ant-prefix}-input-affix-wrapper-textarea-with-clear-btn {
          border: 1px solid #d9d9d9 !important; // 处理正式环境border样式被覆盖问题
        }
      }
      a.@{ant-prefix}-btn-disabled, a.@{ant-prefix}-btn-disabled:hover, a.@{ant-prefix}-btn-disabled:focus, a.@{ant-prefix}-btn-disabled:active{
        color: rgba(0, 0, 0, 0.25)!important; // 处理合同包模板预览按钮问题
      }
      .@{ant-prefix}-popover-message-title{
        padding-left: 0 !important; // 处理删除提示多了距离
      }
    }
  }
}
