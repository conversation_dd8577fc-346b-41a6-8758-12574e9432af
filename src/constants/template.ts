// 模板名称的提示
export const templateFormatTip = '仅支持中文、英文、数字，以及部分字符（）()【】「」《》-_、.&—'

/**
 *  排版样式可选择字体
 */
export const fontSizeOptions = [
  {
    value: 10,
    label: 10,
  },
  {
    value: 12,
    label: 12,
  },
  {
    value: 14,
    label: 14,
  },
  {
    value: 16,
    label: 16,
  },
  {
    value: 18,
    label: 18,
  },
  {
    value: 20,
    label: 20,
  },
]

/**
 * 排版样式字体选项
 */
export const fontFamiltOptions = [
  {
    value: '0',
    label: '宋体',
  },
  {
    value: '1',
    label: '仿宋',
  },
  {
    value: '2',
    label: '黑体',
  },
  {
    value: '3',
    label: '楷体',
  },
  {
    value: '4',
    label: '微软雅黑',
  },
]

/**
 * 字体值映射
 */
export const fontFamiltMap = {
  0: 'STSong',
  1: 'STFangsong',
  2: 'STHeiti',
  3: 'STKaiti',
  4: 'YaHei',
}

/**
 * 员工单方签署 code
 */
export const STAFF_SIGN_CODE = 'CM_ST_003'

/**
 * 证明模板 code
 */
export const PROOF_TEMPLATE_CODE = 'CM_TT_002'
