// 时间常量
export const TIME_CONSTANTS = {
  YEAR: 365,
  THREE_MONTHS: 90,
  MONTH: 30,
  WEEK: 7,
  DAY: 1,
}

/* eslint-disable */
export const comRegexp = {
  mobile: /^[1][0-9]{10}$/, // 通讯商放宽号码段，所以前端也放宽手机号正则校验
  email: /^[a-zA-Z0-9_-]+@([a-zA-Z0-9]+\.)+(com|cn|net|org)$/,
  // commonChar: /^([\u4e00-\u9fa5]|[a-zA-Z0-9]|[_#-]|[，。？！：；‘“”]|[,.?!:;'""])*$/g, // 汉字&英文&字母&标点符号
  zhChNumReg: /^[A-Za-z0-9\u2E80-\uFE4F]+$/,
  zhChNumSignReg: /^([\u2E80-\uFE4Fa-zA-Z0-9\s`～·~！!@@##$¥%%^……&&**（()）=+=+{【「{\\[\]}」】}｜、|;:；：'"‘“,<，《.>。》/?、？_——\\-])*$/,
  emojiReg: /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/,
}

export const isMicroApp = (function () {
  return !!window.__POWERED_BY_QIANKUN__
}())

// 九宫格组件排列顺序
export const NINE_CODE_SORT = ['TD_IR_004', 'TD_IR_003', 'TD_IR_001', 'TD_IR_008', 'TD_IR_006', 'TD_IR_002', 'TD_IR_009', 'TD_IR_007', 'TD_IR_005']

// 状态
export const STATUS = [
  { value: 1, label: '启用中', color: '#52c41a' },
  { value: 0, label: '已停用', color: '#a0a0a0' },
]

// 法大大状态
export const FDD_STATUS = [
  { value: 1, label: '已上传', color: '#02A7F0' },
  { value: 0, label: '待上传', color: '#F59A23' },
]

// 是和否
export const YES_OR_NO = [
  {
    label: '是', value: true,
  },
  {
    label: '否', value: false,
  },
]

export const RESIGN_BUSINESS_CODE = 'CM_BI_025'

export const RESIGN_INVALID_REASON_CODE = ['CM_IR_005', 'CM_IR_006'] //线下签署、无需签署
