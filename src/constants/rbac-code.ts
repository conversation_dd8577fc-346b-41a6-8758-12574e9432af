import config from '@/config'

const { ucAppCode } = config

// 签署管理
const SIGN_MANAGE = {
  TABS: {
    SIGNING: `${ucAppCode}_con_9001`, // 签署中tab
    SIGNED: `${ucAppCode}_con_9002`, // 已签署 tab
    SIGN_EXPIRED: `${ucAppCode}_con_9003`, // 已失效 tab
    SIGN_VOID: `${ucAppCode}_con_9004`, // 已作废 tab
  },
  START_SIGN: `${ucAppCode}_con_9005`, // 发起签署
  SIGNING: {
    VIEW: `${ucAppCode}_con_9007`, // 查看
    RESEND: `${ucAppCode}_con_9008`, // 重发
    EXPIRED: `${ucAppCode}_con_9009`, // 失效
    COPY: `${ucAppCode}_con_9020`, // 复制链接
  },
  SIGNED: {
    VIEW: `${ucAppCode}_con_9007`, // 查看
    REMOVE: `${ucAppCode}_con_9012`, // 解除合同
    CANCEL: `${ucAppCode}_con_9013`, // 作废
    UPLOAD: `${ucAppCode}_con_9014`, // 导入
  },
  SIGN_EXPIRED: {
    VIEW: `${ucAppCode}_con_9007`, // 查看
  },
  SIGN_VOID: {
    VIEW: `${ucAppCode}_con_9007`, // 查看
  },
  SIGN_FILE: {
    DOWNLOAD: `${ucAppCode}_con_9019`, // 下载
  },
}

// 自营第三方签署管理
const THIRDPARTY_SIGN_MANAGE = {
  TABS: {
    SIGNING: `${ucAppCode}_con_9021`, // 签署中tab
    SIGNED: `${ucAppCode}_con_9022`, // 已签署 tab
    SIGN_EXPIRED: `${ucAppCode}_con_9023`, // 已失效 tab
    SIGN_VOID: `${ucAppCode}_con_9024`, // 已作废 tab
  },
  START_SIGN: `${ucAppCode}_con_9025`, // 发起签署
  SIGNING: {
    VIEW: `${ucAppCode}_con_9027`, // 查看
    RESEND: `${ucAppCode}_con_9028`, // 重发
    EXPIRED: `${ucAppCode}_con_9029`, // 失效
    COPY: `${ucAppCode}_con_9030`, // 失效
  },
  SIGNED: {
    VIEW: `${ucAppCode}_con_9027`, // 查看
    REMOVE: `${ucAppCode}_con_9032`, // 解除合同
    CANCEL: `${ucAppCode}_con_9033`, // 作废
    UPLOAD: `${ucAppCode}_con_9034`, // 导入
  },
  SIGN_EXPIRED: {
    VIEW: `${ucAppCode}_con_9027`, // 查看
  },
  SIGN_VOID: {
    VIEW: `${ucAppCode}_con_9027`, // 查看
  },
  SIGN_FILE: {
    DOWNLOAD: `${ucAppCode}_con_9037`, // 下载
  },
}

// 变量管理
const VAR_MANAGE = {
  CREATE: `${ucAppCode}_con_2002`, // 新增
  LIST: {
    VIEW: `${ucAppCode}_con_2003`, // 查看
    EDIT: `${ucAppCode}_con_2004`, // 编辑
    DISABLED: `${ucAppCode}_con_2005`, // 停用
    ENABLED: `${ucAppCode}_con_2006`, // 启用
    DELETE: `${ucAppCode}_con_2007`, // 删除
  },
  GROUP_TYPE: {
    // 变量分类
    ADD: `${ucAppCode}_con_2008`, // 添加
    EDIT: `${ucAppCode}_con_2009`, // 编辑
    DELETE: `${ucAppCode}_con_2010`, // 新建
  },
}

// 模板管理
const TEMPLATE_MANAGE = {
  CREATE: `${ucAppCode}_con_3002`, // 新增模板
  BATCH_DOWNLOAD_VAR: `${ucAppCode}_con_3008`, // 批量导出变量
  BATCH_UPLOAD: `${ucAppCode}_con_3010`, // 批量上传法大大
  LIST: {
    VIEW: `${ucAppCode}_con_3003`, // 查看
    EDIT: `${ucAppCode}_con_3004`, // 编辑
    DISABLED: `${ucAppCode}_con_3005`, // 停用
    ENABLED: `${ucAppCode}_con_3006`, // 启用
    DELETE: `${ucAppCode}_con_3007`, // 删除
    DOWNLOAD_VAR: `${ucAppCode}_con_3008`, // 导出变量
    UPLOAD: `${ucAppCode}_con_3010`, // 上传法大大
  },
}

// 规则设置
const RULE_CONFIG = {
  RULE_CHECK: `${ucAppCode}_con_4006`,
  TABS: {
    PARAMS_RULE: `${ucAppCode}_con_4001`, // 参数规则
    TYPE_RULE: `${ucAppCode}_con_4002`, // 类型规则
    DEADLINE_RULE: `${ucAppCode}_con_4003`, // 期限规则
    SALARY_RULE: `${ucAppCode}_con_4004`, // 工资签订规则
  },
  TYPE_RULE: {
    CREATE: `${ucAppCode}_con_5002`, // 新增规则
    LIST: {
      VIEW: `${ucAppCode}_con_5003`, // 查看
      EDIT: `${ucAppCode}_con_5004`, // 编辑
      COPY: `${ucAppCode}_con_5005`, // 复制
      DISABLED: `${ucAppCode}_con_5006`, // 停用
      ENABLED: `${ucAppCode}_con_5007`, // 启用
      DELETE: `${ucAppCode}_con_5008`, // 删除
    },
  },
  DEADLINE_RULE: {
    CREATE: `${ucAppCode}_con_6002`, // 新增规则
    LIST: {
      VIEW: `${ucAppCode}_con_6003`, // 查看
      EDIT: `${ucAppCode}_con_6004`, // 编辑
      COPY: `${ucAppCode}_con_6005`, // 复制
      DISABLED: `${ucAppCode}_con_6006`, // 停用
      ENABLED: `${ucAppCode}_con_6007`, // 启用
      DELETE: `${ucAppCode}_con_6008`, // 删除
    },
  },
  SALARY_RULE: {
    CREATE: `${ucAppCode}_con_7002`, // 新增规则
    LIST: {
      VIEW: `${ucAppCode}_con_7003`, // 查看
      EDIT: `${ucAppCode}_con_7004`, // 编辑
      DISABLED: `${ucAppCode}_con_7005`, // 停用
      ENABLED: `${ucAppCode}_con_7006`, // 启用
      DELETE: `${ucAppCode}_con_7007`, // 删除
    },
  },
}

const CONTRACT_PACKAGE = {
  CREATE: `${ucAppCode}_con_8002`, // 新增合同包
  BATCH_DOWNLOAD_VAR: `${ucAppCode}_con_8008`, // 批量导出变量
  LIST: {
    VIEW: `${ucAppCode}_con_8003`, // 查看
    EDIT: `${ucAppCode}_con_8004`, // 编辑
    DISABLED: `${ucAppCode}_con_8005`, // 停用
    ENABLED: `${ucAppCode}_con_8006`, // 启用
    DELETE: `${ucAppCode}_con_8007`, // 删除
    DOWNLOAD_VAR: `${ucAppCode}_con_8008`, // 导出变量
    VERIFICATION_PERSONNEL: `${ucAppCode}_con_8009`, // 校验人员
  },
}

export {
  SIGN_MANAGE,
  VAR_MANAGE,
  TEMPLATE_MANAGE,
  RULE_CONFIG,
  CONTRACT_PACKAGE,
  THIRDPARTY_SIGN_MANAGE,
}
