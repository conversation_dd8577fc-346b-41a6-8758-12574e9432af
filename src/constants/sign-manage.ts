export const SIGN_STATUS = [
  {
    value: 0,
    label: '签署中',
    color: 'process',
  },
  {
    value: 1,
    label: '已签署',
    color: 'done',
  },
  {
    value: 2,
    label: '已失效',
    color: 'yet',
  },
  {
    value: 3,
    label: '已作废',
    color: 'yet',
  },
]

export const dataTypeMap = {
  0: 'Input',
  1: 'InputNumber',
  2: 'InputNumber',
  3: 'DatePicker',
  4: 'DatePicker',
  5: 'Select',
}

export enum OPT_TYPE {
  REMOVE = 1, // 解除
  CANCEL = 2, // 作废
}

/**
 * offer 签署业务 code
 */
export const OFFER_BUSINESS_CODE_INNER = [
  'CM_BI_001',
  'CM_BI_012',
  'CM_BI_013',
  'CM_BI_015',
  'CM_BI_016',
]
export const OFFER_BUSINESS_CODE_OUTER = ['CM_BI_001', 'CM_BI_015', 'CM_BI_016']
export const OFFER_BUSINESS_CODE = 'CM_BI_001'

// 主体换签解除=CM_BI_021
export const OFFER_BUSINESS_CODE_LIFT = 'CM_BI_021'
// 主体换签切换 =CM_BI_022
export const OFFER_BUSINESS_CODE_SWITCH = 'CM_BI_022'

/**
 * 签署模板类型
 */
export enum TEMPLATE_TYPE {
  REMOVE = 'CM_TT_004', // 解除
  CANCEL = 'CM_TT_005', // 作废
}
