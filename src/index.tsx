// import 'core-js/stable';
import './publicPath'
import 'regenerator-runtime/runtime'
import { isMicroApp } from './constants'

if (!isMicroApp) {
  import('./bootstrap').then(({ renderIndividual }) => {
    renderIndividual()
  })
}
// eslint-disable-next-line @typescript-eslint/no-empty-function
export async function bootstrap() {}

/**
 * 应用每次进入都会调用 mount 方法，通常我们在这里触发应用的渲染方法
 */
export async function mount(props) {
  import('./bootstrap').then(({ mount: innerMount }) => {
    innerMount(props)
  })
}

export async function unmount(props) {
  import('./bootstrap').then(({ unmount: innerUnmount }) => {
    innerUnmount(props)
  })
}

// eslint-disable-next-line @typescript-eslint/no-empty-function
export async function update() {}
