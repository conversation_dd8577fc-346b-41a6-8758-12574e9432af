import { PermissionItem } from '@/components/permissionDropdown'

export interface IPackagePage {
  contractPackageName: string
  contractPackageNumber: number
  startDisableDate: number
  endDisableDate: number
  userRangeName: string
  status: number
  contractTemplateId: number
  relatedBusinessItemCode: string
  priority: number
}

export interface IPackageItem {
  contractTemplateName: string
  disableDate: number
  id: number
  name: string
  number: string
  permissions: PermissionItem[]
  priority: number
  relatedBusinessItemCode: string
  relatedBusinessItemName: string
  status: 0 | 1
  timeCreate: number
  userIdCreate: string
  userNameCreate: string
  userRangeName: string
  validDurationDays: number
  validDurationName: string
}

// 弹窗-启用合同包参数
export type EnablePackageType = {
  contractTemplateIds: number[]
  id: number
  relatedContractDeadlineRuleId?: number
  relatedContractTypeRuleId?: number
}

// 规则条件
export type RuleItem = {
  /** 条件的符号 */
  conditionSymbol: string
  /** 条件类型：0 固定 1 自定义 */
  conditionType: number
  /** 字段名称 */
  fieldName: string
  /** 字段名称中文 */
  fieldNameCn: string
  /** 字段的value值（多个用逗号隔开） */
  fieldValue: string
  /** 字段的value中文值（多个用逗号隔开） */
  fieldValueCn: string
}

// 创建合同包
export interface IPackageCreate {
  /** 合同包人员范围 */
  contractPackageUserRangeRules?: Array<RuleItem>
  /** 合同模板IDS */
  contractTemplateIds: Array<number>
  /** 合同包名称 */
  name: string
  /** 优先级 */
  priority: string
  /** 关联的业务项code */
  relatedBusinessItemCode?: string
  /** 关联的业务项ID */
  relatedBusinessItemId?: number
  /** 关联合同时限规则ID */
  relatedContractDeadlineRuleId?: number
  /** 关联合同类型规则ID */
  relatedContractTypeRuleId?: number
  /** 描述/说明 */
  remark?: string
  /** 合同包人员范围名称 */
  userRangeName: string
}

// 模板弹窗
export interface ITemplateRelaItem {
  /** 关联的业务项code */
  enterpriseKeyWord: string
  /** 模板ID */
  id: number
  /** 模板名称 */
  name: string
  /** 员工关键字 */
  personKeyWord: string
  /** 印章定位方式 */
  sealLocateModeCode: string
}

export type TableItem = ITemplateRelaItem & {
  value: string
  label: string
  title?: string
  isMain?: boolean
}

/**
 * 合同包 - 设置人员范围中特殊选项的类型
 */
export interface SpecialOption {
  label: string
  value: string
}
