export interface ITemplateItem {
  id: number
  status: number
  fddStatus: number
  name: string
  relationList: any[]
}

/**
 * 外部模块 url 映射类型
 */
export interface RequestUrlMap {
  /**
   * 请求模板列表
   */
  getTemplateList?: string
  /**
   * 请求变量
   */
  getVariables?: string
  /**
   * 请求模板内容
   */
  getTemplateContent?: string
  /**
   * 作废合同
   */
  cancelContract?: string
}

/**
 * 外部模块 url 映射类型
 */
export interface RequestUrlMap {
  /**
   * 请求模板列表
   */
  getTemplateList?: string
  /**
   * 请求变量
   */
  getVariables?: string
  /**
   * 请求模板内容
   */
  getTemplateContent?: string
  /**
   * 作废合同
   */
  cancelContract?: string
}
