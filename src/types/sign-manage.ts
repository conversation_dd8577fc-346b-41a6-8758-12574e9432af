import { SchemaFormProps } from '@amazebird/schema-form'

// 签署详情基本信息类型
export interface SignDetailProps {
  postName: string // 职位
  postLevelName: string // 岗位
  entryDate: number // 入职日期
  phoneNumber: string // 手机号
  idCardNumber: string // 身份证号码
  workCityName: string // 工作城市
  departmentName: string // 部门
}

// 签署详情类型
export interface SignDetailOtherProps {
  id: number // 合同 id 签署id
  type: number // 合同类型
  status: number // 状态
  contractSubjectName: string // 合同主体
  contractTypeName: string // 合同类型名称
  contractNumber: string // 合同编号
  contractStartTime: number // 合同开始日期
  contractEndTime: number // 合同到期日期
  contractDeadline: string // 合同期限
  sponsorName: string // 发起人
  sponsorNum: string // 发起人工号
  timeCreate: number // 发起签署时间
  fileName: string // 签署文件名
  dataSource: string // 数据来源
  businessItemName: string // 关联业务项
  relatedApplyNumber: string // 关联申请单号
  contractSignDate: number // 合同签订日期
  relatedContractName: string // 关联合同
  isEffect: boolean // 是否生效
  invalidType: 'CM_FC_001' | 'CM_FC_002' | 'CM_FC_003' // 失效分类
  invalidTypeName: string // 失效分类
  invalidTime: number // 失效时间
  invalidReason: string // 失效原因
  invalidReasonCode?: string // 失效原因字典值
  cancelTime: string // 作废时间
  cancelTypeName: string // 作废原因
  cancelRemark: string // 作废备注
  removeTime: number // 解除时间
  removeTypeName: string // 解除原因
  invalidRemark: string // 失效备注
  removeRemark: string // 解除备注
  linkValidDurationDay: number // 有效时长
  linkRemainDay: number // 剩余时长
  relatedSignId: number // 关联合同id (2024.11.4 修改为关联作废协议id)
  relatedSignFileName: string // 关联合同name (2024.11.4 修改为关联作废协议name)
  relatedRenewVisaSignFileName: string // 关联换签合同name
  relatedRenewVisaSignId: number // 关联换签合同id
  relatedBusinessItemCode: string // 关联业务代码
  invalidAttachment?: string // 附件
  // 重发历史
  reSendHistory: {
    reason: string
    sendTime: number
  }[]
  fddContractId?: string
}

export type ContractType = 'SignFile' | 'RelatedFile'

export interface TemplateVar {
  code: string
  value: string
}

// 模板相关信息类型
export interface TemplateContentAndVarsMapProps {
  valueMap: Record<string, string>
  details: any[]
  variables: { code: string; value: string }[]
  userSigns: Array<{
    userName: string
    userNum: string
    variables: TemplateVar[]
  }>
}

export interface SignConfirmHandler {
  getVarsMap: () => any
  getVarsMapForExposeModule: () => any
  initWrapperPosition: () => void
  validateFields: () => Promise<any>
}

// 解除合同和作废的参数
export interface IContractOperate {
  reasonCode: string
  remark: string
  signId: number | string
  templateId: number
  valueMap?: Record<string, any>
  variables: { code: string; value: string }[]
}

// 检查解除合同和作废
export interface IContractCheck {
  optType: 1 | 2
  signId: number | string
}

// 签署管理中解除合同和作废的第一步的插槽
export interface IComponentSlot {
  extraClassName?: string
  form: SchemaFormProps['form']
}

// 签署管理中解除合同和作废
// 获取模板内容和接口（步骤）
export type ActiveType = 'GET_TEMPLATE_CONTENT' | 'RESULT'

// 模块联邦提交方法暴露给外部的参数
export interface SubmitParams {
  /**
   * 签署 id
   */
  signId: string
  /**
   * 模板 id
   */
  templateId: string
  /**
   * 备注
   */
  remark: string
  /**
   * 作废原因 code
   */
  reasonCode: string
  /**
   * 合同变量
   */
  variables: {
    code: string
    value: string
  }
}

// 签署日志信息类型
export interface SignLogProps {
  clientCode: string
  content: string
  contractId: string
  eventType: number
  id: number
  remark: string
  serverCode: string
  sortNum: number
  status: number
  timeCreate: number
  timeUpdate: number
  transferTime: number
}
