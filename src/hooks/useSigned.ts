import { useNavigate } from 'react-router-dom'
import config from '@/config'
import { checkContract } from '@/api/sign-manage'
import { checkContract as thirdpartyCheckContract } from '@/api/thirdparty-sign-manage'
import { OPT_TYPE } from '@/constants/sign-manage'

const { baseRoute } = config

interface IProps {
  tabKey: string
  isThirdparty?: boolean
}

// 解除合同和作废
export const useSigned = ({ tabKey, isThirdparty }: IProps) => {
  const navigate = useNavigate()
  // 作废
  const cancel = async (id: number, businessItemCode: string) => {
    const excuteFn = isThirdparty ? thirdpartyCheckContract : checkContract

    await excuteFn({
      optType: OPT_TYPE.CANCEL,
      signId: id,
    })
    navigate(
      `${baseRoute}/contract/sign-manage${isThirdparty ? '/thirdparty' : '/inside'
      }/cancel?id=${id}&businessItemCode=${businessItemCode}&tabKey=${tabKey}`,
    )
  }

  // 解除
  const remove = async (id: number) => {
    const excuteFn = isThirdparty ? thirdpartyCheckContract : checkContract

    await excuteFn({
      optType: OPT_TYPE.REMOVE,
      signId: id,
    })
    navigate(
      `${baseRoute}/contract/sign-manage${isThirdparty ? '/thirdparty' : '/inside'
      }/remove?id=${id}&tabKey=${tabKey}`,
    )
  }

  return {
    cancel,
    remove,
  }
}
