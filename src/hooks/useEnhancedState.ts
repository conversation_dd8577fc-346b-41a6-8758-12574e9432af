import { useEffect, useRef, useState } from 'react'

type Dispatch<A> = (value: A) => void
type SetStateAction<S> = S | ((prevState: S) => S)

// 组件注销时，set方法不执行，一般用于异步函数中
export default function useEnhancedState<S = any>(
  initialState?: S | (() => S),
): [S, Dispatch<SetStateAction<S>>] {
  const isUnmounted = useRef(false)

  useEffect(
    () => () => {
      isUnmounted.current = true
    },
    [],
  )

  const [state, setState] = useState(initialState)
  return [
    state,
    (newState) => {
      if (!isUnmounted.current) {
        setState(newState)
      }
    },
  ]
}
