import React, { useState } from 'react'
import type { TableRowSelection } from 'antd/es/table/interface'

function useSelection<T>(params?: Omit<TableRowSelection<T>, 'onChange'>) {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [selectedRows, setSelectedRows] = useState<T[]>([])

  // 复选框
  const rowSelection: TableRowSelection<T> = {
    type: 'checkbox',
    fixed: 'left',
    columnWidth: 70,
    selectedRowKeys,
    onChange: (rowKeys: React.Key[], _selectedRows: T[]) => {
      setSelectedRowKeys(rowKeys)
      setSelectedRows(_selectedRows)
    },
    ...params,
  }

  // 清空复选框
  const clearSelection = () => {
    setSelectedRowKeys([])
    setSelectedRows([])
  }

  return {
    selectedRowKeys,
    setSelectedRowKeys,
    setSelectedRows,
    selectedRows,
    rowSelection,
    clearSelection,
  }
}

export default useSelection
