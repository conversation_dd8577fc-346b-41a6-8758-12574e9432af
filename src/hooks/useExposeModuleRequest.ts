import { createRequest } from '@/utils/request/expose'
import { IContractOperate } from '@/types/sign-manage'
import { RequestUrlMap } from '@/types/template'

interface IProps {
  origin: string
  urlMap?: RequestUrlMap
}

export const useExposeModuleRequest: any = ({ origin, urlMap }: IProps) => {
  const { req } = createRequest({ serverUrl: origin })

  /**
   * 作废
   */
  const invalidContract = (params: IContractOperate) =>
    req.post(urlMap?.cancelContract ? urlMap.cancelContract : '/sign/invalid', params)

  /**
   * 获取签署（解除合同、作废）模板内容及变量值
   */
  const getTemplateAndVar = ({
    signId,
    templateId,
  }: {
    signId: number | string
    templateId: number
  }) =>
    req.get(
      urlMap?.getTemplateContent
        ? urlMap.getTemplateContent.replace('$[templateId]', templateId.toString())
        : `/sign/invalid/${signId}/template/${templateId}/content`,
    )

  /**
   * 获取模板列表
   */
  const getTemplateList = (type: string, fddStatus?: 0 | 1) =>
    req.get(urlMap?.getTemplateList ? urlMap.getTemplateList : '/template/simple', {
      fddStatus,
      type,
      typeCode: type,
      isShowSealKeyWord2: false,
    })

  /**
   * 获取变量模板
   */
  const getVariablesApi = (id) => {
    const useUrl = urlMap?.getVariables
      ? urlMap.getVariables.replace('$[templateId]', id)
      : '/template/variables'

    return req.get(useUrl)
  }

  return {
    invalidContract,
    getTemplateList,
    getTemplateAndVar,
    getVariablesApi,
  }
}
