import { RulesDataGroup } from '@pupu/brick/lib/components/RuleBuilder/types'
import { isJSON } from '@/utils/utils'

interface DataProps {
  name: string
  rules: RulesDataGroup
}

type SetValue = (value: DataProps) => void

type GetValue = () => DataProps | null

type RemoveValue = () => void

const CACHE_KEY = 'PERSON_SCOPE_VALUE'

export const usePersonScopeValue = (cacheKey = CACHE_KEY) => {
  const removeValue: RemoveValue = () => localStorage.removeItem(cacheKey)

  const setValue: SetValue = (value) => {
    removeValue()
    const valueString = JSON.stringify(value)
    localStorage.setItem(cacheKey, valueString)
  }

  const getValue: GetValue = () => {
    const value = localStorage.getItem(cacheKey)

    if (!value || !isJSON(value)) {
      // eslint-disable-next-line no-console
      console.warn('不存在设置的人员范围或格式不正确')
      return null
    }

    const result = JSON.parse(value)

    return result
  }

  return {
    setValue,
    getValue,
    removeValue,
  }
}
