import config from '@/config'

export const login = () => {
  const redirectUrl = sessionStorage.getItem('redirectUrl')
  if (redirectUrl) {
    sessionStorage.removeItem('redirectUrl')
    window.location.href = redirectUrl
  } else {
    window.location.href = `${config.baseRoute}/`
  }
}

export const logout = () => {
  sessionStorage.setItem('redirectUrl', window.location.href)
  window.location.href = config.baseRoute + config.loginUrl
}
