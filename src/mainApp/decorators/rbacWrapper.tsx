import React, { useEffect, useState } from 'react'
import { setEntity, getOperationResource } from '@galaxy/rbac'
import config from '@/config'

const { ucAppCode } = config

/**
 * 只在本地开发的时候使用，线上环境由主应用提供
 */

// uc-rbac 配置， 本地开发的时候默认给一个权限配置
const ucRbacConfig = {
  id: 'a98eaa4442f50cef95d01f2149c96f31',
  name: '合同管理',
}

// 是否对接UC-RBAC
export default function rbacWrapper(WrappedComponent) {
  function Rbac(props) {
    const [loading, setLoading] = useState(true)
    useEffect(() => {
      ;(async () => {
        setEntity(ucRbacConfig)
        await getOperationResource({
          ...ucRbacConfig,
          appId: ucAppCode,
        })
        setLoading(false)
      })()
    }, [])

    if (loading) {
      return null
    }
    return <WrappedComponent {...props} />
  }
  return Rbac
}
