import React, { useEffect, useState } from 'react'
import config from '@/config'
import uc from '@galaxy/uc'
import rbac from '@galaxy/rbac'
import userService from '@/mainApp/services/userService'
import { logout } from '@/mainApp/services/authService'

const { ucAppCode } = config

export default function authWrapper(WrappedComponent) {
  function Auth(props) {
    const [loading, setLoading] = useState(true)
    useEffect(() => {
      ;(async () => {
        const isLogin = await uc.isLogin()
        if (isLogin) {
          const userInfo = await uc.getUserInfo()
          userService.userInfo = userInfo
          // 获取实体权限
          // await rbac.getOperationPermission()
          await rbac.getMenuResource({
            id: userInfo.id,
            appId: ucAppCode,
          })
          await rbac.getOperationResource({
            id: userInfo.id,
            appId: ucAppCode,
          })
          setLoading(false)
          return
        }
        logout()
      })()
    }, [])

    if (loading) {
      return null
    }
    return <WrappedComponent {...props} />
  }
  return Auth
}
