import React, { useState } from 'react'
import { message } from 'antd'
import uc from '@galaxy/uc'
import { Login as LoginCom } from '@galaxy/uc-component'
import { RecentlyEntity, SwitchEntity } from '@galaxy/rbac-component'
import { getRecentEntity, getUsefulEntities } from '@galaxy/rbac-component/es/models/dhr'
import { getEntities } from '@galaxy/rbac'
import { login } from '@/mainApp/services/authService'
import style from './style.module.less'

const Login: React.FC = () => {
  const [recentVisiable, setRecentVisiable] = useState(false)
  const [switchVisiable, setSwitchVisiable] = useState(false)
  const [userInfo, setUserInfo] = useState(null)

  // 切换实体权限
  const handleSwitchEntity = () => {
    message.success('登录成功', 3)
    login()
  }

  const initInfo = async () => {
    const info = await uc.getUserInfo()
    setUserInfo(info)
    await getEntities(info.phone, info.num)

    const entities = getUsefulEntities()
    // 无可用权限
    if (!entities.length) {
      message.success('登录成功', 3)
      login()
      return
    }

    // 单一权限直接进入
    if (entities.length === 1) {
      // const entityPath = getEntityPath(entities[0].id)
      handleSwitchEntity()
      return
    }
    const recentEntity = getRecentEntity(info.id)
    if (recentEntity.length) {
      setRecentVisiable(true)
    } else {
      setSwitchVisiable(true)
    }
  }

  const onLoginSuccess = async () => {
    message.success('登录成功', 3)
    initInfo()
  }

  const onLoginError = (error) => {
    /* eslint-disable */
    console.error(error)
  }

  const handleChangeToSwitch = () => {
    setRecentVisiable(false)
    setSwitchVisiable(true)
  }

  const handleCancel = () => {
    const recentEntity = getRecentEntity(userInfo?.id)
    if (recentEntity.length && switchVisiable) {
      setRecentVisiable(true)
      setSwitchVisiable(false)
    } else {
      window.location.reload()
    }
  }

  return (
    <div className={style.loginContainer}>
      <div className={style.loginPageContainer}>
        {!recentVisiable && !switchVisiable ? (
          <LoginCom onSuccess={onLoginSuccess} onError={onLoginError} />
        ) : (
          <>
            {recentVisiable && (
              <div className={style.RecentlyEntity}>
                <RecentlyEntity
                  userId={userInfo?.id}
                  onConfirm={handleSwitchEntity}
                  onCancel={handleChangeToSwitch}
                />
              </div>
            )}
            {switchVisiable && (
              <div className={style.SwitchEntity}>
                <SwitchEntity
                  onCancel={handleCancel}
                  onConfirm={handleSwitchEntity}
                />
              </div>
            )}
          </>
        )}
        <div className={style.copyRight}>
          推荐使用
          <a
            style={{
              color: '#fff',
            }}
            href="https://imgs.pupuapi.com/appfile/ChromeSetup.exe"
          >
            谷歌浏览器，点此下载
            <img
              style={{
                marginTop: '-3px',
              }}
              alt=""
              src={require('@/assets/Vector.png')}
            />
          </a>
        </div>
      </div>
    </div>
  )
}
export default Login
