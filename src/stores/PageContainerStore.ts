import { StateCreator } from 'zustand'
import { getTraffic } from '@/utils/traffic'
import { PageContainerStore } from './types'

const createPageContainerStore: StateCreator<PageContainerStore, [], [], PageContainerStore> = (
  set,
) => ({
  extra: [],

  tabList: [],

  title: [],

  needBack: false,

  tabActiveKey: '',

  showTrafficBtn: !!getTraffic(),

  closely: false,

  pageKeepCache: {}, // 存储页面是否需要保留缓存 key 页面路由， value: boolean

  setExtra: (extra: any = []) => {
    set({
      extra,
    })
  },

  setTabList: (tabList: any = []) => {
    set({
      tabList,
    })
  },

  setTitle: (title: any = []) => {
    set({
      title,
    })
  },

  setBack: (needBack: any = false) => {
    set({
      needBack,
    })
  },

  setActiveTab: (tabActiveKey: any = '') => {
    set({
      tabActiveKey,
    })
  },

  setTrafficBtn: (showTrafficBtn: any = false) => {
    set({
      showTrafficBtn,
    })
  },

  setPageKeepCache: (path, val) => {
    set((state) => ({
      pageKeepCache: {
        ...state.pageKeepCache,
        [path]: val,
      },
    }))
  },

  setClosely: (closely: boolean) => {
    set({
      closely,
    })
  },
})

export default createPageContainerStore
