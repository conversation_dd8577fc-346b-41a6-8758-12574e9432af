export interface PageContainerStore {
  extra: any
  tabList: any
  title: any
  needBack: any
  tabActiveKey: any
  showTrafficBtn: boolean
  pageKeepCache: any
  closely: boolean

  setExtra: (value: any) => void
  setTabList: (value: any) => void
  setTitle: (value: any) => void
  setBack: (value?: boolean) => void
  setActiveTab: (value: any) => void
  setTrafficBtn: (value?: boolean) => void
  setPageKeepCache: (path: any, value: any) => void
  setClosely: (value: boolean) => void
}
