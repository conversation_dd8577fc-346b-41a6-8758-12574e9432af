// import 'core-js/stable';
import './debugForMF'
import './publicPath'
import 'regenerator-runtime/runtime'
import React from 'react'
import { createRoot, Root } from 'react-dom/client'
import RouteFactory from '@/router/RouteFactory'
import mainRoutes from '@/router/route/mainRoutes'
import { AppUpdater, handleAppUpdate } from '@galaxy/app-updater'
import App from './App'

const updater = new AppUpdater({
  appPrefix: 'contract-webapp',
  appName: '合同管理',
})

// 独立访问时需要自己处理应用更新后弹窗提示
if (!window.__POWERED_BY_QIANKUN__) {
  updater.events.on('update', handleAppUpdate)
}

let root: Root | null = null

function render(props) {
  const { container } = props
  const containerDom = container
    ? container.querySelector('#microRoot')
    : document.querySelector('#microRoot')
  root = createRoot(containerDom)
  root.render(<App />)
}

export function renderIndividual() {
  render({})
}

// eslint-disable-next-line @typescript-eslint/no-empty-function
export async function bootstrap() {}

const updateStateFromMainApp = (props, subAppName) => {
  const routeMap = {
    contractRoute: mainRoutes[0],
  }

  // 1. 做一层转换，传给主应用的时候，转换成 Component
  const routes = RouteFactory.joinBaseName([routeMap[`${subAppName}Route`]])
  props.registerRoute(routes)
}

/**
 * 应用每次进入都会调用 mount 方法，通常我们在这里触发应用的渲染方法
 */
export async function mount(props) {
  console.log(props, 'microProps')
  const subAppName = props.name
  props.onGlobalStateChange((params) => updateStateFromMainApp(params, subAppName), true)

  render(props)
}

export async function unmount() {
  root?.unmount()
  root = null
}

// eslint-disable-next-line @typescript-eslint/no-empty-function
export async function update() {}
