import React, { useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import { DhrContainer as Container } from '@galaxy/dhr-style'
import { useStore } from '@/stores'
import { isNil } from 'lodash-es'
import ExtraWrap from '@/components/baseContainer/extraWrap'

import queryString from 'query-string'

interface StateProps {
  isWechat: string
  isHideNav: string
  isHideMenu: string
}

interface IProps {
  /**
   * 路由组件
   */
  children: any
  /**
   * 返回按钮
   */
  needBack?: any
  /**
   * 通过路由名称传入的默认 title
   */
  defaultTitle?: React.ReactNode
}

const DhrContainer: React.FC<IProps> = function ({ children, defaultTitle }: IProps) {
  const location = useLocation()
  const store = useStore((state) => state)
  const { setTitle, title, needBack } = store || {}
  const { isHideNav = false } = queryString.parse(location.search) as unknown as StateProps
  const useHideNav = sessionStorage.getItem('isHideNav') || isHideNav

  /**
   * TODO：初始化路由默认名称（defaultTitle）以后，业务上可能对页面的 title 会进行重新操作（进行 setTitle），从默认路由名称变到操作后的名称，导致闪一下
   * 后续优化
   */
  useEffect(() => {
    setTitle(React.isValidElement(defaultTitle) ? defaultTitle : <span>{defaultTitle}</span>)
  }, [defaultTitle])

  return (
    <Container
      title={title}
      needBack={needBack}
      extra={<ExtraWrap key="extra" />}
      noStyle
      unableToSticky={!isNil(useHideNav)}
    >
      {children}
    </Container>
  )
}

export default DhrContainer
