import React from 'react'
import { Select, Row, Col, SelectProps, Tooltip } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'

import styles from './style.module.less'

export const typeOptions = [
  {
    value: '1',
    label: '固定值',
    tooltip: '指该字段本身包含的具体数值或文本内容，可直接指定。',
  },
  {
    value: '2',
    label: '业务赋值',
    tooltip: '上游系统实际发起业务时，传输的业务数值或文本内容。',
  },
]

interface IProps {
  isSpecial?: boolean
  component: React.ReactNode
  value?: SelectProps['value']
  onChange?: SelectProps['onChange']
}

const SpecialWrapper = ({ value, component, isSpecial, onChange }: IProps) => {
  if (!isSpecial) {
    return component
  }
  return (
    <Row gutter={8} style={{ width: '100%' }}>
      <Col span={10}>
        <Select
          value={value}
          onChange={onChange}
          placeholder="请选择类型"
          options={typeOptions.map((o) => ({
            value: o.value,
            title: o.label,
            label: (
              <div className={styles.groupNameWrapper}>
                {o.label}
                <Tooltip title={o.tooltip}>
                  <QuestionCircleOutlined className={styles.icon} />
                </Tooltip>
              </div>
            ),
          }))}
        />
      </Col>
      <Col span={14}>{component}</Col>
    </Row>
  )
}

export default SpecialWrapper
