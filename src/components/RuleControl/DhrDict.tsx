import React from 'react'
import { TreeSelect as Dict } from '@galaxy/dict'
import config from '@/config'
import { cloneDeep } from 'lodash-es'
import { SpecialOption } from '@/types/package'

const { tenantId } = config

type DictProps = React.ComponentProps<typeof Dict>

type Props = {
  code: string
  scope?: string
  specialOption?: SpecialOption | null
} & DictProps

// EHR字典
function DhrDict({ code, scope = 'EHR', specialOption, ...restProps }: Props) {
  return (
    // @ts-ignore
    <Dict
      code={code}
      scope={scope}
      tenantId={tenantId}
      showSearch
      labelInValue
      multiple
      allowClear
      enhanceOptions={(option) => {
        let useOption = cloneDeep(option)

        // 如果是特殊条件，需要新增一个变更前的选项
        if (specialOption) {
          useOption = [specialOption].concat(useOption)
        }

        return useOption
      }}
      {...restProps}
    />
  )
}

export default DhrDict
