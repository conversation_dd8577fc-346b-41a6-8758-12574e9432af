import React, { memo, useEffect, useState } from 'react'
import { Select } from 'antd'
import { maxTagPlaceholder } from '@/utils/utils'
import { getPositionApi } from '@/api/basic-server'
import OrgsFilterSelect from '@/components/business/OrgsFilterSelect'
import DhrDict from './DhrDict'
import PageSelect from '../PageSelect'
import SpecialWrapper, { typeOptions } from './SpecialWrapper'

import styles from './style.module.less'

// 支持规则控件化
// 规则选择器属性（部门、实体岗位、基础岗位、字典项、审批角色）支持控件化选择
enum ControlType {
  interface, // 接口
  ehrDictionary, // 字典
  departmentTree, // 部门树
  contractDictionary, // 合同字典
}

export type ControlTypeKey = keyof typeof ControlType

type OuterValue = {
  type: any
  value: any
}

type IProps = {
  type?: ControlTypeKey
  code?: string // 字典
  value: OuterValue
  isSpecial?: boolean
  valueLabel?: string // 中文名
  onChange: any
  options: any[]
  [field: string]: any
}

const fixedValue = typeOptions.find((option) => option.label === '固定值')?.value

function RuleControl({
  type,
  code = '0',
  valueLabel,
  onChange,
  options,
  value: rawValue,
  ...restProps
}: IProps) {
  const { value: outerValue, type: outerSpecialType } = rawValue || {}
  const mutipleProps: any = {
    mode: 'multiple',
    multiple: true,
    maxTagCount: 'responsive',
    maxTagPlaceholder: maxTagPlaceholder(),
  }

  const [innerValue, setInnerValue] = useState<any>(outerValue || [])
  const [specialType, setSpecialType] = useState(outerSpecialType || null)

  const onSpecialTypeChange = (_, option) => {
    setSpecialType({
      value: option.value,
      label: option.title,
    })
  }

  // 根据条件类型生成对应的组件（接口、部门树、字典）
  const generateComponentWithType = ({ comType, ...rest }) => {
    switch (comType) {
      case 'interface':
        return (
          <PageSelect
            fieldNames={{
              value: 'id',
            }}
            maxLength={100}
            value={outerValue}
            apiFunction={getPositionApi}
            onChange={(v) => setInnerValue(v)}
            {...rest}
            {...mutipleProps}
          />
        )
      case 'ehrDictionary':
        return (
          <DhrDict
            onChange={(v) => setInnerValue(v)}
            code={code}
            scope="EHR"
            value={outerValue}
            {...rest}
            {...mutipleProps}
          />
        )
      case 'contractDictionary':
        return (
          <DhrDict
            onChange={(v) => setInnerValue(v)}
            code={code}
            scope="CONTRACT"
            value={outerValue}
            {...rest}
            {...mutipleProps}
          />
        )
      case 'departmentTree':
        return (
          <OrgsFilterSelect
            onChange={(v) => setInnerValue(v)}
            autoClearSearchValue={false}
            showSearch
            value={outerValue}
            {...rest}
            {...mutipleProps}
          />
        )
      default: {
        // 可能是xxxDictionary，split之后传递给scope,如果匹配不到则返回无匹配组件
        const scope = comType?.split('Dictionary')[0].toUpperCase()
        if (scope) {
          return (
            <DhrDict
              onChange={(v) => setInnerValue(v)}
              code={code}
              scope={scope}
              value={outerValue}
              {...rest}
              {...mutipleProps}
            />
          )
        }
        return <>无匹配组件可选择</>
      }
    }
  }

  // 动态组件
  const DynamicComponent = ({
    isSpecial,
    controlType,
    ...rest
  }: {
    controlType?: ControlTypeKey
    [field: string]: any
  }) => {
    let SecondCom = null as unknown as JSX.Element

    if (specialType?.value === '2') {
      // 业务赋值
      const useOptions = options.map((v) => ({
        label: v.leftDescription,
        value: v.leftName,
        ...v,
      }))
      SecondCom = (
        <Select
          options={useOptions}
          placeholder="请选择字段"
          value={innerValue}
          onChange={(v, option) => {
            setInnerValue({
              label: option.label,
              value: option.value,
              controlType: option.controlType,
              dictionaryCode: option.dictionaryCode,
            })
          }}
        />
      )
    } else {
      // 固定值
      SecondCom = generateComponentWithType({ comType: controlType, ...rest })
    }
    return (
      <SpecialWrapper
        value={outerSpecialType}
        onChange={(value, option) => {
          // 需要把之前的值清空，要不然会影响变化后的选择框
          // 固定值时为数组，业务赋值时为对象
          setInnerValue(value === fixedValue ? [] : null)
          onSpecialTypeChange(value, option)
        }}
        isSpecial={isSpecial}
        component={SecondCom}
      />
    )
  }

  useEffect(() => {
    if (!specialType && !innerValue) {
      return
    }

    onChange({
      type: specialType,
      value: innerValue,
    })
  }, [specialType, innerValue])

  return (
    <div className={styles.ruleControl}>
      {DynamicComponent({
        controlType: type,
        ...restProps,
        style: {
          width: 'calc(100% - 23px)', // 文本过长，长度溢出问题
        },
      })}
    </div>
  )
}

export default memo(RuleControl)
