import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, But<PERSON>, Tooltip, Upload } from 'antd'
import { PaperClipOutlined, DeleteOutlined, UploadOutlined } from '@ant-design/icons'
import { SchemaForm, Item, SchemaType } from '@amazebird/antd-schema-form'
import styles from './style.module.less'

interface IProps {
  visible: boolean
  close: () => void
  title?: React.ReactNode
  message?: string
  downloadFn: () => void
  onOk: (values) => Promise<any>
  [fieldName: string]: any
}

const fileValidator = async (_, file) => {
  if (file) {
    const suffix = file.name.slice((file.name as string).lastIndexOf('.') + 1)
    if (file.size > 2097152 || suffix !== 'xlsx') {
      return Promise.reject(new Error('仅支持文件格式为：.xlsx；最大支持上传2MB；'))
    }
    return Promise.resolve()
  }
  return Promise.resolve()
}

const uploadComponent = (props) => {
  const { value, onChange } = props
  return value ? (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <Tooltip title={value.name}>
        <Button
          type="link"
          style={{
            padding: 0,
            width: '100%',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <PaperClipOutlined />
          <span
            style={{
              width: 'calc(100% - 20px)',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
            }}
          >
            {value.name}
          </span>
        </Button>
      </Tooltip>
      <DeleteOutlined
        style={{
          color: 'red',
        }}
        onClick={() => {
          onChange(undefined)
        }}
      />
    </div>
  ) : (
    <>
      <Upload
        listType="text"
        showUploadList={false}
        customRequest={(options) => {
          onChange(options.file)
        }}
      >
        <Button icon={<UploadOutlined />}>上传文件</Button>
      </Upload>
      <div
        style={{
          whiteSpace: 'nowrap',
          marginTop: 8,
          color: '#c2c2c2',
        }}
      >
        仅支持文件格式为：.xlsx；最大支持上传2MB；
      </div>
    </>
  )
}

const UploadModal = (props: IProps) => {
  const { visible, close, title = '导入数据', message, downloadFn, onOk: okClick } = props
  const form = SchemaForm.createForm()
  const [loading, setLoading] = useState(false)

  const schema: SchemaType = {
    module: {
      label: '下载模板',
      component: () => (
        <Button
          type="link"
          style={{
            padding: 0,
          }}
          onClick={downloadFn}
        >
          {`${title}模板.xlsx`}
        </Button>
      ),
    },
    file: {
      label: '上传文件',
      component: uploadComponent,
      rules: [
        {
          required: true,
          message: '请上传文件',
        },
        {
          validator: fileValidator,
        },
      ],
    },
  }

  const onOk = async () => {
    try {
      const values = await form?.validateFields()
      setLoading(true)
      okClick(values)
        .then(() => {
          close()
        })
        .finally(() => {
          setLoading(false)
        })
    } catch (error) {
      console.error(error)
    }
  }

  return (
    <Modal
      title={title}
      visible={visible}
      onCancel={close}
      onOk={onOk}
      width={650}
      okButtonProps={{
        loading,
      }}
      wrapClassName={styles.modalContent}
    >
      {message && (
        <Alert
          message={message}
          type="info"
          showIcon
          style={{
            marginBottom: 24,
          }}
        />
      )}
      <SchemaForm form={form} schema={schema}>
        <Item field="module" />
        <Item field="file" />
      </SchemaForm>
    </Modal>
  )
}

export default UploadModal
