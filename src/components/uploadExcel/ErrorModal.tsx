import React from 'react'
import { Modal } from 'antd'
import { SchemaTable } from '@amazebird/antd-schema-table'

import styles from './style.module.less'

interface IProps {
  width?: number
  visible: boolean
  errorText: string
  dataSource: any[]
  handleClose: () => void
}

const UploadErrorModal: React.FC<IProps> = ({
  width = 500,
  visible,
  dataSource,
  handleClose,
  errorText,
}) => {
  const columns = [
    {
      title: '序号',
      width: 10,
      key: 'index',
      cell: ({ record }) => record.index + 1,
    },
    {
      title: '错误信息',
      dataIndex: 'failMessage',
      formComponent: 'Input',
      cell: 'Text',
      hideInSearch: true,
    },
    {
      title: '错误字段',
      width: 20,
      dataIndex: 'failField',
      formComponent: 'Input',
      cell: 'Text',
      hideInSearch: true,
    },
  ]

  return (
    <Modal
      visible={visible}
      className={styles.errorModal}
      title="导入失败"
      width={width}
      okText="知道了"
      onCancel={handleClose}
      onOk={handleClose}
    >
      <p className={styles.tip}>{errorText}</p>
      <SchemaTable
        rowKey="failMessage"
        stripe={false}
        tableClassName="custom-table-class-name"
        dataSource={dataSource}
        columns={columns}
        pagination={{
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total) => `总共 ${total} 个项目`,
        }}
        footer={() => null}
      />
    </Modal>
  )
}

export default UploadErrorModal
