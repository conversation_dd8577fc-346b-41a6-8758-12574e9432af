import React, { useState } from 'react'
import { Button } from 'antd'
import { CheckCircleFilled } from '@ant-design/icons'
import modalWrapperHoc from '@/components/modalWrapperHoc'
import { millisecondsToDate } from '@/utils/utils'
import UploadModal from './uploadModal'
import UploadErrorModal from './ErrorModal'
import UploadSuccessModal from './SuccessModal'

/**
 * title modal的标题
 * downloadFn 下载模板调用函数
 * message Alert的提示信息
 * uploadFn 确定导入函数
 */

interface IProps {
  children?: React.ReactNode
  downloadFn: () => void
  title?: React.ReactNode
  message?: React.ReactNode
  uploadFn: (values) => Promise<any>
}

const UploadExcel = (props: IProps) => {
  const { children, downloadFn, title, message, uploadFn } = props
  const [tip, setTip] = useState('')
  const [errorModalVisible, setErrorModalVisible] = useState(false)
  const [successModalVisible, setSuccessModalVisible] = useState(false)
  const [errorList, setErrorList] = useState([])

  const showModal = () => {
    modalWrapperHoc(UploadModal)({
      onOk: (values: any) =>
        new Promise((resolve, reject) => {
          try {
            const formData = new FormData()
            formData.append('file', values.file)
            uploadFn({
              formData,
            }).then(({ data: { data: result } }) => {
              const { isSuccess, importFailDetails, number, cost } = result
              if (isSuccess) {
                setSuccessModalVisible(true)
                resolve(true)
              } else {
                resolve(true)
                setErrorModalVisible(true)
                setErrorList(
                  importFailDetails.map((detail, index) => ({
                    ...detail,
                    index,
                  })),
                )
              }
              setTip(`导入${isSuccess ? '' : '失败'}${number}条，用时：${millisecondsToDate(cost)}`)
            })
          } finally {
            reject()
          }
        }),
      message,
      downloadFn,
      title,
    })
  }
  return (
    <>
      <Button type="primary" onClick={showModal}>
        {children || '导入数据'}
      </Button>
      {/* 导入失败弹窗 */}
      <UploadErrorModal
        errorText={tip}
        visible={errorModalVisible}
        dataSource={errorList}
        handleClose={() => {
          setErrorModalVisible(false)
        }}
      />
      {/* 导入成功弹窗 */}
      <UploadSuccessModal
        visible={successModalVisible}
        titleText="导入成功"
        width={400}
        closable={false}
        titleIcon={
          <CheckCircleFilled
            style={{
              fontSize: '28px',
              color: '#52C41A',
              marginRight: 8,
            }}
          />
        }
        footer={
          <div
            style={{
              textAlign: 'right',
              marginTop: '20px',
            }}
          >
            <Button
              type="primary"
              onClick={() => {
                setSuccessModalVisible(false)
              }}
            >
              知道了
            </Button>
          </div>
        }
      >
        {tip}
      </UploadSuccessModal>
    </>
  )
}

export default UploadExcel
