import React from 'react'
import { useStore } from '@/stores'
import { Button } from 'antd'
import { removeTraffic } from '@/utils/traffic'

const ExtraWrap = () => {
  const store = useStore((state) => state)
  const { extra, showTrafficBtn, setTrafficBtn } = store
  const removeTrafficClick = () => {
    try {
      removeTraffic()
    } finally {
      setTrafficBtn(false)
    }
  }
  return (
    <>
      {showTrafficBtn && (
        <Button
          type="link"
          style={{
            position: 'fixed',
            top: '8px',
            right: '500px',
            zIndex: '99',
            color: '#ffb400',
          }}
          onClick={removeTrafficClick}
        >
          目前为测试租户，点击此处切换为正式租户
        </Button>
      )}
      {extra}
    </>
  )
}

export default ExtraWrap
