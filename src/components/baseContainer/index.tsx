import React, { useEffect } from 'react'
import { useStore } from '@/stores'
import Header from './Header'
import Content from './Content'

interface IProps {
  children: any
  title?: string
  needBack?: boolean
}

const BaseContainer: React.FC<IProps> = function ({ children, title, needBack }: IProps) {
  const store = useStore((state) => state)
  const { setTitle, setBack } = store
  useEffect(() => {
    setTitle(title)
    setBack(needBack)
  }, [title, needBack])

  return (
    <>
      <Header />
      <Content>{children}</Content>
    </>
  )
}
export default BaseContainer
