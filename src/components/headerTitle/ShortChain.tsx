import React from 'react'
import { Select, InputNumber } from 'antd'
import { TreeSelect as Dict } from '@galaxy/dict'
import { isNil } from 'lodash-es'

// 短链有效时长
function ShortChain(props: any) {
  const { value = {}, onChange, isView, disabled, isShowDay = false, options, ...restProps } = props
  const { type, day } = value
  const setType = (_type: string) => {
    onChange({
      ...value,
      type: _type,
      day: null,
    })
  }

  const setDay = (_day: number | null) => {
    onChange({
      ...value,
      day: _day,
    })
  }

  if (isView) {
    return `${type || '--'}${isNil(day) ? '--' : `${day}天`}`
  }

  return (
    <div
      style={{
        display: 'flex',
      }}
    >
      <Dict
        value={type}
        {...restProps}
        allowClear
        disabled
        onChange={setType}
        style={{
          minWidth: '200px',
        }}
      />

      {isShowDay ? (
        <InputNumber
          value={day}
          disabled={disabled}
          addonAfter="天"
          controls={false}
          precision={0}
          min={1}
          max={7}
          onChange={setDay}
          style={{
            width: '100px',
            flexShrink: 0,
            marginLeft: 10,
          }}
        />
      ) : (
        <Select
          options={options.map((i) => ({ label: i.label, value: i.value }))}
          style={{
            minWidth: '100px',
            marginLeft: 8,
          }}
          value={day}
          disabled={disabled}
          onChange={setDay}
        />
      )}
    </div>
  )
}

export default ShortChain
