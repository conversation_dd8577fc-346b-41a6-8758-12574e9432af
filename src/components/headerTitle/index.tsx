import React, { CSSProperties } from 'react'
import { Divider, Typography, Tooltip } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'

interface IHeaderProp {
  title: string
  toolTipText?: string
  marginLeft?: string
  extraStyle?: CSSProperties
}

// 头部标题
function HeaderTitle({ title, toolTipText, marginLeft = '30px', extraStyle }: IHeaderProp) {
  return (
    <>
      <Typography.Title
        level={5}
        style={{
          margin: `0 0 0 ${marginLeft}`,
          fontWeight: 'normal',
          ...extraStyle,
        }}
      >
        {title}
        {toolTipText && (
          <Tooltip title={toolTipText}>
            <QuestionCircleOutlined
              style={{
                marginLeft: '10px',
                cursor: 'help',
              }}
            />
          </Tooltip>
        )}
      </Typography.Title>
      <Divider
        style={{
          margin: '10px 0',
        }}
      />
    </>
  )
}

type TitleProps = {
  title: string
  toolTipText?: string
  style?: CSSProperties
  isDivider?: boolean
  extra?: JSX.Element | null
}

function Title({ title, toolTipText, extra, style, isDivider = true }: TitleProps) {
  return (
    <>
      <h3 style={style}>
        {title}
        {toolTipText && (
          <Tooltip title={toolTipText}>
            <QuestionCircleOutlined
              style={{
                marginLeft: '10px',
                cursor: 'help',
              }}
            />
          </Tooltip>
        )}
        <div
          style={{
            position: 'absolute',
            right: '50px',
            top: '50%',
            transform: 'translateY(-50%)',
          }}
        >
          {extra}
        </div>
      </h3>
      {isDivider && <Divider />}
    </>
  )
}

export { Title }

export default HeaderTitle
