import React, { useState } from 'react'
import { Typography } from 'antd'
import { DownOutlined, UpOutlined } from '@ant-design/icons'

const { Paragraph } = Typography

interface TextProps {
  text: string
  rows?: number
}

const FieldContainer: React.FC<any> = ({ text, rows = 5 }: TextProps) => {
  const [expand, setExpand] = useState(false)
  const Expand = (
    <span
      onClick={(e) => {
        e.stopPropagation()
        setExpand(true)
      }}
    >
      <DownOutlined />
      展开
    </span>
  )
  return (
    <Paragraph
      ellipsis={
        expand
          ? false
          : {
              rows,
              expandable: true,
              symbol: Expand,
            }
      }
    >
      {text}
      {expand && (
        <a onClick={() => setExpand(false)}>
          <UpOutlined />
          收起
        </a>
      )}
    </Paragraph>
  )
}

export default FieldContainer
