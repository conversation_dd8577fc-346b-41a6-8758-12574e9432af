import React, { memo, useEffect, useState } from 'react'
import { Select, SelectProps, Tooltip } from 'antd'
import { SpecialOption } from '@/types/package'

type SelectType = {
  specialOption?: SpecialOption | null
  apiFunction: () => Promise<any> // TODO，多个参数类型定义
} & SelectProps

type OptionProps = {
  label: string
  value: number
  tooltip: string
}

function PageSelect({ apiFunction, value, specialOption, onChange, ...restProps }: SelectType) {
  const [list, setList] = useState<OptionProps[]>([])
  const [loading, setLoading] = useState(false)

  const getList = async () => {
    setLoading(true)
    try {
      const { data } = await apiFunction()

      const result = data?.data ?? []
      // 转换参数
      let newList = result.map((v) => ({
        ...v,
        value: v.id,
        title: v.name,
        label: <Tooltip title={v.fullName}>{v.name}</Tooltip>,
      }))

      // 如果是特殊条件，需要新增一个变更前的选项
      if (specialOption) {
        const useSpecialOption = {
          ...specialOption,
          id: specialOption?.value,
        }
        newList = [useSpecialOption].concat(newList)
      }
      setList(newList)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getList()
  }, [])

  return (
    <Select
      showSearch
      allowClear
      labelInValue
      loading={loading}
      filterOption={(search, option) => (option?.name as string)?.indexOf(search) !== -1}
      {...restProps}
      onChange={(v, option) => {
        const newValue = (v || []).map((item) => ({
          ...item,
          label: item.title,
        }))

        onChange?.(newValue, option)
      }}
      value={value}
      options={list}
    />
  )
}

export default memo(PageSelect)
