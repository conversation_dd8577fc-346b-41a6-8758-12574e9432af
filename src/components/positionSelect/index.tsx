import React, { useEffect, useState } from 'react'
import { Select } from 'antd'
import type { SelectProps } from 'antd'
import { getPositionApi } from '@/api/basic-server'

const PositionSelect = (props: SelectProps) => {
  const { value, onChange, ...rest } = props
  const [options, setOptions] = useState([])

  useEffect(() => {
    getPositionApi().then(({ data: resData }) => {
      if (resData?.data) {
        setOptions(
          resData.data.map((item) => ({
            value: item.id,
            label: item.name,
          })),
        )
      }
    })
  }, [])
  return (
    <Select
      placeholder="请选择岗位"
      value={value}
      onChange={onChange}
      {...rest}
      options={options}
      optionFilterProp="label"
      showSearch
      allowClear
    />
  )
}

export default PositionSelect
