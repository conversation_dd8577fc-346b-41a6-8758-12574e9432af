import React, { useState, useEffect } from 'react'
import { getPermission } from '@galaxy/rbac'
import { omit } from 'lodash-es'
import { isLocalNetwork } from '@/utils/utils'
import { DownOutlined } from '@ant-design/icons'
import { Dropdown, DropDownProps, Menu, Tooltip, Button, ButtonProps } from 'antd'
import type { MenuClickEventHandler } from 'rc-menu/es/interface'
import classNames from 'classnames'
import styles from './style.module.less'

export type PermissionItem = {
  code: string
  hasPermission: boolean
}

export interface ActionProps {
  code: string
  component: React.ReactNode | ((permission?: PermissionItem) => React.ReactNode)
  danger?: boolean
  disabled?: boolean
  /** 点击当前action触发对应的操作 */
  onClick?: MenuClickEventHandler
  /** 没有权限的时候不显示（默认为显示为禁用状态） */
  hideDisabled?: boolean
  tooltip?: string | ((permission?: PermissionItem) => string)
}

export interface PermissionDropdownProps extends Omit<DropDownProps, 'overlay'> {
  permissions?: PermissionItem[]
  actions: ActionProps[]
  onClick?: MenuClickEventHandler
  showIcon?: boolean
  /** 下拉菜单中的普通按钮显示为朴朴绿以及字体大小为12px */
  inTableStyle?: boolean
  // 是否业务权限控制显示隐藏
  workPermissionControl?: boolean
  // 是否uc权限控制显示隐藏
  ucPermissionControl?: boolean
  // 按钮
  btnProps?: ButtonProps
}

/**
 * 表格中下拉操作框
 * @param permissions 直接将 record 中的 permissions 属性传入
 * @param actions 有哪些操作项
 * @param onClick 点击操作项的回调
 * @param showIcon 是否显示下拉箭头图标
 */
const PermissionDropdown: React.FC<PermissionDropdownProps> = (props) => {
  const [menu, setMenu] = useState<any[]>([])
  const {
    permissions,
    actions,
    onClick,
    children,
    showIcon = true,
    inTableStyle = true,
    workPermissionControl = true,
    ucPermissionControl = true,
    btnProps = {
      type: 'link',
    },
    ...restProps
  } = props

  const prefixCls = 'permissionDropdown'
  const getMenu = () =>
    // 线上独立访问的时候始终不走uc权限
    // eslint-disable-next-line implicit-arrow-linebreak
    actions
      .filter(
        (action) =>
          getPermission(action.code) ||
          (!isLocalNetwork() && !window.__POWERED_BY_QIANKUN__) ||
          !ucPermissionControl,
      ) // uc-rbac 没有权限的直接不显示
      .map((action) => {
        const { component, tooltip } = action

        if (permissions) {
          // 受权限控制
          const permission = permissions.find((item) => item.code === action.code)

          const label = typeof component === 'function' ? component(permission) : component

          return {
            key: action?.code,
            label: tooltip ? (
              <Tooltip
                placement="topRight"
                title={typeof tooltip === 'function' ? tooltip(permission) : tooltip}
              >
                {label}
              </Tooltip>
            ) : (
              label
            ),
            disabled: !permission?.hasPermission,
            ...action,
          }
        }
        return {
          key: action?.code,
          label: typeof component === 'function' ? component() : component,
          disabled: workPermissionControl,
          ...action,
        }
      })
      .filter((action) => !(action?.disabled && action?.hideDisabled)) // 过滤掉没有权限下设置为不显示的字段
      .map((action) => omit(action, ['hideDisabled']))

  useEffect(() => {
    // TODO: 发起请求去获取权限
    setMenu(getMenu())
  }, [permissions, actions])

  // 如果没有菜单的时候不显示更多'按钮'
  if (!menu || menu?.length === 0) {
    return null
  }

  return (
    <Dropdown
      trigger={['click']}
      className={styles[prefixCls]}
      // loading={loading}
      overlay={
        <Menu
          onClick={onClick}
          items={menu}
          className={classNames(styles.disabledColor, {
            [`${styles.primaryColorMenu}`]: inTableStyle,
          })}
        />
      }
      {...restProps}
    >
      <Button size="small" {...btnProps}>
        {children}
        {showIcon && (
          <DownOutlined
            style={{
              marginLeft: '2px',
              verticalAlign: 'middle',
            }}
          />
        )}
      </Button>
    </Dropdown>
  )
}

export default PermissionDropdown
