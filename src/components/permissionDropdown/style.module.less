@import '@/themes/theme.less';

.primaryColorMenu {
  :global {
    .@{ant-prefix}-dropdown-menu-item {
      font-size: 12px;
      color: @primary-color;
    }

    .@{ant-prefix}-dropdown-menu-title-content {
      text-align: center;
    }
  }
}

.disabledColor {
  :global {

    // 禁用的颜色大于disabled的颜色
    .@{ant-prefix}-dropdown-menu-item.@{ant-prefix}-dropdown-menu-item-disabled {
      color: @disabled-color;

      &:hover {
        background-color: transparent;
      }
    }
  }
}
