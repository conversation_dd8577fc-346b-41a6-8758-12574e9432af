import React, { useCallback } from 'react'
import { TreeSelect as OrgSelector } from '@galaxy/org-selector'

type OrgSelectorProps = React.ComponentProps<typeof OrgSelector>

type OrgProps = {
  isFilterVirtualDep?: boolean // 是否过滤虚拟组织
} & OrgSelectorProps

const OrgsFilterSelect: React.FC<OrgProps> = ({ isFilterVirtualDep = true, ...restProps }) => {
  /**
   * TODO 根据产品需求过滤的部门名称  暂时写死
   * id、code 各个环境不一致  不适合做唯一标识
   * 如果过滤的部门名称名字改了，可能意义也不一样，目前用作标识比较合适
   */
  // const filterName = ['总经办', '朴朴意见箱', '朴朴投稿箱', '员工咨询窗口', '朴心在线']

  const onFilter = useCallback((data) => {
    if (isFilterVirtualDep) {
      return !data.isVirtualDep
    }
    return true
  }, [])

  return <OrgSelector {...restProps} filter={onFilter} />
}

export default OrgsFilterSelect
