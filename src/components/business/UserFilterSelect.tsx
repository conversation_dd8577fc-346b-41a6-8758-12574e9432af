import React from 'react'
import { TreeSelect as UserSelector } from '@galaxy/user-selector'

type OrgSelectorProps = React.ComponentProps<typeof UserSelector>

type IProps = {
  filterUsers?: Array<any>
  isThirdparty?: boolean
  isFilterInner?: boolean
  isFilterVirtualDep?: boolean // 是否过滤虚拟组织
} & OrgSelectorProps

const UserFilterSelect: React.FC<IProps> = (props) => {
  const {
    filterUsers = [],
    isFilterVirtualDep = true,
    isFilterInner = true,
    isThirdparty,
    ...rest
  } = props

  // TODO: 存在两个下拉组件，需要互相监听时，树下拉的同一个节点已经打开了，就不会重新触发数据
  // 合同包：人员管理的排除人员和指定人员
  const onFilter = (data) => {
    if (filterUsers.includes(data.value)) {
      // eslint-disable-next-line no-param-reassign
      data = {
        ...data,
        disabled: true,
      }
    }

    let isPass = true

    if (isFilterVirtualDep) {
      isPass = !data.source?.isVirtualDep
    }

    if (isThirdparty && data.employmentForm) {
      /**
       * 自营第三方 - 5
       */
      isPass = data.employmentForm === '5'
    }

    if (isFilterInner && data.employmentForm) {
      /**
       * 全职 - 1，全职 - 2，实习 - 3
       * 内部保留以上三个
       */
      isPass = ['1', '2', '3'].includes(data.employmentForm)
    }
    return isPass
  }

  return <UserSelector {...rest} filter={onFilter} />
}

export default UserFilterSelect
