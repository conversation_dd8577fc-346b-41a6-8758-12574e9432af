import React from 'react'
import { getPermission } from '@galaxy/rbac'
import { find, isPlainObject } from 'lodash-es'
import { Tooltip, TooltipProps } from 'antd'
import { Delete } from '@amazebird/antd-schema-table'
import { isLocalNetwork } from '@/utils/utils'
import type { PermissionItem } from '../permissionDropdown'

type TooltipType = { isShowTip: boolean } & TooltipProps
export interface IProps {
  children: React.ReactElement
  code: string
  permissions: PermissionItem[]
  disabledAction?: boolean
  tooltip?: React.ReactNode | TooltipType
  ucPermissionControl?: boolean
}

export const getCodePermission = (code, permission) =>
  find(permission, ['code', code])?.hasPermission === true

/**
 * permissions: [{ code: 'editProject', hasPermission: true }], 直接将 record 中的 permissions 属性传入
 * code: 对应操作项的code
 * disabledAction: 没有权限时是否显示操作项，显示的话会默认给元素增加 disabled 属性
 * ucPermissionControl 是否走uc权限控制
 */
const PermissionAction: React.FC<IProps> = function (props) {
  const {
    code,
    permissions,
    children,
    disabledAction = true,
    tooltip,
    ucPermissionControl = true,
  } = props
  // children 有可能里面使用了条件语句导致是空的
  // 或者 uc-rbac 没有权限的直接不显示, 并且要非独立访问的环境下
  if (
    (!children || (ucPermissionControl && !getPermission(code))) &&
    (isLocalNetwork() || window.__POWERED_BY_QIANKUN__)
  ) {
    return null
  }

  const TooltipWrapper = (component: React.ReactNode, isDisabled?: boolean) => {
    if (isPlainObject(tooltip)) {
      // isShow 任何状态都有tooltip
      if (isDisabled || (tooltip as TooltipType)?.isShowTip) {
        return <Tooltip {...(tooltip as TooltipProps)}>{component}</Tooltip>
      }
      return component
    }
    return <Tooltip title={tooltip}>{component}</Tooltip>
  }

  // 判断子组件的类型为Delete,那么会默认禁用时不再展示confirm提示，后续需要可增加参数
  const childProps =
    React.isValidElement(children) && children.type === Delete
      ? {
          disabled: true,
          showConfirm: false, // 默认禁用时Delete不展示confirm提示
        }
      : {
          disabled: true,
        }

  const node = React.cloneElement(children, childProps)

  if (!getCodePermission(code, permissions)) {
    if (!disabledAction) {
      return null
    }
    return tooltip ? TooltipWrapper(node, true) : node
  }

  return tooltip ? TooltipWrapper(children) : children
}

export default PermissionAction
