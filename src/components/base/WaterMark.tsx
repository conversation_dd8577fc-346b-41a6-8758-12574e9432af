import React, { useMemo } from 'react'
import { WaterMark as AntdWaterMark } from '@ant-design/pro-layout'
import userService from '@/mainApp/services/userService'
import config from '@/config'
import { formatDate } from '@/utils/utils'

const WaterMark = (props: any) => {
  const { children } = props

  const watermark = useMemo(() => {
    let result = `${userService.userInfo.userNumber}-${userService.userInfo.username}-${formatDate(
      new Date(),
      '{y}.{m}.{d} {h}:{i}',
    )}`
    if (config.environment !== 'prod') {
      result += `-${config.environment}`
    }
    return result
  }, [])

  return (
    <AntdWaterMark content={watermark} fontColor="rgba(0,0,0,.1)">
      {children}
    </AntdWaterMark>
  )
}

export default WaterMark
