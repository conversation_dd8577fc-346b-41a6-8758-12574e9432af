import React from 'react'
import { Descriptions } from 'antd'
import classnames from 'classnames'

import styles from './style.module.less'

interface Item {
  label: string
  value: string | number
}

interface IProps {
  /** 数据来源 */
  data: Item[]
  isSpecial?: boolean
  /** 标题width */
  labelWidth?: number
  [field: string]: any
}

const DescriptionsWrap = (props: IProps) => {
  const { data, isSpecial = false, labelWidth = 100, ...rest } = props
  return (
    <Descriptions
      {...rest}
      labelStyle={{
        color: '#8C8C8C',
        width: labelWidth,
        display: 'flex',
        justifyContent: 'end',
      }}
      className={classnames({
        [styles.special]: isSpecial,
      })}
    >
      {data.map((item) => (
        <Descriptions.Item label={item.label} key={item.value}>
          {item.value || '--'}
        </Descriptions.Item>
      ))}
    </Descriptions>
  )
}

export default DescriptionsWrap
