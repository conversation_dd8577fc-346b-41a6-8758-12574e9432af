import React from 'react'
import { createRoot, Root } from 'react-dom/client'
import { ConfigProvider, ModalProps } from 'antd'
import zhCN from 'antd/lib/locale/zh_CN'

// 直接用 ReactDOM.render 挂载国际化无法作用到
const localeWrapper = (Component) => (
  <ConfigProvider locale={zhCN} prefixCls="contract">
    {Component}
  </ConfigProvider>
)

type ModalType = {
  isRedirect?: boolean
} & Omit<ModalProps, 'close' | 'visible'> &
  Record<string, any>
/**
 * 封装 Modal 组件，提供一些默认的关闭操作
 * 从antd Modal.confirm 截取：https://github.com/ant-design/ant-design/blob/master/components/modal/confirm.tsx
 * @param component 弹窗组件， 会默认给组件传入 visible 和 close, visible 可以设置弹窗是否显示，close 可以设置关闭弹窗的方法
 */
const modalWrapperHoc = (component) => {
  const container = document.createDocumentFragment()
  let root: Root | null = null
  const destroy = () => {
    // 删除挂载的React组件
    root?.unmount()
    root = null
  }
  // 渲染组件
  const render = (restProps?: ModalType) => {
    // 将modal组件内容挂载到 fragment上,添加一些close,visible公共的属性
    const comModalVDom = localeWrapper(
      React.createElement(component, {
        ...restProps,
        close: destroy,
        visible: true,
      }),
    )
    // 将统一处理添加close和visible的属性，挂载在container上
    // ReactDOM.render(comModalVDom, container)
    root = createRoot(container)
    root.render(comModalVDom)
    if (restProps?.isRedirect) {
      // TODO 实现不了，有时间研究
      // const parent = document.querySelector('#microRoot')
      // ReactDOM.createPortal(comModalVDom, parent)
    }
  }

  return (restProps?: ModalType) => render(restProps)
}

// 这里的主要目的就是，统一处理了下modal框自己的显示和隐藏。
// 其他的modal需要的数据或者属性，利用函数调用的方式，通过restProps传入
export default modalWrapperHoc
