import React, { createContext, useRef, useContext } from 'react'
import { create, useStore as useStoreInCreate } from 'zustand'
import { StoreProps, StoreActionProps } from '../common/types'

type StoreKeys = keyof StoreProps

type MergeProps = Record<StoreKeys, any>

export const createStore = () =>
  create<StoreProps & StoreActionProps>()((set) => {
    // 单个更新
    const updateStateSector = (key: StoreKeys, value) => {
      /**
       * 有时候多次触发更新只会更新最后一次执行的内容
       *
       * 因此可以传入方法通过 set 的参数拿到最新的值再执行
       */
      if (typeof value === 'function') {
        set((state) => ({ [key]: value(state) }))
      } else {
        set(() => ({ [key]: value }))
      }
    }

    // 多个更新
    const updateStateWithMergeSector = (value: MergeProps) => set(() => value)

    return {
      tabsKey: '常用变量',
      file: {} as any,
      boxes: [],
      content: [],
      search: ['', '', ''],
      linePosition: {
        page: 0,
        position: 0,
      },
      fontParams: {
        fontSize: null,
        fontType: null,
      },
      highlightKey: '',
      activeNode: null,
      isScroll: false,
      readOnly: false,
      sealCode: '',
      signType: '',
      locateMode: '',
      scrollContainer: null,
      pageHeight: 0,
      updateState: updateStateSector,
      updateStateWithMerge: updateStateWithMergeSector,
    }
  })

export type StoreContextProps = ReturnType<typeof createStore>

export const StoreContext = createContext<StoreContextProps | null>(null)

export const Provider = ({ children }: { children: React.ReactNode }) => {
  const componentStoreRef = useRef<StoreContextProps>()
  if (!componentStoreRef.current) {
    componentStoreRef.current = createStore()
  }
  return <StoreContext.Provider value={componentStoreRef.current}>{children}</StoreContext.Provider>
}

// 为了在使用类似 state => state.xxx 的 sector 时有类型提示，所以需要该类型
type UseCreateStore = () => (sector: (state: StoreProps & StoreActionProps) => any) => any

/**
 * 创建一个类似 zustand 原生 useStore 能力的函数
 */
export const useCreateStore: UseCreateStore = () => {
  const storeApi = useContext(StoreContext)

  if (!storeApi) {
    throw new Error('Missing TestContext.Provider in the tree')
  }
  return (sector) => useStoreInCreate(storeApi, sector)
}
