import React, { useState } from 'react'
import { useDrag } from 'react-dnd'
import { DRAG_BOX, VAR_TYPE } from '../common/constant'
import { useDragLine } from './common'

import styles from './style.module.less'

interface PositionProps {
  initX?: number
  initY?: number
  screenX?: number
  screenY?: number
}

interface IProps {
  code: string
  name: string
}

/**
 * 合同管理中存放变量和印章的盒子
 */
const VarBox = function Box({ name, code }: IProps) {
  const [position, setPosition] = useState<PositionProps>({
    initX: 0,
    initY: 0,
    screenX: 0,
    screenY: 0,
  })

  const [{ isDragging }, drag] = useDrag(
    () => ({
      type: DRAG_BOX,
      item: {
        code,
        name,
        top: position.initY,
        left: position.initX,
        screenX: position.screenX,
        screenY: position.screenY,
        source: VAR_TYPE.NORMAL_BOX,
      },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
        handlerId: monitor.getHandlerId(),
      }),
    }),
    [position],
  )

  const { onMouseDown: onDragMouseDown, ...restDragLineProps } = useDragLine({ isDragging })

  const opacity = isDragging ? 0.4 : 1

  return (
    <div
      ref={drag}
      className={styles.varBox}
      style={{ opacity }}
      data-testid="box"
      onMouseDown={(e) =>
        // 用鼠标进行拖拽选择时，需要记录鼠标是从哪个位置开始点击拖拽的
        onDragMouseDown(e, () => {
          const { nativeEvent } = e
          const { offsetX, offsetY } = nativeEvent
          setPosition({
            ...position,
            initX: -offsetX,
            // 边框包含 2px 偏移，导致位置会稍微偏上
            initY: -offsetY - 2,
          })
        })
      }
      {...restDragLineProps}
    >
      {name}
    </div>
  )
}

export default VarBox
