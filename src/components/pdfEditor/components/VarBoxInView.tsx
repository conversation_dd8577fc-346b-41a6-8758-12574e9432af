import React, { CSSProperties, useEffect, useMemo, useRef, useState } from 'react'
import { CloseCircleOutlined } from '@ant-design/icons'

import { fontFamiltMap } from '@/constants/template'

import { isNil } from 'lodash-es'
import { useDrag } from 'react-dnd'
import classNames from 'classnames'
import { Resizable } from 're-resizable'
import update from 'immutability-helper'

import { useCreateStore } from '../context'
import { adjustBoxParams, useDragLine } from './common'
import { DRAG_BOX, INIT_FONT_SIZE } from '../common/constant'

import styles from './style.module.less'

interface IProps {
  code: string
  index: number
  top: number
  page: number
  left: number
  style?: CSSProperties
  hideSourceOnDrag?: boolean
  children: React.ReactNode
  readOnly?: boolean
  move: boolean
  width: number
  height: number
  fontNum: number
  initHeight: number
  initWidth: number
  fontNumChange?: boolean
}

/**
 * pdf上可拖拽的变量
 */
const VarBoxInView = (props: IProps) => {
  const {
    code,
    index,
    left,
    top,
    page,
    style,
    children,
    hideSourceOnDrag = true,
    readOnly,
    move,
    width,
    height,
    initWidth,
    initHeight,
    fontNum,
    fontNumChange,
  } = props

  const useStore = useCreateStore()
  const ResizableComponent: any = Resizable

  const boxes = useStore((state) => state.boxes)
  const isScroll = useStore((state) => state.isScroll)
  const highlightKey = useStore((state) => state.highlightKey)
  const handleUpdateState = useStore((state) => state.updateState)
  const fontParams = useStore((state) => state.fontParams)
  const innerContainer = useRef<any>(null)
  const resizeContainer = useRef<any>(null)

  // re-resizable 开启禁用需要传以下一整个对象，不能只传一个 false
  // 见 https://github.com/bokuweb/re-resizable#enable-enable
  const resizeDisabled = {
    top: false,
    right: false,
    bottom: false,
    left: false,
    topRight: false,
    bottomRight: false,
    bottomLeft: false,
    topLeft: false,
  }

  const resizeEnable = {
    bottomRight: true,
    right: true,
    bottom: true,
  }

  const [boxSize, setBoxSize] = useState({
    height,
    width,
  })
  const [initBoxSize, setInitBoxSize] = useState({
    initHeight: initHeight || 0,
    initWidth: initWidth || 0,
  })

  const [{ isDragging }, drag] = useDrag(
    () => ({
      type: DRAG_BOX,
      item: {
        index,
        left,
        top,
        page,
      },
      hideSourceOnDrag: true,
      canDrag: !readOnly,
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    }),
    [index, left, top, page],
  )

  const dragLineProps = useDragLine({ isDragging })

  const inDragging = useMemo(() => isDragging && hideSourceOnDrag, [hideSourceOnDrag, isDragging])

  // 更新变量盒子属性
  const updateBoxParams = (w?, h?, change?) => {
    handleUpdateState('boxes', (state) => {
      const newPdfBoxes = adjustBoxParams({
        params: {
          ...props,
          ...initBoxSize,
        },
        width: w,
        height: h,
        fontNumChange: change,
        boxes: state.boxes,
        node: innerContainer.current,
      })

      return newPdfBoxes
    })
  }

  // 变量盒子大小改变
  const handleBoxResize = (w, h) => updateBoxParams(w, h)

  /**
   * 通过填充字数改变盒子宽度
   */
  const changeBoxWidth = (num) => {
    const node = resizeContainer.current.resizable
    if (!node) {
      return
    }
    const fontSize = Number(window.getComputedStyle(node).fontSize.replace('px', ''))
    const adjustWidth = (num + 1) * fontSize
    updateBoxParams(adjustWidth, null, false)
  }

  /**
   * 字体相关参数改变函数
   */
  const handleChangeFontInComponent = (params) => {
    const { fontSize, fontType } = params
    const node = resizeContainer.current.resizable

    const originStyle = node?.getAttribute('style')

    node?.setAttribute(
      'style',
      // 需要将 默认变量字体大小 按比例进行换算 scale 来转换
      `${originStyle}${
        fontSize
          ? `transform:scale(${fontSize / parseFloat(INIT_FONT_SIZE)});transform-origin: top left;`
          : ''
      }
      ${!isNil(fontType) ? `font-family:${fontFamiltMap[fontType]};` : ''}`,
    )
  }

  const onMouseEvent = (type?) => {
    if (type === 'mouseover') {
      handleUpdateState('activeNode', { index, code })
    }

    if (type === 'mouseout') {
      handleUpdateState('activeNode', null)
    }
  }

  useEffect(() => {
    if (fontNum && fontNumChange) {
      changeBoxWidth(fontNum)
    }
  }, [fontNum, fontNumChange])

  useEffect(() => {
    if (!inDragging) {
      updateBoxParams()
    }
  }, [move, inDragging])

  useEffect(() => {
    const node = innerContainer.current?.childNodes[0]
    setInitBoxSize({
      initWidth: initBoxSize.initWidth || node?.offsetWidth,
      initHeight: initBoxSize.initHeight || node?.offsetHeight,
    })
  }, [])

  useEffect(() => {
    setBoxSize({
      width: width || initWidth,
      height: height || initHeight,
    })
  }, [height, width])

  useEffect(() => {
    if (code === highlightKey && isScroll) {
      const node = resizeContainer.current.resizable
      node && node.scrollIntoView()
      handleUpdateState('isScroll', false)
    }
  }, [highlightKey])

  useEffect(() => {
    handleChangeFontInComponent(fontParams)
  }, [fontParams])

  useEffect(() => {
    innerContainer.current.addEventListener('mouseover', () => onMouseEvent('mouseover'))

    innerContainer.current.addEventListener('mouseout', () => onMouseEvent('mouseout'))
  }, [])

  return (
    <div ref={innerContainer}>
      <ResizableComponent
        ref={resizeContainer}
        enable={readOnly ? resizeDisabled : resizeEnable}
        minWidth={initBoxSize.initWidth}
        minHeight={initBoxSize.initHeight}
        className={classNames(
          styles.pdfVarBox,
          readOnly && styles.readOnly,
          code === highlightKey && styles.highlight,
        )}
        size={
          boxSize.height && boxSize.width
            ? {
                // 边框包含 2px 偏移，导致压缩内部空间
                width: boxSize.width + 2,
                height: boxSize.height,
              }
            : undefined
        }
        style={{ left, top, ...style }}
        onResizeStop={(e, direction, ref, d) =>
          handleBoxResize(boxSize.width + d.width, boxSize.height + d.height)
        }
      >
        <div
          ref={drag}
          data-index={index}
          style={boxSize.height && boxSize.width ? boxSize : {}}
          className={styles.boxContainer}
          {...dragLineProps}
        >
          {children}
          {!readOnly && !inDragging && (
            <CloseCircleOutlined
              className={styles.pdfVarBoxClear}
              onClick={() => {
                handleUpdateState(
                  'boxes',
                  update(boxes, {
                    $splice: [[index, 1]],
                  }),
                )
              }}
            />
          )}
        </div>
      </ResizableComponent>
    </div>
  )
}

export default VarBoxInView
