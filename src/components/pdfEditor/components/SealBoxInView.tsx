import React, { CSSProperties, useEffect, useRef, useMemo } from 'react'
import { CloseCircleOutlined } from '@ant-design/icons'
import update from 'immutability-helper'
import { useDrag } from 'react-dnd'
import classNames from 'classnames'
import { DRAG_BOX, SEAL_TYPE } from '../common/constant'
import { useCreateStore } from '../context'
import { adjustBoxParams, useDragLine } from './common'

import styles from './style.module.less'

interface IProps {
  sealType: keyof typeof SEAL_TYPE
  index: number
  top: number
  page: number
  left: number
  style?: CSSProperties
  hideSourceOnDrag?: boolean
  readOnly?: boolean
  move?: boolean
  [key: string]: any
}

/**
 * pdf上可拖拽的变量
 */
const SealBoxInView = (props: IProps) => {
  const { index, left, top, page, style, readOnly, hideSourceOnDrag = true, sealType, move } = props

  const useStore = useCreateStore()

  const boxes = useStore((state) => state.boxes)
  const handleUpdateState = useStore((state) => state.updateState)
  const innerContainer = useRef<HTMLDivElement>(null)
  const [{ isDragging }, drag] = useDrag(
    () => ({
      type: DRAG_BOX,
      item: {
        index,
        left,
        top,
        page,
      },
      canDrag: !readOnly,
      hideSourceOnDrag: true,
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    }),
    [index, left, top, page],
  )
  const dragLineProps = useDragLine({ isDragging })

  const inDragging = useMemo(() => isDragging && hideSourceOnDrag, [hideSourceOnDrag, isDragging])

  useEffect(() => {
    const newPdfBoxes = adjustBoxParams({
      params: props,
      node: innerContainer.current,
      boxes,
    })
    if (newPdfBoxes) {
      handleUpdateState('boxes', newPdfBoxes)
    }
  }, [move])

  const isWhichSealClass = (type: keyof typeof SEAL_TYPE) => {
    if (type === SEAL_TYPE.SignatureSeal) {
      return styles.sign
    }
    if (type === SEAL_TYPE.HrSeal) {
      return styles.hr
    }
    return styles.enterprise
  }

  return (
    <div ref={innerContainer}>
      <div
        data-index={index}
        ref={drag}
        className={classNames(
          styles.sealVarBox,
          styles.viewerUse,
          readOnly ? styles.readonly : '',
          isWhichSealClass(sealType),
        )}
        style={{
          left,
          top,
          ...style,
        }}
        data-testid="box"
        {...dragLineProps}
      >
        {!readOnly && !inDragging && (
          <CloseCircleOutlined
            className={styles.pdfVarBoxClear}
            onClick={() => {
              handleUpdateState(
                'boxes',
                update(boxes, {
                  $splice: [[index, 1]],
                }),
              )
            }}
          />
        )}
      </div>
    </div>
  )
}

export default SealBoxInView
