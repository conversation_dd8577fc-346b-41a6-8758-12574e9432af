import React, { useState } from 'react'
import { useDrag } from 'react-dnd'
import classNames from 'classnames'
import { StampVarType } from '../common/types'
import { VAR_TYPE, DRAG_BOX, SEAL_TYPE } from '../common/constant'
import { useDragLine } from './common'

import styles from './style.module.less'

interface PositionProps {
  left?: number
  top?: number
}

interface IProps {
  disabled?: boolean
  type: StampVarType
  sealType: keyof typeof SEAL_TYPE
}

/**
 * 合同管理中存放变量和印章的盒子
 */
const SealBox = ({ type, sealType, disabled }: IProps) => {
  const [position, setPosition] = useState<PositionProps>({
    left: 0,
    top: 0,
  })

  const [{ isDragging }, drag] = useDrag(
    () => ({
      type: DRAG_BOX,
      item: {
        type,
        sealType,
        source: VAR_TYPE.SEAL_BOX,
        left: position.left,
        top: position.top,
      },
      canDrag: () => !disabled,
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
        handlerId: monitor.getHandlerId(),
      }),
    }),
    [position, sealType, type],
  )

  const { onMouseDown: onDragMouseDown, ...restDragLineProps } = useDragLine({ isDragging })

  const isWhichSealClass = (_type: keyof typeof SEAL_TYPE) => {
    if (_type === SEAL_TYPE.EnterpriseSeal) {
      return styles.enterprise
    }
    if (_type === SEAL_TYPE.HrSeal) {
      return styles.hr
    }
    return styles.sign
  }

  const opacity = isDragging ? 0.4 : 1

  return (
    <div
      ref={drag}
      className={classNames(styles.sealVarBox, isWhichSealClass(sealType))}
      style={{
        opacity,
      }}
      data-testid="box"
      onMouseDown={(e) =>
        onDragMouseDown(e, () => {
          const { nativeEvent } = e
          const { offsetX, offsetY } = nativeEvent
          setPosition({
            ...position,
            left: -offsetX,
            top: -offsetY,
          })
        })
      }
      {...restDragLineProps}
    />
  )
}

export default SealBox
