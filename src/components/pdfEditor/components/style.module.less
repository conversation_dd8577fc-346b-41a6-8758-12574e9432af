.sealVarBox {
  margin-right: 20px;
  position: relative;


  &:hover .pdfVarBoxClear {
    opacity: 1;
    color: #27bc9a;
    border-color: #38c0a3;
  }

  &.viewerUse {
    text-align: center;
    position: absolute;
    border-radius: 50px;
    cursor: move;
    margin: 0;
  }

  &.readonly {
    cursor: not-allowed;
  }

  &.sign {
    background-color: red;
    height: 40px;
    width: 95px;
    background: url('@/assets/signature.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  &.enterprise,
  &.hr {
    width: 166px;
    background-color: #27bc9a;
    border-radius: 50%;
    height: 166px;
  }

  &.enterprise {
    background: url('@/assets/stamp2.png') center/contain no-repeat;
  }

  &.hr {
    background: url('@/assets/stamp1.png') center/contain no-repeat;
  }
}

.varBox {
  display: inline-block;
  padding: 5px 8px;
  margin-right: 10px;
  margin-bottom: 10px;
  cursor: move;
  text-align: center;
  background-color: white;
  border: 1px solid #d2d2d2;
  border-radius: 5px;
  color: #9d9d9d;

  &:hover {
    color: #27bc9a;
    border: 1px solid #38c0a3;
  }
}

.pdfVarBox {
  position: absolute !important;
  color: #9d9d9d;
  box-sizing: content-box;
  transform: translate(0, 0);
  border-radius: 5px;
  border: 1px solid transparent;
  font-size: 12pt;


  .boxContainer {
    display: flex;
    justify-content: start;
    align-items: center;
    cursor: move;
    padding: 4px 5px;
    white-space: nowrap;
  }

  &:hover {
    color: #27bc9a;
    border: 1px solid #38c0a3;
  }

  &:hover .pdfVarBoxClear {
    opacity: 1;
    color: #27bc9a;
    border-color: #38c0a3;
  }

  &.highlight {
    border-radius: 5;
    background-color: #f59a23 !important;
  }

  &.readOnly {
    cursor: not-allowed;
    border: none;
    font-weight: bold;
    color: black;
    background-color: transparent;
    width: unset !important;

    .boxContainer {
      width: fit-content !important;
    }
  }
}

.pdfVarBoxClear {
  opacity: 0;
  position: absolute;
  right: -5px;
  top: -5px;
  cursor: pointer;
  font-size: 14px;
  background-color: white;
  border-radius: 50%;
  z-index: 999;
}