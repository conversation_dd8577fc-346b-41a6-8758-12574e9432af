import update from 'immutability-helper'
import { useEffect, useState } from 'react'
import { useThrottleFn } from 'ahooks'
import { SCALE, INIT_FONT_SIZE } from '../common/constant'
import { useCreateStore } from '../context'

interface PointProps {
  x: number
  y: number
}

interface CalcPointFunc {
  point: PointProps
  height: number
  width: number
}

interface AdjustBoxParams {
  params: any
  node: HTMLDivElement | null
  boxes: Array<any>
  width?: number
  height?: number
  change?: boolean
  fontNumChange?: boolean
}

/**
 * 计算右下角(第二个点)坐标
 */
const calcPoint2 = ({ point, width, height }: CalcPointFunc) => ({
  x2: Number(((point.x * SCALE + width) / SCALE).toFixed(2)),
  y2: Number(((point.y * SCALE - height) / SCALE).toFixed(2)),
})

/**
 * 计算单个文字宽度
 */
const getSingleTextWidth = () => {
  const parentDom = document.querySelector('.react-pdf__Document')
  const hiddenDiv = document.createElement('div')
  hiddenDiv.style.visibility = 'hidden'
  hiddenDiv.style.position = 'absolute'
  hiddenDiv.style.fontSize = INIT_FONT_SIZE // 设置字体大小

  hiddenDiv.innerText = '我'
  parentDom?.appendChild(hiddenDiv)

  // 获取文本的宽度
  const textWidth = hiddenDiv.offsetWidth
  parentDom?.removeChild(hiddenDiv)

  return textWidth
}

export const adjustBoxParams = ({
  params,
  node,
  boxes,
  width,
  height,
  fontNumChange,
}: AdjustBoxParams) => {
  const {
    left,
    top,
    index,
    code,
    sealType,
    viewport,
    x: originX,
    y: originY,
    width: currentWidth,
    height: currentHeight,
  } = params

  if (!node) {
    return boxes
  }

  const children = node?.childNodes[0] as HTMLDivElement
  const useWidth = width || currentWidth || children.offsetWidth
  const useHeight = height || currentHeight || children.offsetHeight

  // 根据变量类型计算左上或中心点坐标
  const point1: PointProps = {} as any

  if (viewport && viewport.convertToPdfPoint) {
    // 印章、签名只需要一个坐标   计算中心点就行
    const calcX = sealType ? left + useWidth / 2 : left
    const calcY = sealType ? top + useHeight / 2 : top
    const [pointX, pointY] = viewport.convertToPdfPoint(calcX, calcY)
    point1.x = Number(pointX?.toFixed(2))
    point1.y = Number(pointY?.toFixed(2))
  } else {
    point1.x = Number(originX?.toFixed(2))
    point1.y = Number(originY?.toFixed(2))
  }

  // 计算右下坐标
  const point2 = calcPoint2({
    point: point1,
    width: useWidth,
    height: useHeight,
  })

  const inner = node.querySelector('[draggable]')
  const paddingInfo = inner ? window.getComputedStyle(inner).padding : null
  const padding = paddingInfo
    ? (Number(paddingInfo.split(' ')?.[1]?.replace('px', '')) || 0) * 2
    : 0

  const textWidth = getSingleTextWidth()
  const fontNum = Math.floor((useWidth - padding) / textWidth)

  const newPdfBoxes = boxes.map((box, i) => {
    if (index === i) {
      return update(box, {
        $merge: {
          move: false,
          widthChange: true,
          fontNum,
          fontNumChange,
          width: useWidth,
          height: useHeight,
          initWidth: params.initWidth,
          initHeight: params.initHeight,
          pageWidth: viewport.width / SCALE,
          pageHeight: viewport.height / SCALE,
          top,
          left,
          ...point1,
          ...(sealType ? {} : point2),
        },
      })
    }
    if (box.code === code) {
      return update(box, {
        $merge: { width: useWidth, fontNum, widthChange: true },
      })
    }
    return box
  })
  return newPdfBoxes
}

/**
 * 拖拽参考线 hooks
 */
export const useDragLine = ({ isDragging }) => {
  const useStore = useCreateStore()
  const pageHeight = useStore((state) => state.pageHeight)
  const scrollContainer = useStore((state) => state.scrollContainer)
  const handleUpdateState = useStore((state) => state.updateState)

  const [dragOffsetTop, setDragOffsetTop] = useState(0)

  const { run: onBoxDrop } = useThrottleFn(
    (e) => {
      const { pageY } = e

      const topDistance = scrollContainer?.getBoundingClientRect().top || 0
      const scrollDistance = scrollContainer?.scrollTop || 0

      // 当前对其参考线位置 = 鼠标位置 - 滚动容器距离顶部距离 + 滚动容器已滚动距离
      const mousePositionInDocument = pageY - topDistance + scrollDistance
      const page = Math.ceil(mousePositionInDocument / pageHeight) - 1
      const mousePositionInPage = mousePositionInDocument - pageHeight * page

      handleUpdateState('linePosition', {
        page,
        position: isDragging ? mousePositionInPage - dragOffsetTop : 0,
      })
    },
    { wait: 100 },
  )

  useEffect(() => {
    if (!isDragging) {
      handleUpdateState('linePosition', {
        page: 0,
        position: 0,
      })
    }
  }, [isDragging])

  return {
    onDrag: onBoxDrop,
    onMouseDown: (e, callBack?) => {
      const { nativeEvent, target } = e
      const { offsetY } = nativeEvent
      setDragOffsetTop(offsetY - (target as any).clientHeight / 2)
      callBack && callBack()
    },
  }
}
