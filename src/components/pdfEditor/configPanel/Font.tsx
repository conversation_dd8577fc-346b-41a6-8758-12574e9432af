import React, {
  BaseSyntheticEvent,
  useEffect,
  useRef,
  useState,
  useMemo,
  forwardRef,
  useImperativeHandle,
} from 'react'
import { Collapse } from 'antd'
import { SchemaTable } from '@amazebird/antd-schema-table'
import type { SchemaColumnType } from '@amazebird/antd-schema-table'
import type { SchemaType } from '@amazebird/schema-form'
import { SchemaForm, Item, Observer } from '@amazebird/antd-schema-form'
import { fontSizeOptions, fontFamiltOptions } from '@/constants/template'
import { cloneDeep } from 'lodash-es'
import { FontHandler } from '../common/types'
import { useCreateStore } from '../context'
import styles from './style.module.less'

const { Panel } = Collapse

interface IProps {}

interface DataSource {
  title: string
  code: string
  text?: string
  fontNum?: number
}

const Font = forwardRef<FontHandler, IProps>((props, ref) => {
  const useStore = useCreateStore()
  const form = SchemaForm.createForm()
  const fontForm = SchemaForm.createForm()
  const action = SchemaTable.createAction()
  const boxes = useStore((state) => state.boxes)
  const fontParams = useStore((state) => state.fontParams)
  const handleUpdateState = useStore((state) => state.updateState)
  const [dataSource, setDataSource] = useState<DataSource[]>([])
  const keyRef = useRef(0)

  // 向外暴露的方法
  useImperativeHandle(ref, () => ({
    fontValidate: async () => {
      await form?.validateFields()
      await fontForm?.validateFields()
    },
  }))

  const onFocus = (e: BaseSyntheticEvent) => {
    const componentId = e.target.getAttribute('id')
    const reg = /.*_(\d)_.*/
    const index = Number(reg.exec(componentId)?.[1])
    const current = action.getDataSource()[index]
    if (!current) {
      return
    }
    handleUpdateState('isScroll', true)
    handleUpdateState('highlightKey', current.code)
  }

  const onBlur = (aboutFontNum) => {
    // TODO form ts 类型问题，后续 schema-form 修复
    // @ts-ignore
    let newInitBoxes = cloneDeep(form.getState('boxes'))
    const currentDataSource = action?.getEditData() as any[]

    newInitBoxes = newInitBoxes?.map((box) => {
      if (!box.code) {
        return box
      }

      const current = currentDataSource.find((v) => v.code === box.code)
      return Object.assign(
        box,
        { content: current.text },
        aboutFontNum
          ? {
              fontNumChange: true,
              fontNum: current.fontNum,
            }
          : {},
      )
    })

    handleUpdateState('highlightKey', null)
    handleUpdateState('boxes', newInitBoxes || [])
  }

  // 用来重新渲染
  const keyId = useMemo(() => {
    if (boxes.length === dataSource.length) {
      return keyRef.current
    }
    keyRef.current += 1
    return keyRef.current
  }, [boxes])

  const schema: SchemaType = {
    fontType: {
      label: '字体',
      component: 'Select',
      required: true,
      options: fontFamiltOptions,
    },
    fontSize: {
      label: '字号',
      component: 'Select',
      required: true,
      options: fontSizeOptions,
    },
  }

  const columns: SchemaColumnType = [
    {
      title: '变量',
      dataIndex: 'title',
      width: 100,
      cell: 'Text',
      fixed: 'left',
    },
    {
      title: '填充字数',
      dataIndex: 'fontNum',
      width: 100,
      cell: 'Text',
      editable: {
        max: 50,
        component: 'InputNumber',
        props: {
          precision: 0,
          onFocus,
          onBlur: () => onBlur('ABOUT_FONT_NUM'),
        },
        observer: Observer({
          watch: './',
          action: (_, { index }) => {
            const current = action?.getEditData()[index]
            return {
              props: {
                precision: 0,
                onFocus,
                onBlur: () => onBlur('ABOUT_FONT_NUM'),
                min: (current?.title?.length || 0) + 1,
              },
            }
          },
        }),
        rules: [{ required: true, message: '请输入' }],
      },
      tooltip:
        '系统将根据变量文本宽高，计算出填充字数；用户可再设定字数，减少/增加变量文本宽度，从而控制填充字数。',
    },
    {
      title: '设置预览文字',
      dataIndex: 'text',
      cell: 'Text',
      width: 120,
      editable: {
        max: 100,
        component: 'Input',
        observer: Observer({
          watch: './',
          action: (_, { index }) => {
            const current = action?.getEditData()[index]
            return {
              props: {
                onBlur,
                onFocus,
                maxLength: current?.fontNum,
              },
            }
          },
        }),
      },
    },
  ]

  useEffect(() => {
    const result: any[] = []
    boxes.forEach((box) => {
      const codes = result.map((r) => r.code)
      if (box.code && !codes.includes(box.code)) {
        result.push({
          code: box.code,
          title: box.title,
          text: box.content || '',
          fontNum: box.fontNum || (box.title?.length || 0) + 1,
        })
      }
    })

    console.log(result)
    setDataSource(result)

    // TODO form ts 类型问题，后续 schema-form 修复
    // @ts-ignore
    form?.setState({
      boxes,
    })
  }, [boxes])

  useEffect(() => {
    form?.setFieldsValue(fontParams)
  }, [fontParams])

  return (
    <SchemaForm
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
      schema={schema}
      form={form}
      className={styles.fontModule}
      onValuesChange={(_, values) => handleUpdateState('fontParams', values)}
    >
      <Collapse defaultActiveKey={['1', '2']}>
        <Panel header="字体设置" key="1">
          <Item field="fontType" />
          <Item field="fontSize" />
        </Panel>
        <Panel header="变量填充" key="2">
          <SchemaTable
            key={keyId}
            editForm={fontForm}
            action={action}
            columns={columns}
            emptyText="暂无数据"
            dataSource={dataSource}
            editable={{ errorType: 'inline' }}
            footer={undefined}
            pagination={false}
          />
        </Panel>
      </Collapse>
    </SchemaForm>
  )
})

export default Font
