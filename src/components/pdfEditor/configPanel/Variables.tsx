import React, { useEffect, useState, useMemo } from 'react'
import { useDebounce } from 'ahooks'
import { Collapse, Input, Empty } from 'antd'
import { VarBox } from '../components'
import styles from './style.module.less'

const { Panel } = Collapse

interface IProps {
  pdfVars: any
}

const VariablesElement = ({ pdfVars }: IProps) => {
  const [search, setSearch] = useState('')
  const [useVars, setUseVars] = useState(pdfVars)

  const debouncedSearch = useDebounce(search, {
    wait: 500,
  })

  const defaultCollapseKeys = useMemo(() => Object.keys(useVars), [useVars])

  useEffect(() => {
    if (debouncedSearch) {
      const vars = {}
      Object.keys(pdfVars).forEach((key) => {
        const searchVars = pdfVars[key]?.list?.filter(
          (item) => item.variableName.indexOf(debouncedSearch) !== -1,
        )
        if (searchVars.length) {
          vars[key] = {
            ...pdfVars[key],
            list: searchVars,
          }
        }
      })
      setUseVars(vars)
    } else {
      setUseVars(pdfVars)
    }
  }, [debouncedSearch, pdfVars])

  return (
    <div className={styles.variables}>
      <Input
        value={search}
        placeholder="搜索常用变量"
        className={styles.search}
        onChange={(e) => setSearch(e.target.value)}
      />
      {defaultCollapseKeys.length > 0 ? (
        <Collapse
          defaultActiveKey={defaultCollapseKeys}
          expandIconPosition="end"
          className={styles.collapseContainer}
        >
          {Object.keys(useVars)
            .sort((prev, next) => useVars[prev].sort - useVars[next].sort)
            .map((key) => (
              <Panel
                header={
                  <>
                    <span>{key}</span>
                    {useVars[key].source && (
                      <span className={styles.source}>
                        （取自
                        {useVars[key].source}）
                      </span>
                    )}
                  </>
                }
                key={key}
              >
                {useVars[key]?.list?.length > 0 ? (
                  useVars[key].list.map((item) => (
                    <VarBox
                      key={item.variableCode}
                      name={item.variableName}
                      code={item.variableCode}
                    />
                  ))
                ) : (
                  <Empty />
                )}
              </Panel>
            ))}
        </Collapse>
      ) : (
        <Empty className={styles.emptyWrapper} />
      )}
    </div>
  )
}

export default VariablesElement
