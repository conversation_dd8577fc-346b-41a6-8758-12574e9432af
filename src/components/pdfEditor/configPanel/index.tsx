import React, { useEffect } from 'react'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { Tabs, Tooltip } from 'antd'
import classNames from 'classnames'
import { getVariablesApi } from '@/api/sign-manage'
import { getVarGroups } from '@/api/basicConfig/var-manage'
import { VariableProps } from '@/types/pdf-edit'
import { useCreateStore } from '../context'
import Keyword from './Keyword'
import VariablesElement from './Variables'
import Font from './Font'
import styles from './style.module.less'

const tabs = ['常用变量', '印章定位', '排版样式']

interface IProps {
  keyForm?: any
  fontRef?: any
  className?: string
  detailSlot?: () => React.ReactNode
}

export const ConfigDrawer = ({ className, fontRef, keyForm, detailSlot }: IProps) => {
  // 配置面板上的模板变量
  const [pdfVars, setPdfVars] = React.useState<Record<string, VariableProps[]>>({})
  const useStore = useCreateStore()
  const tabsKey = useStore((state) => state.tabsKey)
  const handleUpdateState = useStore((state) => state.updateState)

  /**
   * 获取变量
   */
  const getVariables = async () => {
    const { data: result } = await getVariablesApi()
    const { data: groupRes } = await getVarGroups()
    const varGroups = groupRes.data

    const varsSet = {}
    ;(result.data as VariableProps[]).forEach((v) => {
      if (v.isEnable) {
        // 展示已启用的变量
        ;(varsSet[v.groupName || '未分组'] ??= [] as VariableProps[]).push(v)
      }
    })
    setPdfVars(varsSet)

    // 根据分组归类判断携带来源信息
    const varsSetWithSource = {}
    Object.keys(varsSet).forEach((key) => {
      // 当同属于一组的所有变量的 source 一致时，才使用这个 source 否则为空
      const source = varsSet[key][0].source
      const sameSource = varsSet[key].every((v) => v.source === source)
      const groupIndex = (varGroups as Array<any>).findIndex((v) => v.groupName === key)

      varsSetWithSource[key] = {
        source: sameSource ? source : '',
        list: varsSet[key],
        sort: groupIndex,
      }
    })

    setPdfVars(varsSetWithSource)
  }

  const DescIcon = () => (
    <Tooltip
      className={styles.descTooltip}
      showArrow={false}
      title={() => (
        <div className={styles.descArea}>
          <p className={styles.title}>常用变量</p>
          <p className={styles.text}>
            1、<span className={styles.highlight}>拖拽</span>
            变量放在合同内容后，拖动可进行文字对齐，鼠标悬停变量下边框、右边框、右下角可调整宽度和高度。
          </p>
          <br />
          <p className={styles.title}>印章定位</p>
          <p className={styles.text}>
            1、关键字定位：支持批量签署。通过合同中的
            <span className={styles.highlight}>关键字</span>，确定盖章位置；
          </p>
          <p className={styles.text}>
            2、固定位置定位：不支持批量签署。通过<span className={styles.highlight}>拖拽</span>
            印章放在合同固定位置，确定盖章位置。
          </p>
          <br />
          <p className={styles.title}>排版样式</p>
          <p className={styles.text}>1、支持设置字体颜色和大小；</p>
          <p className={styles.text}>
            2、变量填充文字只是<span className={styles.highlight}>模拟数据</span>
            查看效果，实际不会发起签署。
          </p>
        </div>
      )}
    >
      <QuestionCircleOutlined className={styles.icon} />
    </Tooltip>
  )

  const TabPane = {
    0: <VariablesElement pdfVars={pdfVars} />,
    1: <Keyword keyForm={keyForm} />,
    2: <Font ref={fontRef} />,
  }

  useEffect(() => {
    getVariables()
  }, [])

  return (
    <div className={classNames(styles.configPanel, className)}>
      {detailSlot ? (
        detailSlot()
      ) : (
        <Tabs
          activeKey={tabsKey}
          onTabClick={(key) => handleUpdateState('tabsKey', key)}
          className={styles.tabContainer}
          type="card"
          tabBarExtraContent={<DescIcon />}
          items={tabs.map((tab, i) => {
            return {
              label: tab,
              key: tab,
              children: TabPane[i],
            }
          })}
        />
      )}
    </div>
  )
}
