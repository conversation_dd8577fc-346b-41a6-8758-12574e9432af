.configPanel {
  width: 380px;
  flex-shrink: 0;
  border-left: 1px solid #e5e5e5;

  @media screen and (max-width: 1600px) {
    // TODO
    width: 410px;

    :global {
      .@{ant-prefix}-row {
        display: unset;
      }

      .@{ant-prefix}-col-sm-8 {
        max-width: unset;
      }
    }
  }


  .schemaWrapper {
    padding: 10px;
  }

  :global {
    .@{ant-prefix}-tabs-tab {
      margin-left: 0px !important;
    }

    .@{ant-prefix}-tabs-nav-wrap {
      flex: unset !important;
    }

    .@{ant-prefix}-space-horizontal {
      gap: 0px !important;
    }

    .@{ant-prefix}-tabs-content-holder {
      overflow: auto;
    }

    .@{ant-prefix}-collapse-header {
      padding: 12px 24px;
      border-top: none;
    }


    .@{ant-prefix}-collapse-item {
      &:not(.@{ant-prefix}-collapse-item-active) {
        border-bottom: 1px solid #d9d9d9;
      }

      border-bottom: none;
    }
  }



  .descTooltip {
    position: absolute;
    margin-left: 10px;
    top: 50%;
    cursor: pointer;
    transform: translateY(-50%);
  }


  .schemaWrapper {
    padding: 10px;
  }


  .tabContainer {
    background-color: white;
    height: 100%;

    :global {
      .@{ant-prefix}-tabs-tab {
        position: relative;
        margin-left: 0px !important;
      }

      .@{ant-prefix}-space-horizontal {
        gap: 0px !important;
      }

      .@{ant-prefix}-tabs-content-holder {
        overflow: auto;
      }
    }

    .collapseContainer {
      margin-top: 20px;
      background-color: white;
      border: none;

      .source {
        font-size: 12px;
        color: #AAAAAA;
      }
    }

    .search {
      width: 84%;
      margin: 0 auto;
      border-radius: 3px;
      display: block;
    }
  }
}

.keyWordModule {
  .tip {
    margin: 10px 12px;
    color: #acacac;
  }

  .tooltipIcon {
    cursor: pointer;
  }

  .keywordItem {
    display: flex;
    align-items: center;
    margin: 0px 12px 20px;
  }

  .desc {
    color: #aeaeae;
    font-size: 12px;
  }

  .positionInfo {
    margin: 5px 14px 30px;
    font-size: 12px;
    color: #a3a3a3;
  }

  :global {
    .@{ant-prefix}-form-horizontal .@{ant-prefix}-form-item-control {
      max-width: unset;
    }

    .@{ant-prefix}-form-item {
      margin-bottom: 10px;
    }
  }
}

.variables {
  .emptyWrapper {
    margin-top: 20px;
  }
}

.descArea {
  .highlight {
    color: #e7952a;
  }

  .title {
    font-size: 14px;
    margin: 0px 0px 5px 0px;
  }

  .text {
    font-size: 12px;
    margin: 0px;
  }
}


.fontModule {
  :global {
    .@{ant-prefix}-row {
      display: flex;
    }
  }
}