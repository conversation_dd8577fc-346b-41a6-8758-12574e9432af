/* eslint-disable max-lines-per-function */
import React, { useMemo, useState, useEffect, useRef } from 'react'
import { Row, Col } from 'antd'
import { useDebounceFn } from 'ahooks'
import { SchemaForm, Item, SchemaType } from '@amazebird/antd-schema-form'
import { dictLocateMode } from '@/api/dict'
import { STAMP_VAR_TYPE, SIGN_TYPE, SEAL_TYPE } from '../common/constant'
import { useCreateStore } from '../context'
import { SealBox } from '../components'

import styles from './style.module.less'

type KeywordType = {
  label: string
  field: 'signature' | 'stamp' | 'stamp2'
}

interface IProps {
  keyForm: any
}

const Keyword = ({ keyForm }: IProps) => {
  const useStore = useCreateStore()
  const search = useStore((state) => state.search)
  const boxes = useStore((state) => state.boxes)
  const locateMode = useStore((state) => state.locateMode)
  const pdfContent = useStore((state) => state.content)
  const handleUpdateState = useStore((state) => state.updateState)
  const sealCode = useStore((state) => state.sealCode)
  const signType = useStore((state) => state.signType)
  const positionForm = SchemaForm.createForm()
  const keyRef = useRef(0)

  const [stampInfo, setStampInfo] = useState('')
  const [stamp2Info, setStamp2Info] = useState('')
  const [signatureInfo, setSignatureInfo] = useState('')
  /**
   * 生成印章、签名的位置信息
   */
  const generatePositionInfo = (type: STAMP_VAR_TYPE) => {
    const newBoxes = boxes.filter((box) => box.type === type)
    const page = Array.from(new Set(newBoxes.map((b) => b.page + 1)))
    return page.length > 0
      ? `共 ${newBoxes.length} 处，${page.length === 1 ? '' : '分别'}在第 ${page.join('、')} 页`
      : ''
  }

  /**
   * 位置信息查找
   */
  const searchTask = async (searchText) => {
    let occurNum = 0
    const pageNumbers: Array<number> = []

    for (let i = 0; i < pdfContent.length; i++) {
      const content = pdfContent[i].content

      const occur = content.split(searchText).length - 1

      if (occur) {
        occurNum += occur
        pageNumbers.push(i + 1)
      }
    }
    return [occurNum, pageNumbers]
  }

  /**
   * 关键字变化
   */
  const { run: onKeywordChangeDebounce } = useDebounceFn(
    (value, type) => {
      const trimValue = (value || '').trim()
      // handleUpdateState(
      //   'search',
      //   type === STAMP_VAR_TYPE.SEAL ? [trimValue, search[1]] : [search[0], trimValue],
      // )
      let targetSearch: [string?, string?, string?] = ['', '', '']
      switch (type) {
        case STAMP_VAR_TYPE.SEAL:
          targetSearch = [trimValue, search[1], search[2]]
          break
        case STAMP_VAR_TYPE.SIGNATURE:
          targetSearch = [search[0], trimValue, search[2]]
          break
        case STAMP_VAR_TYPE.SEAL2:
          targetSearch = [search[0], search[1], trimValue]
          break
      }
      handleUpdateState('search', targetSearch)
    },
    {
      wait: 300,
    },
  )

  /**
   * 校验搜索的关键字是否存在
   */
  const keywordExistValidator = async (value) => {
    for (let i = 0; i < pdfContent.length; i++) {
      const content = pdfContent[i].content
      if (content.includes(value)) {
        return Promise.resolve()
      }
    }
    return Promise.reject(new Error('没有找到对应关键字'))
  }

  /**
   * 与员工签名关键字不允许重复校验
   */
  const sameWithAnotherKeywordValidator = async (value: string, fields: KeywordType[]) => {
    for (let i = 0; i < fields.length; i++) {
      if (value && value === keyForm.getFieldValue(fields[i].field)) {
        return Promise.reject(new Error(`与「${fields[i].label}」关键字不能重复`))
      }
    }
    return Promise.resolve()
  }

  /**
   * 获取印章定位方式字典选项
   */
  const getLocateMode = async () => {
    const { LOCATE_MODE } = await dictLocateMode()
    const options = LOCATE_MODE.sort((prev, next) => next.id - prev.id)
    return options.map((v) => ({
      label: v.name,
      value: v.code,
    }))
  }

  const positionSchema: SchemaType = useMemo(
    () => ({
      position: {
        label: '印章定位方式',
        component: 'Radio',
        options: () => getLocateMode(),
        props: {
          optionType: 'button',
          buttonStyle: 'solid',
          onChange: (e) => handleUpdateState('locateMode', e.target?.value),
        },
      },
    }),
    [],
  )

  const keywordSchema: SchemaType = useMemo(
    () => ({
      stamp: {
        label: '企业印章关键字',
        component: 'Input',
        required: true,
        max: 10,
        props: {
          autocomplete: 'off',
          onChange: (e) => onKeywordChangeDebounce(e.target.value, STAMP_VAR_TYPE.SEAL),
        },
        rules: [
          {
            validator: (_, value) => keywordExistValidator(value),
          },
          {
            validator: (_, value) =>
              sameWithAnotherKeywordValidator(value, [
                {
                  label: '企业印章关键字2',
                  field: 'stamp2',
                },
                {
                  label: '员工签名关键字',
                  field: 'signature',
                },
              ]),
          },
        ],
      },
      stamp2: {
        label: '企业印章关键字2',
        component: 'Input',
        max: 10,
        props: {
          autocomplete: 'off',
          onChange: (e) => onKeywordChangeDebounce(e.target.value, STAMP_VAR_TYPE.SEAL2),
        },
        rules: [
          {
            validator: (_, value) => keywordExistValidator(value),
          },
          {
            validator: (_, value) =>
              sameWithAnotherKeywordValidator(value, [
                {
                  label: '企业印章关键字',
                  field: 'stamp',
                },
                {
                  label: '员工签名关键字',
                  field: 'signature',
                },
              ]),
          },
        ],
      },
      signature: {
        label: '员工签名关键字',
        component: 'Input',
        required: true,
        max: 10,
        props: {
          onChange: (e) => onKeywordChangeDebounce(e.target.value, STAMP_VAR_TYPE.SIGNATURE),
          autocomplete: 'off',
        },
        rules: [
          {
            validator: (_, value) => keywordExistValidator(value),
          },
          {
            validator: (_, value) =>
              sameWithAnotherKeywordValidator(value, [
                {
                  label: '企业印章关键字',
                  field: 'stamp',
                },
                {
                  label: '企业印章关键字2',
                  field: 'stamp2',
                },
              ]),
          },
        ],
      },
    }),
    [pdfContent],
  )

  // 用来重新渲染
  const keyId = useMemo(() => {
    keyRef.current += 1
    return keyRef.current
  }, [pdfContent])

  // 设置搜索结果信息
  const setSearchInfo = (index, value) => {
    switch (index) {
      case 0:
        setStampInfo(value)
        break
      case 1:
        setSignatureInfo(value)
        break
      case 2:
        setStamp2Info(value)
        break
    }
  }

  useEffect(() => {
    keyForm.setFieldsValue({
      stamp: search[0] || '',
      signature: search[1] || '',
      stamp2: search[2] || '',
    })

    search.forEach((s, index) => {
      if (!s) {
        setSearchInfo(index, '')
        // index ? setSignatureInfo('') : setStampInfo('')
        return
      }

      searchTask(s).then(([count, page]) => {
        const info = !count
          ? ''
          : `共 ${count} 处，${count === 1 ? '' : '分别'}在第 ${(page as Array<number>)
              .sort((prev, next) => Number(prev) - Number(next))
              .join('、')} 页`
        setSearchInfo(index, info)
        // index ? setSignatureInfo(info) : setStampInfo(info)
      })
    })
  }, [search])

  useEffect(() => {
    positionForm?.setFieldsValue({ position: locateMode })
  }, [locateMode])

  return (
    <div className={styles.keyWordModule}>
      <SchemaForm
        form={positionForm}
        schema={positionSchema}
        labelAlign="left"
        className={styles.schemaWrapper}
      >
        <Item
          field="position"
          tooltip={
            <>
              1、固定位置定位：通过设置印章在合同模板上的固定位置，确定企业和员工的签章位置；
              <br />
              2、关键字定位：通过合同中的特定文字，如“签字处”，确定企业和员工的签章位置。
            </>
          }
        />
      </SchemaForm>
      {locateMode === 'CM_LM_001' ? (
        <>
          <p className={styles.tip}>拖拽印章，放在左侧固定位置</p>
          {sealCode && (
            <div className={styles.keywordItem}>
              <SealBox
                type={STAMP_VAR_TYPE.SEAL}
                sealType={sealCode === 'CM_RS_001' ? SEAL_TYPE.EnterpriseSeal : SEAL_TYPE.HrSeal}
              />
              <span className={styles.desc}>{generatePositionInfo(STAMP_VAR_TYPE.SEAL)}</span>
            </div>
          )}
          {(!sealCode || (sealCode && signType === SIGN_TYPE.BOTH_SIGN)) && (
            <div className={styles.keywordItem}>
              <SealBox type={STAMP_VAR_TYPE.SIGNATURE} sealType={SEAL_TYPE.SignatureSeal} />
              <span className={styles.desc}>{generatePositionInfo(STAMP_VAR_TYPE.SIGNATURE)}</span>
            </div>
          )}
        </>
      ) : (
        <SchemaForm
          schema={keywordSchema}
          key={keyId}
          form={keyForm}
          labelAlign="left"
          className={styles.schemaWrapper}
          onValuesChange={onKeywordChangeDebounce}
        >
          {
            // 签署方式为企业员工双方签署、企业单方签署时
            signType === SIGN_TYPE.BOTH_SIGN || signType === SIGN_TYPE.ENTERPRISE_SIGN ? (
              <>
                <Item field="stamp" validateFirst />
                <Row>
                  <Col xxl={{ offset: 8 }}>
                    <p className={styles.positionInfo}>{stampInfo}</p>
                  </Col>
                </Row>
                <Item field="stamp2" validateFirst />
                <Row>
                  <Col xxl={{ offset: 8 }}>
                    <p className={styles.positionInfo}>{stamp2Info}</p>
                  </Col>
                </Row>
              </>
            ) : null
          }
          {
            // 签署方式为企业员工双方签署、员工单方签署时
            signType === SIGN_TYPE.BOTH_SIGN || signType === SIGN_TYPE.PERSON_SIGN ? (
              <>
                <Item field="signature" validateFirst />
                <Row>
                  <Col xxl={{ offset: 8 }}>
                    <p className={styles.positionInfo}>{signatureInfo}</p>
                  </Col>
                </Row>
              </>
            ) : null
          }
        </SchemaForm>
      )}
    </div>
  )
}

export default Keyword
