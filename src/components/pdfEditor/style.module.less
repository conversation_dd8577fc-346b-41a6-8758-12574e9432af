.pdfEdit {
  background-color: white;
  width: fit-content;
  display: inline-block;

  &.readOnly {
    margin: initial;
    background-color: initial;

    .container {
      max-height: unset;
    }
  }

  .container {
    display: flex;
    height: 100%;
    max-height: calc(100vh - 341px);
    flex-direction: row;
    overflow-x: auto;
    overflow-y: hidden;
  }
}

.varBox {
  padding: 3px 5px;
  margin-right: 10px;
  margin-bottom: 10px;
  cursor: move;
  text-align: center;
  background-color: white;
  border: 1px dashed gray;
}

@font-face {
  font-family: "STFangsong";
  src: url("@/assets/fonts/STFangsong.ttf");
  font-display: swap;
}

@font-face {
  font-family: "STHeiti";
  src: url("@/assets/fonts/STHeiti.ttf");
  font-display: swap;
}

@font-face {
  font-family: "STKaiti";
  src: url("@/assets/fonts/STKaiti.ttf");
  font-display: swap;
}

@font-face {
  font-family: "STSong";
  src: url("@/assets/fonts/STSong.ttf");
  font-display: swap;
}

@font-face {
  font-family: "YaHei";
  src: url("@/assets/fonts/YaHei.ttf");
  font-display: swap;
}