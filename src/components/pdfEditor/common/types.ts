import { UseBoundStore, StoreApi } from 'zustand'
import { STAMP_VAR_TYPE } from './constant'

export type Store = UseBoundStore<StoreApi<StoreProps & StoreActionProps>>

export interface StoreProps {
  tabsKey: string
  boxes: any[]
  search: [string?, string?, string?]
  file: FileProps
  locateMode: string
  readOnly: boolean
  sealCode: string
  signType: string
  pageHeight: number
  highlightKey: string
  isScroll: boolean
  scrollContainer: HTMLDivElement | null
  content: Array<{ page: number; content: string }>
  activeNode: any
  linePosition: {
    page: number
    position: number
  }
  fontParams: {
    fontSize: string | null
    fontType: string | null
  }
}

export interface StoreActionProps {
  updateState: (key: keyof StoreProps, value) => void
}

export type PdfEditorHandler = {
  updateTabsState: (status) => void
}

/**
 * 印章类型
 * 1:公司/hr印章  2:员工签名印章 3:企业关键字印章2
 */
export type StampVarType = STAMP_VAR_TYPE.SEAL | STAMP_VAR_TYPE.SIGNATURE | STAMP_VAR_TYPE.SEAL2

export interface BoxProps {
  type?: StampVarType // 1.印章 2.签名 undefined: 变量
  variableCode?: string // 变量名
  point1: { x: number; y: number } // 左上角坐标
  point2: { x: number; y: number } // 右下角左边
  info: string
}

export type HighLightTextProps = {
  text: string
  color: string
}

export interface FileProps {
  data: File | Blob | string
  name?: string
  id?: string
}

export type FontHandler = {
  fontValidate: () => Promise<void>
}

export interface FontValues {
  fontSize: string
  fontType: string
}
