const findAllOccur = (str, searchStr) => {
  if (!searchStr || !str) {
    return []
  }
  const indices: Array<[number, number]> = []
  let idx = str.indexOf(searchStr)
  while (idx !== -1) {
    indices.push([idx, idx + searchStr.length])
    idx = str.indexOf(searchStr, idx + 1)
  }
  return indices
}

const findTextByIndexes = (indexesArray, textArray) => {
  const resultArray: any[] = []
  for (let i = 0; i < indexesArray.length; i++) {
    const [startIndex, endIndex] = indexesArray[i]
    let remainingStartIndex = startIndex
    let remainingEndIndex = endIndex - 1

    for (let j = 0; j < textArray.length; j++) {
      const { str, transform } = textArray[j]
      const textLength = str.length

      if (remainingStartIndex >= textLength) {
        remainingStartIndex -= textLength
        remainingEndIndex -= textLength
      } else if (remainingEndIndex < textLength) {
        const foundText = str.substring(remainingStartIndex, remainingEndIndex + 1)
        resultArray.push({ text: foundText, transform: transform.join(',') })
        break
      } else {
        const foundText = str.substring(remainingStartIndex)
        resultArray.push({ text: foundText, transform: transform.join(',') })
        remainingStartIndex = 0
        remainingEndIndex -= textLength
      }
    }
  }

  return resultArray
}

const findTextByRawContent = (searchStr, rawPageContent: any[] = [], searchContent: any[] = []) => {
  const transformMap: Record<string, boolean> = {}
  searchContent.forEach((item) => {
    transformMap[item.transform] = item.text.indexOf(searchStr) >= 0
  })
  const resultArray: any[] = []
  rawPageContent.forEach((item) => {
    if (item.str.indexOf(searchStr) >= 0 && !transformMap[item.transform.join(',')]) {
      transformMap[item.transform.join(',')] = true
      resultArray.push({
        text: item.str,
        mergeText: item.mergeText,
        maxSubstr: item.maxSubstr,
        transform: item.transform.join(','),
        transformArr: item.transformArr.map((transform) => transform.join(',')),
        isMerge: true,
      })
    }
    return undefined
  })
  return resultArray
}

/** 查找组合后各子串在目标串中的最大占比 */
const findSubstringIndex = (arr, substr) => {
  const concatStr = arr.join('')
  const start = concatStr.indexOf(substr)
  const end = start + substr.length
  const proportionArr: any[] = []
  let curStr = ''
  let count = 0
  let maxIndex = -1
  let maxLen = -1
  let maxSubstr
  for (let i = 0; i < arr.length; i++) {
    curStr += arr[i]
    if (curStr.length >= start) {
      const proportionLen =
        curStr.length >= end ? substr.length - count : curStr.length - start - count
      const mergeSub = curStr.substring(start + count, start + count + proportionLen)
      count += proportionLen
      proportionArr.push([i, proportionLen])
      if (maxLen < proportionLen) {
        maxLen = proportionLen
        maxIndex = i
        maxSubstr = mergeSub
      }
      if (count === substr.length) {
        break
      }
    }
  }

  return {
    maxIndex,
    maxSubstr,
  }
}

// 数据处理，存在目标字符串在查询数据集被分段，导致无法匹配，如目标串为”竞业限制协议“，查询数据集[{ str: "xxx竞业限制" }, { str: "协议xx" }]
const processData = (searchStr: any, data: any[] = []) => {
  const resultArr: any[] = []

  // 进行数据处理
  for (let i = 0; i < data.length; i++) {
    let str = data[i].str
    let width = data[i].width
    const itemArr: any[] = [data[i]]
    const baseTransform = data[i].transform
    // 首先将当前数据添加到结果中

    // 逐步添加后续的字符串
    for (let j = i + 1; j < data.length; j++) {
      if (baseTransform[5] !== data[j].transform[5]) {
        break
      }
      str += data[j].str
      width += data[j].width
      itemArr.push(data[j])
      // 将合并后的字符串插入到结果数组的当前位置之后
      if (str.indexOf(searchStr) >= 0) {
        i = j + 1
        const { maxIndex, maxSubstr } = findSubstringIndex(
          itemArr.map((item) => item.str),
          searchStr,
        )
        resultArr.push({
          ...data[i],
          str,
          width,
          transform: itemArr[maxIndex].transform,
          transformArr: itemArr.map((item) => item.transform), // 已合并数据的坐标
          mergeText: itemArr[maxIndex].str,
          maxSubstr,
        })
        break
      }
    }
  }

  return resultArr
}

export const sealLoateByKeyword = (search, pageContent, rawPageContent) => {
  const rawPageContentMergeSearch1 = search[0] ? processData(search[0], rawPageContent) : []
  const rawPageContentMergeSearch2 = search[1] ? processData(search[1], rawPageContent) : []
  const rawPageContentMergeSearch3 = search[2] ? processData(search[2], rawPageContent) : []
  const search1FindIndex = search[0] ? findAllOccur(pageContent, search[0]) : []
  const search2FindIndex = search[1] ? findAllOccur(pageContent, search[1]) : []
  const search3FindIndex = search[2] ? findAllOccur(pageContent, search[2]) : []
  const searchContent1 = findTextByIndexes(search1FindIndex, rawPageContent)
  const searchContent2 = findTextByIndexes(search2FindIndex, rawPageContent)
  const searchContent3 = findTextByIndexes(search3FindIndex, rawPageContent)
  const searchMergeContent1 = search[0]
    ? findTextByRawContent(search[0], rawPageContentMergeSearch1, searchContent1)
    : []
  const searchMergeContent2 = search[1]
    ? findTextByRawContent(search[1], rawPageContentMergeSearch2, searchContent2)
    : []
  const searchMergeContent3 = search[2]
    ? findTextByRawContent(search[2], rawPageContentMergeSearch3, searchContent3)
    : []
  // console.log(
  //   searchContent1,
  //   searchContent2,
  //   searchContent3,
  //   searchMergeContent1,
  //   searchMergeContent2,
  //   searchMergeContent3,
  // )
  return {
    content1: [...searchContent1, ...searchMergeContent1],
    content2: [...searchContent2, ...searchMergeContent2],
    content3: [...searchContent3, ...searchMergeContent3],
  }
}
