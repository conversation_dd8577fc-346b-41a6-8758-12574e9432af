import { SCALE } from './constant'

const pageHeight = 842 * SCALE

export function transPageToPdfCoords(pageCoords) {
  if (!pageCoords) {
    return undefined
  }

  const pdfCoords = pageCoords.map((item) => {
    const { page = 1, top, ...rest } = item
    const newItem = {
      top: top + (page - 1) * pageHeight,
      ...rest,
    }
    return newItem
  })

  return pdfCoords
}

/**
 * 把有 page 参数的坐标转换绝对位置的坐标
 */
export function transPdfToPageCoords(pdfCoords) {
  if (!pdfCoords) {
    return undefined
  }

  const pageCoords = pdfCoords.map((item) => {
    const { top, ...rest } = item
    const page = Math.floor(top / pageHeight) + 1
    const newItem = {
      page,
      top: top - (page - 1) * pageHeight,
      ...rest,
    }
    return newItem
  })

  return pageCoords
}

/**
 * 转换成 Base64
 */
export const toBase64 = (file) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = (error) => reject(error)
  })

/**
 * Base64 转 blob
 */
export const dataUrlToFile = async (dataUrl) => {
  const res = await fetch(dataUrl)
  const blob = await res.blob()
  return blob
}

export function getScrollbarWidth() {
  // Creating invisible container
  const outer = document.createElement('div')
  outer.style.visibility = 'hidden'
  outer.style.overflow = 'scroll' // forcing scrollbar to appear
  ;(outer.style as any).msOverflowStyle = 'scrollbar' // needed for WinJS apps
  document.body.appendChild(outer)

  // Creating inner element and placing it in the container
  const inner = document.createElement('div')
  outer.appendChild(inner)

  // Calculating difference between container's full width and the child width
  const scrollbarWidth = outer.offsetWidth - inner.offsetWidth

  // Removing temporary elements from the DOM
  outer?.parentNode?.removeChild(outer)

  return scrollbarWidth
}

export function asyncMap(arr, fn) {
  return new Promise((resolve, reject) => {
    Promise.all(arr.map(fn)).then(resolve).catch(reject)
  })
}
