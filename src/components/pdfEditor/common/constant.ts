const INIT_FONT_SIZE = '12pt'

const SCALE = 1.4

const DRAG_BOX = 'DRAG_BOX'

const VAR_TYPE = {
  SEAL_BOX: 'SealBox',
  NORMAL_BOX: 'NormalBox',
}

enum STAMP_VAR_TYPE {
  SEAL = 1, // 企业关键字
  SIGNATURE = 2, // 员工签名关键字
  SEAL2 = 3, // 企业关键字2
}

const SIGN_TYPE = {
  BOTH_SIGN: 'CM_ST_001',
  ENTERPRISE_SIGN: 'CM_ST_002',
  PERSON_SIGN: 'CM_ST_003',
}

export enum SEAL_TYPE {
  EnterpriseSeal = 'EnterpriseSeal',
  HrSeal = 'HrSeal',
  SignatureSeal = 'SignatureSeal',
}

export { SCALE, VAR_TYPE, DRAG_BOX, STAMP_VAR_TYPE, SIGN_TYPE, INIT_FONT_SIZE }
