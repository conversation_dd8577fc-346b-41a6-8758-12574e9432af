import React, { useEffect, forwardRef, useImperativeHandle } from 'react'
import 'react-pdf/dist/esm/Page/TextLayer.css'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import classNames from 'classnames'
import { cloneDeep } from 'lodash-es'

import { Detector as FontDetector } from '@/utils/font'
import { useCreateStore, Provider } from './context'

import PdfViewer from './pdfViewer'
import { ConfigDrawer } from './configPanel/index'
import type {
  BoxProps,
  HighLightTextProps,
  FileProps,
  PdfEditorHandler,
  FontValues,
} from './common/types'

import styles from './style.module.less'

interface PdfEditorProps {
  /** PDF 操作栏扩展 */
  extendAction?: React.ReactNode
  /** 是否使用虚拟滚动 */
  useVirtual?: boolean
  /** 只读模式，用于预览模板; 只读时不能拖动变量，不显示配置面板 */
  readOnly?: boolean
  /** 详情模式时右侧插槽 */
  detailSlot?: (values: any) => React.ReactNode
  /** 是否使用扩展插槽 */
  useActionSlot?: boolean
  /** 关联印章 */
  sealCode?: string
  /** 签署方式 */
  signType?: string
  /** 需要高亮的文字 */
  highlightTexts?: HighLightTextProps[]
  highlightKey?: string
  keyForm?: any
  fontRef?: any
  /** pdf url 或者流 */
  file: FileProps
  /** 变量（包括印章和签名）坐标配置（故不专门开 印章定位的配置） */
  initBoxes?: BoxProps[]
  initSearch?: [string, string, string]
  initLocateMode?: string
  initFontParams?: FontValues
  /** 拖动变量或者印章时将坐标传递传递出去 */
  onBoxChange?: (args: any) => void
  pageHeaderConfig?: {
    total: number /** 拼接的pdf总数 */
    preTotal: number /** 上份pdf总数 */
    pageIndex: number /** 第几份pdf，索引 */
  }
  extraClassName?: string
  docLoaded?: (num: number, pageIndex?: number) => void
  onLocateModeChange?: (value: string) => void
  onSearchChange?: (search: [string?, string?, string?]) => void
  onFontParamsChange?: (values: FontValues) => void
}

const PdfEdit = forwardRef<PdfEditorHandler, PdfEditorProps>((props: PdfEditorProps, ref) => {
  const {
    extendAction,
    keyForm,
    fontRef,
    sealCode,
    signType,
    file,
    initBoxes,
    onBoxChange,
    readOnly = false,
    detailSlot,
    highlightTexts,
    extraClassName,
    pageHeaderConfig,
    docLoaded,
    onLocateModeChange,
    onSearchChange,
    onFontParamsChange,
    highlightKey: initHighlightKey,
    initSearch,
    initLocateMode,
    initFontParams,
    useVirtual = true,
    useActionSlot = true,
  } = props

  const useStore = useCreateStore()
  const search = useStore((state) => state.search)
  const fontParams = useStore((state) => state.fontParams)
  const locateMode = useStore((state) => state.locateMode)
  const handleUpdateState = useStore((state) => state.updateState)

  const fontDetector = new FontDetector()
  const readOnlyClass = readOnly && !detailSlot ? styles.readOnly : null

  useImperativeHandle(ref, () => ({
    updateTabsState: (status) => {
      const { tabsKey, mode } = status
      tabsKey && handleUpdateState('tabsKey', tabsKey)
      mode && handleUpdateState('locateMode', mode)
    },
  }))

  /**
   * 防止 pdfjs 内部相关异步操作产生 Transport destroyed 报错信息被 apm 捕获
   */
  const preventTransportDestroyedWarning = (e) => {
    if ((e.reason?.message || '').indexOf('Transport destroyed') !== -1) {
      e.preventDefault()
    }
  }

  useEffect(() => {
    handleUpdateState('isScroll', true)
    handleUpdateState('highlightKey', initHighlightKey)
  }, [initHighlightKey])

  useEffect(() => {
    handleUpdateState('boxes', cloneDeep(initBoxes) || [])
  }, [initBoxes])

  useEffect(() => {
    initFontParams && handleUpdateState('fontParams', initFontParams)
  }, [initFontParams])

  useEffect(() => {
    handleUpdateState('readOnly', readOnly)
    handleUpdateState('locateMode', initLocateMode)
    handleUpdateState('search', initSearch || ['', '', ''])
  }, [readOnly, initSearch, initLocateMode])

  useEffect(() => {
    handleUpdateState('sealCode', sealCode)
    handleUpdateState('signType', signType)
  }, [sealCode, signType])

  useEffect(() => {
    onSearchChange && onSearchChange(search)
  }, [search])

  useEffect(() => {
    onLocateModeChange && onLocateModeChange(locateMode)
  }, [locateMode])

  useEffect(() => {
    onFontParamsChange && onFontParamsChange(fontParams)
  }, [fontParams])

  useEffect(() => {
    window.addEventListener('unhandledrejection', preventTransportDestroyedWarning)
    return () => {
      window.removeEventListener('unhandledrejection', preventTransportDestroyedWarning)
    }
  }, [])

  useEffect(() => {
    // 判断多个 PDFViewer 组件存在时是否需要重复加载字体文件
    const isFontLoaded = fontDetector.detect('STHeiti')
    if (isFontLoaded) {
      return
    }
    // 字体预加载
    // const familyKeySet = ['STHeiti', 'STKaiti', 'STFangsong', 'STSong', 'YaHei']
    // TODO require 无法动态拼接字符串  后续看看能不能优化
    const familySet = [
      new FontFace('STHeiti', `url(${require('@/assets/fonts/STHeiti.ttf')})`),
      new FontFace('STKaiti', `url(${require('@/assets/fonts/STKaiti.ttf')})`),
      new FontFace('STFangsong', `url(${require('@/assets/fonts/STFangsong.ttf')})`),
      new FontFace('STSong', `url(${require('@/assets/fonts/STSong.ttf')})`),
      new FontFace('YaHei', `url(${require('@/assets/fonts/YaHei.ttf')})`),
    ]

    familySet.forEach((everyFont) => everyFont.load())
  }, [])

  return (
    <div className={classNames(styles.pdfEdit, readOnlyClass, extraClassName)}>
      <div className={classNames(styles.container, readOnlyClass)}>
        <DndProvider backend={HTML5Backend}>
          <PdfViewer
            key={file?.id}
            className={classNames(styles.viewer, readOnlyClass)}
            file={file}
            useVirtual={useVirtual}
            highlightTexts={highlightTexts}
            onBoxChange={onBoxChange}
            pageHeaderConfig={pageHeaderConfig}
            docLoaded={docLoaded}
            extendAction={extendAction}
            useActionSlot={useActionSlot}
          />
          {detailSlot && detailSlot(useStore)}
          {file && !readOnly && !detailSlot && (
            <ConfigDrawer className={styles.config} keyForm={keyForm} fontRef={fontRef} />
          )}
        </DndProvider>
      </div>
    </div>
  )
})

const PdfEditorWithStore = forwardRef<PdfEditorHandler, PdfEditorProps>((props, ref) => (
  <Provider>
    <PdfEdit {...props} ref={ref} />
  </Provider>
))

export default PdfEditorWithStore
