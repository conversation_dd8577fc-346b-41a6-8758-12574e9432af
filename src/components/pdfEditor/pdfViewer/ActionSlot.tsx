import React from 'react'
import { Tooltip } from 'antd'
import { FullscreenOutlined, FullscreenExitOutlined } from '@ant-design/icons'
import classNames from 'classnames'
import 'react-pdf/dist/esm/Page/TextLayer.css'

import style from './style.module.less'

interface IProps {
  extendAction?: React.ReactNode
  isFullscreen: boolean
  extraClassName?: string
  onClick?: (action: string) => void
}

const ActionSlot = ({ isFullscreen = false, onClick, extraClassName, extendAction }: IProps) => {
  const handleClick = (action) => () => {
    onClick && onClick(action)
  }

  return (
    <div className={classNames(style.iconArea, style.uploadWrapper, extraClassName)}>
      {extendAction}
      <Tooltip title={!isFullscreen ? '全屏' : '退出全屏'}>
        {!isFullscreen ? (
          <FullscreenOutlined onClick={handleClick('fullscreen')} className={style.icon} />
        ) : (
          <FullscreenExitOutlined onClick={handleClick('exitFullscreen')} className={style.icon} />
        )}
      </Tooltip>
    </div>
  )
}

export default ActionSlot
