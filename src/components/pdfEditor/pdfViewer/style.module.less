.sealVarBox {
  margin-right: 20px;
  position: relative;


  .pdfVarBoxClear {
    opacity: 0;
    position: absolute;
    right: 0;
    top: 0;
  }

  &:hover .pdfVarBoxClear {
    opacity: 1;
  }

  &.viewerUse {
    text-align: center;
    position: absolute;
    border-radius: 50px;
    cursor: move;
    margin: 0;
  }

  &.sign {
    background-color: red;
    height: 40px;
    background: url('@/assets/signature.png') center/contain no-repeat;
  }

  &.stamp {
    background-color: #27bc9a;
    border-radius: 50%;
    height: 100px;
    clip-path: circle(50px at center);

    &.enterprise {
      background: url('@/assets/stamp1.png') center/contain no-repeat;
    }

    &.hr {
      background: url('@/assets/stamp2.png') center/contain no-repeat;
    }
  }
}

.varBox {
  display: inline-block;
  padding: 5px 8px;
  margin-right: 10px;
  margin-bottom: 10px;
  cursor: move;
  text-align: center;
  background-color: white;
  border: 1px solid #d2d2d2;
  border-radius: 5px;
  color: #9d9d9d;

  &:hover {
    color: #27bc9a;
    border: 1px solid #38c0a3;
  }
}

.pdfDocumentContainer {
  flex-grow: 1;
  display: flex;
  position: relative;
  justify-content: center;
  flex: 7;
  background-color: #f0f2f5;

  &.readOnly {
    padding: initial;
  }

  .alignLine {
    height: 1px;
    border: 1px dashed #AAAAAA;
    width: 848px;
    display: block;
    position: absolute;
    transform: scaleY(.5);
  }



  .documentElement {
    position: relative;
    overflow: auto;

    &.documentLoading {
      opacity: 0;
      position: absolute;
    }

    .scrollContainer {
      height: 100% !important;
      overflow: hidden auto !important;
    }

    .normalScrollContainer {
      overflow: hidden auto !important;
    }
  }

  &.fullScreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    min-height: 100vh;
    max-height: 100vh;
    z-index: 1000;
  }

  .iconArea {
    right: 20px;
    top: 10px;
    position: absolute;
    height: unset !important;
    font-size: 14px;

    .icon {
      font-size: 20px;
    }
  }

  .sr {
    position: relative;
    display: inline-block;
    z-index: 99;
    color: transparent;
    background-color: rgba(217, 247, 43, .4);
  }


  .srg {
    position: absolute;
    top: 0px;
    left: 0px;
    display: inline-block;
    height: 166px;
    width: 166px;
  }

  .complete {
    position: relative;
  }

  .sgr {
    position: relative;
    display: inline-block;
    z-index: 99;
    color: transparent;
    background-color: rgba(245, 196, 0, .4);
  }

  .sgrg {
    position: absolute;
    top: 0px;
    left: 0px;
    display: inline-block;
    height: 40px;
    width: 95px;
  }

  .hl {
    display: inline-block;
    z-index: 99;
    color: transparent;
  }
}


.pdfVarBox {
  text-align: center;
  position: absolute;
  border: 1px solid #d2d2d2;
  border-radius: 5px;
  color: #9d9d9d;
  background-color: white;
  padding: 5px;
  cursor: move;
  min-width: max-content;

  &:hover {
    border-color: #38c0a3;
    color: #27bc9a;
  }

  &:hover .pdfVarBoxClear {
    opacity: 1;
    color: #27bc9a;
    border-color: #38c0a3;
  }

  &.readOnly {
    cursor: not-allowed;

    &:hover {
      border: 1px solid #d2d2d2;
      color: #9d9d9d;
    }
  }

  .pdfVarBoxClear {
    opacity: 0;
    position: absolute;
    right: -5px;
    top: -5px;
    cursor: pointer;
    font-size: 14px;
    background-color: white;
    border-radius: 50%;
  }
}

.loading {
  min-height: 50vh;
  max-height: 85vh;
  width: fit-content;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pageBox {
  position: relative;
  width: unset !important;

  &Bottom {
    margin-bottom: 10px;
  }

  .pageHeader {
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 5px;
    background-color: #c0c0c0;
    z-index: 1;
    padding: 5px 10px;
    color: #fff;
    letter-spacing: 2px;
    font-size: 14px;
    font-family: 'Courier New', Courier, monospace;
  }
}


.uploadConfirm {
  :global {
    .@{ant-prefix}-popover-message {
      align-items: unset;
    }
  }

  .title {
    font-weight: bold;
  }
}

.fullScreenModal {
  max-width: 100%;
  top: 0;
  padding-bottom: 0;
  overflow: hidden;

  .fullScreenBox {
    z-index: 999;
  }
}

.uploadWrapper {
  display: flex;
  align-items: center;
  float: right;
  position: relative;

  .choose {
    margin-top: 30px;
  }

  .desc {
    font-size: 14;
  }

  .tip {
    font-size: 12px;
    margin-top: 10px;
    color: #898989;
  }

  :global {
    .@{ant-prefix}-upload.@{ant-prefix}-upload-drag {
      width: 595px;
      height: 400px;
      border-radius: 10px;
    }
  }
}