import React, { useState, useEffect, useMemo, useRef } from 'react'
import { Spin } from 'antd'
import { Document } from 'react-pdf/dist/esm/entry.webpack5'
import 'react-pdf/dist/esm/Page/TextLayer.css'
import classNames from 'classnames'
import { VariableSizeList as List } from 'react-window'
import update from 'immutability-helper'
import ViewerPage from './ViewerPage'
import { useCreateStore } from '../context'
import ActionSlot from './ActionSlot'
import { SCALE } from '../common/constant'
import { getScrollbarWidth } from '../common/utils'
import { HighLightTextProps, FileProps } from '../common/types'

import style from './style.module.less'

const scrollbarWidth = getScrollbarWidth()

interface PageViewportProps {
  height: number
  width: number
  [key: string]: number | string
}
interface IProps {
  className?: string
  file: FileProps
  useVirtual?: boolean
  extendAction?: React.ReactNode
  useActionSlot?: boolean
  onBoxChange?: (value: any) => void
  highlightTexts?: HighLightTextProps[]
  pageHeaderConfig?: {
    total: number
    preTotal: number
    pageIndex: number
  }
  docLoaded?: (num: number, pageIndex?: number) => void
}

const PdfViewer = ({
  file,
  useVirtual,
  useActionSlot,
  className,
  onBoxChange,
  extendAction,
  highlightTexts,
  pageHeaderConfig,
  docLoaded,
}: IProps) => {
  const useStore = useCreateStore()
  const handleUpdateState = useStore((state) => state.updateState)
  const activeNode = useStore((state) => state.activeNode)

  const [numOfPages, setNumOfPages] = useState(null)
  const [fullScreen, setFullScreen] = useState(false)
  const [pageViewPorts, setPageViewPorts] = useState<PageViewportProps[]>([])
  const [pdfDocument, setPdfDocument] = useState<any>(null)
  const [hasPageRendered, setHasPageRendered] = useState(false)
  const [scrollContainer, setScrollContainer] = useState<any>(null)

  const isRendered = useRef(false)

  /**
   * pdf 加载完成
   */
  const onDocumentLoadSuccess = async (pdfObj) => {
    try {
      // 是否渲染过
      isRendered.current = true
      // 延迟防止重渲染闪烁
      setTimeout(() => {
        setHasPageRendered(true)
      }, 300)

      setPdfDocument(pdfObj)
      const { numPages } = pdfObj
      setNumOfPages(numPages)
      docLoaded?.(numPages, pageHeaderConfig?.pageIndex)

      const pagePromises: Array<any> = []

      for (let i = 1; i <= numPages; i++) {
        pagePromises.push(pdfObj.getPage(i))
      }

      const pages = await Promise.all(pagePromises)
      const textContents = await Promise.all(pages.map((page) => page.getTextContent()))
      const content = textContents.map((textContent, index) => ({
        page: index,
        content: textContent.items.reduce((prev, next) => prev + next.str, ''),
        rawContent: textContent.items,
      }))
      handleUpdateState('content', content)
      // eslint-disable-next-line no-empty
    } catch (e) {}
  }

  // 上传和全屏
  const onClick = (action) => {
    if (action === 'fullscreen') {
      setFullScreen(true)
    } else if (action === 'exitFullscreen') {
      setFullScreen(false)
    }
  }

  const asyncMap: (arr: any[], fn: any) => Promise<any> = (arr, fn) =>
    new Promise((resolve, reject) => {
      Promise.all(arr.map(fn)).then(resolve).catch(reject)
    })

  const getPageHeight = (pageIndex) => {
    if (!pageViewPorts) {
      throw new Error('getPageHeight 调用太早了')
    }

    return pageViewPorts[pageIndex]?.height
  }

  useEffect(() => {
    setPageViewPorts([])

    if (pdfDocument) {
      // 获取每一页的高度，宽度等信息
      ;(async () => {
        const pageNumbers = Array.from(new Array(numOfPages)).map((_, index) => index + 1)

        const nextPageViewPorts: PageViewportProps[] = await asyncMap(pageNumbers, (pageNumber) =>
          pdfDocument.getPage(pageNumber).then((page) =>
            page.getViewport({
              scale: SCALE,
            }),
          ),
        )
        setPageViewPorts(nextPageViewPorts)
        handleUpdateState('pageHeight', nextPageViewPorts[0]?.height)
      })()
    }
  }, [pdfDocument])

  useEffect(() => {
    // 除了第一次渲染，更改文件造成渲染以后要重置定位模式
    if (isRendered.current) {
      handleUpdateState('locateMode', 'CM_LM_002')
    }
  }, [file])

  const SplitElement = (
    <div
      style={{ height: '1px', backgroundColor: '#c7c7c7', position: 'relative', zIndex: '99' }}
    />
  )

  // 虚拟滚动行列
  const createVirtualRow = () => (props: any) =>
    (
      <div style={props.style} className={style.pageBox}>
        {props.index !== 0 && SplitElement}
        {pageHeaderConfig && (
          <span className={style.pageHeader}>{`${pageHeaderConfig.preTotal + props.index + 1}/${
            pageHeaderConfig.total
          }`}</span>
        )}
        <ViewerPage
          highlightTexts={highlightTexts}
          viewParams={pageViewPorts[props.index]}
          pageNumber={props.index}
          onChange={onBoxChange}
        />
      </div>
    )

  // 正常行列
  const createNormalRow = () => {
    return (
      <div className={style.normalScrollContainer}>
        {Array.from(new Array(numOfPages), (el, index) => (
          <div className={classNames(style.pageBox)} key={index}>
            {index !== 0 && SplitElement}
            {pageHeaderConfig && (
              <span className={style.pageHeader}>{`${pageHeaderConfig.preTotal + index + 1}/${
                pageHeaderConfig.total
              }`}</span>
            )}
            <ViewerPage
              highlightTexts={highlightTexts}
              viewParams={pageViewPorts[index]}
              pageNumber={index}
              onChange={onBoxChange}
            />
          </div>
        ))}
      </div>
    )
  }

  const ScrollElement = () =>
    useVirtual ? (
      <List
        innerRef={(ref) => setScrollContainer(ref)}
        className={style.scrollContainer}
        width={pageViewPorts[0].width + scrollbarWidth}
        height={pageViewPorts[0].height}
        estimatedItemSize={pageViewPorts[0].height}
        itemCount={numOfPages}
        itemSize={(index) => getPageHeight(index)}
      >
        {createVirtualRow()}
      </List>
    ) : (
      createNormalRow()
    )

  // 防止状态变更重渲染，导致 document 闪一下
  const DocumentElement = useMemo(() => {
    return (
      <Document
        className={classNames(style.documentElement, hasPageRendered ? '' : style.documentLoading)}
        file={file.data}
        onLoadSuccess={onDocumentLoadSuccess}
      >
        {pageViewPorts.length > 0 ? ScrollElement() : null}
        {file?.data && useActionSlot ? (
          <ActionSlot extendAction={extendAction} isFullscreen={fullScreen} onClick={onClick} />
        ) : null}
      </Document>
    )
  }, [file, numOfPages, fullScreen, pageHeaderConfig, pageViewPorts, hasPageRendered])

  const onKeydown = (event) => {
    const moveKeys = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight']
    const deleteKeys = ['Backspace', 'Delete']
    if (![...moveKeys, ...deleteKeys].includes(event.key) || !activeNode) {
      return
    }

    if (deleteKeys.includes(event.key) && activeNode) {
      handleUpdateState('boxes', ({ boxes }) =>
        update(boxes, {
          $splice: [[activeNode.index, 1]],
        }),
      )
      handleUpdateState?.('activeNode', null)
    } else {
      handleUpdateState('boxes', ({ boxes }) => {
        const current = boxes[activeNode.index]

        const moveMap = {
          ArrowUp: {
            top: current.top - 1,
          },
          ArrowDown: {
            top: current.top + 1,
          },
          ArrowLeft: {
            left: current.left - 1,
          },
          ArrowRight: {
            left: current.left + 1,
          },
        }

        const newPdfBoxes = update(boxes, {
          [activeNode.index]: {
            $merge: moveMap[event.key],
          },
        })
        return newPdfBoxes
      })
    }

    if (event.preventdefault) {
      event.preventdefault()
    } else {
      // eslint-disable-next-line no-param-reassign
      event.returnValue = false
    }
  }

  useEffect(() => {
    handleUpdateState('scrollContainer', scrollContainer)
  }, [scrollContainer])

  useEffect(() => {
    document.addEventListener('keydown', onKeydown)
    return () => document.removeEventListener('keydown', onKeydown)
  }, [activeNode])

  return (
    <div
      className={classNames(
        style.pdfDocumentContainer,
        fullScreen ? style.fullScreen : '',
        className,
      )}
    >
      {DocumentElement}
      {!hasPageRendered ? (
        <div className={style.loading}>
          <Spin />
        </div>
      ) : null}
    </div>
  )
}

export default PdfViewer
