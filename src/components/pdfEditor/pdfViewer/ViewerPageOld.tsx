import React, { useRef, useCallback, useEffect, useMemo, useState } from 'react'
import { useDrop } from 'react-dnd'
import { cloneDeep } from 'lodash-es'
import { Page } from 'react-pdf/dist/esm/entry.webpack5'
import update from 'immutability-helper'
import stampImg1 from '@/assets/stamp1.png'
import stampImg2 from '@/assets/stamp2.png'
import signatureImg from '@/assets/signature.png'
import classNames from 'classnames'
import { useCreateStore } from '../context'
import styles from './style.module.less'
import { VarBoxInView, SealBoxInView } from '../components'
import { SCALE, VAR_TYPE, DRAG_BOX } from '../common/constant'
import { HighLightTextProps } from '../common/types'

interface IProps {
  pageNumber?: number
  viewParams: any
  highlightTexts?: HighLightTextProps[]
  onChange?: (value: any) => void
  onLoad?: () => void
}

const ViewerPage = ({ onChange, onLoad, viewParams = {}, pageNumber = 0 }: IProps) => {
  const pageProxy = useRef<any>(null)
  const pdfPageRef = useRef<any>(null)
  const useStore = useCreateStore()

  const search = useStore((state) => state.search)
  const boxes = useStore((state) => state.boxes)
  const sealCode = useStore((state) => state.sealCode)
  const locateMode = useStore((state) => state.locateMode)
  const readOnly = useStore((state) => state.readOnly)
  const handleUpdateState = useStore((state) => state.updateState)
  const documentContent = useStore((state) => state.content)
  const linePosition = useStore((state) => state.linePosition)

  const [firstSearchIndex, setFirstSearchIndex] = useState<any>([])
  const [secondSearchIndex, setSecondSearchIndex] = useState<any>([])

  /**
   * pdf 内部的 box 移动更新位置
   */
  const moveBox = useCallback(
    ({ index, ...restProps }) => {
      const newPdfBoxes = update(boxes, {
        [index]: {
          $merge: restProps,
        },
      })

      handleUpdateState('boxes', newPdfBoxes)
      onChange && onChange(newPdfBoxes)
    },
    [boxes, onChange],
  )

  /**
   * 将普通坐标转换成 pdf 坐标
   */
  const getViewport = () => {
    const viewport = pageProxy.current?.getViewport({
      scale: SCALE,
    })
    return viewport
  }

  const isSealLocateMode = useMemo(() => locateMode === 'CM_LM_001', [locateMode])

  const [, pdfBoxDrop] = useDrop(
    () => ({
      accept: DRAG_BOX,
      drop: async (item: any, monitor) => {
        const { x, y } = monitor.getClientOffset() || {}

        const viewport = getViewport()
        const pdfViewerRect = pdfPageRef.current?.getBoundingClientRect()

        // 从配置面板拖动过来后，需要将配置面板中的数据同步到 pdfBoxes 中
        if (item?.source === VAR_TYPE.NORMAL_BOX || item?.source === VAR_TYPE.SEAL_BOX) {
          const name = item?.name
          const _top = Math.round(item.top + y - (pdfViewerRect?.y || 0))
          const _left = Math.round(item.left + x - (pdfViewerRect?.x || 0))

          // PDF 坐标是以左下角为原点，所以需要进行坐标转换
          const nexBoxes = [...cloneDeep(boxes)].concat({
            title: name,
            top: _top,
            left: _left,
            viewport,
            page: pageNumber,
            code: item.code,
            type: item.type,
            sealType: item.sealType,
            move: true,
          })
          handleUpdateState('boxes', nexBoxes)
        } else {
          const delta = monitor.getDifferenceFromInitialOffset()
          const offsetTop = Math.round(item.top + (delta?.y || 0))

          const left = Math.round(item.left + (delta?.x || 0))
          const top = offsetTop > 0 ? offsetTop % viewport.height : viewport.height + offsetTop

          moveBox({
            index: item.index,
            left,
            top,
            viewport,
            move: true,
            page: pageNumber,
          })
        }
        return undefined
      },
    }),
    [boxes, pageNumber],
  )

  const calcStampShouldBeThere = () => {
    const stampNodeList = document.querySelectorAll(`.${styles.sr}.${styles.complete}`)
    const signatureNodeList = document.querySelectorAll(`.${styles.sgr}.${styles.complete}`)
    ;[...stampNodeList, ...signatureNodeList].forEach((node) => {
      /**
       * 关键字定位的印章以及签名会受 pdf.js 内部渲染的文字元素的 transform 影响，导致变宽或者变高
       * 需要通过获取该 transform scale 的值做一下还原
       */
      const parentComputedStyle = node.parentElement
        ? window.getComputedStyle(node.parentElement)
        : {}

      let scaleValueX = 1
      let scaleValueY = 1
      if (Object.keys(parentComputedStyle).length > 0) {
        const transformValue = (parentComputedStyle as CSSStyleDeclaration).getPropertyValue(
          'transform',
        )
        const matrixParams = transformValue.match(/matrix\(([^)]+)\)/)?.[1]
        // 0 和 3 的值分别是 translateX 和 translateY 的值
        const valueX = Number(matrixParams?.split(',')[0])
        const valueY = Number(matrixParams?.split(',')[3])

        scaleValueX = Number.isNaN(valueX) ? 1 : 1 / valueX
        scaleValueY = Number.isNaN(valueY) ? 1 : 1 / valueY
      }

      // 再根据文字渲染层框、高计算需要动态偏移的距离，让它定位到关键字正中间
      const nodeParams = node.getBoundingClientRect()
      const icon = node.childNodes[0] as HTMLElement
      const height = nodeParams.height * scaleValueY
      const width = nodeParams.width * scaleValueX

      const left = `calc(-${(icon.clientWidth / 2 / width) * 100}% + ${width / 2}px)`
      const top = `calc(-${(icon.clientHeight / 2 / height) * 100}% + ${height / 2}px)`
      icon.style.left = left
      icon.style.top = top
      icon.style.transform = `scale(${scaleValueX},${scaleValueY})`
    })
  }

  const onPageLoadSuccess = (_page: any) => {
    pageProxy.current = _page
    onLoad && onLoad()
  }

  const findAllOccur = (str, searchStr) => {
    if (!searchStr || !str) {
      return []
    }
    const indices: Array<[number, number]> = []
    let idx = str.indexOf(searchStr)
    while (idx !== -1) {
      indices.push([idx, idx + searchStr.length])
      idx = str.indexOf(searchStr, idx + 1)
    }
    return indices
  }

  const findTextByIndexes = (indexesArray, textArray) => {
    const resultArray: any[] = []
    for (let i = 0; i < indexesArray.length; i++) {
      const [startIndex, endIndex] = indexesArray[i]
      let remainingStartIndex = startIndex
      let remainingEndIndex = endIndex - 1

      for (let j = 0; j < textArray.length; j++) {
        const { str, transform } = textArray[j]
        const textLength = str.length

        if (remainingStartIndex >= textLength) {
          remainingStartIndex -= textLength
          remainingEndIndex -= textLength
        } else if (remainingEndIndex < textLength) {
          const foundText = str.substring(remainingStartIndex, remainingEndIndex + 1)
          resultArray.push({ text: foundText, transform: transform.join(',') })
          break
        } else {
          const foundText = str.substring(remainingStartIndex)
          resultArray.push({ text: foundText, transform: transform.join(',') })
          remainingStartIndex = 0
          remainingEndIndex -= textLength
        }
      }
    }

    return resultArray
  }

  const getSearchOccurIndex = () => {
    const pageContent = documentContent.find((v) => v.page === pageNumber)?.content
    const rawPageContent = documentContent.find((v) => v.page === pageNumber)?.rawContent
    const search1FindIndex = search[0] ? findAllOccur(pageContent, search[0]) : []
    const search2FindIndex = search[1] ? findAllOccur(pageContent, search[1]) : []
    const searchContent1 = findTextByIndexes(search1FindIndex, rawPageContent)
    const searchContent2 = findTextByIndexes(search2FindIndex, rawPageContent)
    setFirstSearchIndex(searchContent1)
    setSecondSearchIndex(searchContent2)
  }

  /**
   *
   * @param text 当前循环的渲染文本
   * @param current 当前符合渲染文本的子字符串
   * @param type 类型
   * @returns 渲染过的文本
   */
  const highlightText = (text, current, type) => {
    const searchText = current.text
    const rawSearch = type === 1 ? search[0] : search[1]
    // 如果当前匹配的字符串不是完整的  则不需要盖章、签名，只需要高亮
    const isComplete = searchText.length === rawSearch.length

    const imgElement =
      type === 1
        ? `<img class="${styles.srg}" src="${sealCode === 'CM_RS_001' ? stampImg2 : stampImg1}"/>`
        : `<img class="${styles.sgrg}" src="${signatureImg}"/>`

    const replaceText = isComplete
      ? `<div class="${
          type === 1
            ? classNames(styles.sr, styles.complete)
            : classNames(styles.sgr, styles.complete)
        }">${imgElement}${searchText}</div>`
      : `<div class="${type === 1 ? styles.sr : styles.sgr}">${searchText}</div>`

    const newText = text.replace(searchText, () => replaceText)

    return newText
  }

  useEffect(() => {
    onChange && onChange(boxes)
  }, [boxes])

  useEffect(() => {
    getSearchOccurIndex()
  }, [search, documentContent])

  return (
    <div
      ref={(ref) => {
        pdfPageRef.current = ref
        return pdfBoxDrop(ref)
      }}
    >
      <Page
        customTextRenderer={({ str, transform }) => {
          if (isSealLocateMode) {
            return str
          }

          const unique = transform.join(',')
          const current1 = firstSearchIndex.find((v) => v.transform === unique)
          const current2 = secondSearchIndex.find((v) => v.transform === unique)

          if (!current1 && !current2) {
            return str
          }

          let text = str

          if (current1) {
            text = highlightText(text, current1, 1)
          }

          if (current2) {
            text = highlightText(text, current2, 2)
          }

          return text
        }}
        key={`page_${pageNumber + 1}`}
        scale={SCALE}
        pageNumber={pageNumber + 1}
        renderTextLayer
        renderAnnotationLayer={false}
        onLoadSuccess={onPageLoadSuccess}
        onRenderTextLayerSuccess={() => calcStampShouldBeThere()}
      />
      {Object.keys(boxes)
        .filter((key) => {
          const isCurrentPage = boxes[key].page === pageNumber
          return isCurrentPage && (isSealLocateMode || !boxes[key].type)
        })
        .map((key) => {
          const { title, content, ...restProps } = boxes[key]

          return restProps.type ? (
            <SealBoxInView
              key={key}
              viewParams={viewParams}
              index={Number(key)}
              readOnly={readOnly}
              {...restProps}
            />
          ) : (
            <VarBoxInView
              key={key}
              readOnly={readOnly}
              viewParams={viewParams}
              index={Number(key)}
              {...restProps}
            >
              {content || `#${title}#`}
            </VarBoxInView>
          )
        })}
      {linePosition.page === pageNumber && linePosition.position ? (
        <span className={styles.alignLine} style={{ top: linePosition.position }} />
      ) : null}
    </div>
  )
}

export default ViewerPage
