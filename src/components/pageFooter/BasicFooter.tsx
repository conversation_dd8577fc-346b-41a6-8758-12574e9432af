import React, { HTMLProps, SyntheticEvent } from 'react'
import { Button, ButtonProps, Modal } from 'antd'
import { Submit } from '@amazebird/antd-schema-form'

import styles from './style.module.less'

interface IProps {
  showCancel?: boolean
  showConfirm?: boolean
  cancelProps?: ButtonProps
  confirmProps?: ButtonProps
  confirmText?: string
  cancelText?: string
  loading?: boolean
  // 是否使用 schema 的 Submit 组件进行提交
  useSchemaSubmit?: boolean
  modalTitle?: string
  // 是否展现modal提示，可以是一个判断函数
  showModal?: boolean | (() => boolean)
  footer?: ((confirmDom: any, cancelDom: any) => JSX.Element) | null
  handleConfirm?: (e?: SyntheticEvent | any) => void
  handleCancel?: (e?: SyntheticEvent) => void
}

const BasicFooter: React.FC<IProps & HTMLProps<HTMLDivElement>> = ({
  showCancel = true,
  showConfirm = true,
  cancelProps = {},
  confirmProps = {},
  loading = false,
  cancelText,
  confirmText,
  useSchemaSubmit = true,
  modalTitle = '项目内容未提交，确认要取消吗？',
  showModal = false,
  handleCancel,
  handleConfirm,
  footer,
  ...props
}) => {
  const SubmitButton = () =>
    useSchemaSubmit ? (
      <Submit
        loading={loading}
        onFinish={handleConfirm}
        className={styles.confirm}
        type="primary"
        {...confirmProps}
      >
        {confirmText || '保存'}
      </Submit>
    ) : (
      <Button
        loading={loading}
        onClick={handleConfirm}
        className={styles.confirm}
        type="primary"
        {...confirmProps}
      >
        {confirmText || '保存'}
      </Button>
    )

  const CancelButton = () => (
    <Button
      className={styles.cancel}
      onClick={() => {
        const visible = typeof showModal === 'function' ? showModal() : showModal
        if (visible) {
          Modal.confirm({
            title: modalTitle,
            okText: '确定',
            cancelText: '取消',
            onOk: handleCancel,
          })
        } else {
          handleCancel?.()
        }
      }}
      {...cancelProps}
    >
      {cancelText || '取消'}
    </Button>
  )

  return (
    <div className={styles.footer} {...props}>
      {footer ? (
        footer(CancelButton(), SubmitButton())
      ) : (
        <>
          {showCancel ? CancelButton() : null}
          {showConfirm ? SubmitButton() : null}
        </>
      )}
    </div>
  )
}

export default BasicFooter
