import React, { useState } from 'react'
import { Mo<PERSON>, Button, message } from 'antd'
import { debounce } from 'lodash'
import { uploadServiceApi } from '@/service/fileService'
import UploadComponent from './UploadComponent'
import ErrorContent from './Error'
import Success from './Success'

import styles from './style.module.less'

interface IProps {
  /** 是否显示 */
  visible: boolean
  /** 关闭函数 */
  close: () => void
  /** modal标题 */
  title: string
  /** modal确认操作 */
  onOk: (values) => Promise<any>
  /** 下载模板函数 */
  downloadFn: () => Promise<any>
  /** 下载模板名称 */
  downloadName: string
  /** 是否需要成功和错误弹框 */
  needModal?: boolean
  /** 导入成功的回掉 */
  successFn?: () => void
  [field: string]: any
}

const UploadModal = (props: IProps) => {
  const {
    visible,
    close,
    onOk: okClick,
    downloadFn,
    downloadName = '导入模板',
    title = '导入',
    needModal = true,
    successFn,
  } = props
  const [width, setWidth] = useState<number>(800)
  const [loading, setLoading] = useState(false)
  const [downloading, setDownloading] = useState(false)
  const [contentType, setContentType] = useState<string>('normal')
  const [result, setResult] = useState<any>({})
  const [files, setFiles] = useState<any>()

  // 下载模板
  const downTemplate = debounce(() => {
    setDownloading(true)
    const apiRes: any = downloadFn?.()
    if (apiRes) {
      apiRes?.finally(() => {
        setDownloading(false)
      })
    }
  }, 500)

  const onOk = async () => {
    // 将表单数据以及关闭函数抛出去
    // TODO BUG修改暂时这么改
    // const values = await form.validateFields()
    // const { file } = values
    try {
      setLoading(true)
      const { name = '', originFileObj } = files
      const fileId = await uploadServiceApi(originFileObj)

      const { data: resultData } = await okClick({
        fileId,
        fileName: name,
      })

      if (resultData?.data) {
        const { isSuccess, ...rest } = resultData.data
        if (needModal) {
          setResult(rest)
          setContentType(isSuccess ? 'success' : 'error')
          // eslint-disable-next-line no-nested-ternary
          setWidth(isSuccess ? (rest?.number ? 500 : 400) : 600)
        } else {
          close()
          successFn?.()
        }
      }
    } finally {
      setLoading(false)
    }
  }

  // 导入期间不能关闭
  const onCancel = () => {
    if (loading) {
      message.info('正在导入数据，请在导入完成后关闭')
    } else {
      close()
    }
  }

  const footerRender = () => (
    <div
      style={{
        width: '100%',
        display: 'flex',
        justifyContent: 'end',
      }}
    >
      {contentType === 'normal' ? (
        <Button onClick={onCancel}>关闭</Button>
      ) : (
        <Button type="primary" onClick={close}>
          知道了
        </Button>
      )}
    </div>
  )

  return (
    <Modal
      // eslint-disable-next-line no-nested-ternary
      title={contentType === 'normal' ? title : contentType === 'success' ? false : '导入失败'}
      footer={contentType === 'success' ? null : footerRender()}
      onCancel={onCancel}
      visible={visible}
      confirmLoading={loading}
      width={width}
      wrapClassName={styles.modalWrap}
      closable={contentType !== 'success'}
      maskClosable={false}
    >
      {contentType === 'normal' && (
        <div className={styles.content}>
          <div className={styles.form}>
            <div className={styles.item}>
              <span className={styles.label}>下载模板</span>
              <Button
                type="link"
                style={{
                  padding: 0,
                }}
                onClick={downTemplate}
                loading={downloading}
              >
                {downloadName}
              </Button>
            </div>
            <div className={styles.item}>
              <span className={styles.label}>上传文件</span>
              <UploadComponent value={files} onChange={setFiles} />
            </div>
            <div className={styles.item}>
              <span className={styles.label}>导入</span>
              <Button type="primary" onClick={onOk} disabled={!files} loading={loading}>
                开始导入
              </Button>
            </div>
          </div>
          <div className={styles.tip}>
            注意事项：
            <div className={styles.content}>
              <div className={styles.red}>
                1.
                导入线下签订的合同，可先下载模板填写主要信息，再将人员实际签署文件（pdf格式）一并压缩上传。
              </div>
              <div className={styles.red}>
                2.
                上传文件只能选择zip格式的压缩包文件。压缩包中需要包含合同导入模板、以及签署文件，导入成功后系统将会根据模板第一个SHEET页信息，结合压缩包中的签署文件生成签署数据。
              </div>
              <div>
                ①
                不能删除、修改模板第一行的字段，不能改变字段列的顺序、增加其他辅助字段（辅助字段不会导入）
              </div>
              <div>
                ② 模板数据之间不能有空行（存在空行时仅会导入空行以上的数据，空行以下的数据不会导入）
              </div>
              <div>
                3.
                开始导入后系统会进行校验，当存在不满足条件的记录时，系统会返回所有的错误信息，并阻止本次导入操作；只有当所有记录都满足校验条件时，才可导入成功。
              </div>
              <div className={styles.red}>4. 导入未完成时，请勿关闭此对话框！</div>
            </div>
          </div>
        </div>
      )}
      {contentType === 'success' && (
        <Success
          close={() => {
            close()
            successFn?.()
          }}
          data={result}
        />
      )}
      {contentType === 'error' && <ErrorContent data={result} />}
    </Modal>
  )
}

export default UploadModal
