import React from 'react'
import { But<PERSON>, Tooltip, Upload } from 'antd'

import styles from './style.module.less'

interface IProps {
  value?: any
  onChange: (value) => void
}

const UploadComponent = (props: IProps) => {
  const { value = {}, onChange } = props
  return (
    <div className={styles.uploadContent}>
      <Upload
        customRequest={() => {}}
        showUploadList={false}
        listType="text"
        onChange={(values) => onChange(values.file)}
        // accept=".zip"
      >
        <Button type="primary">选择压缩包文件</Button>
      </Upload>
      <Tooltip title={value.name}>
        <span className={styles.name}>{value.name}</span>
      </Tooltip>
    </div>
  )
}

export default UploadComponent
