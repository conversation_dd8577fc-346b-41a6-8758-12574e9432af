import React from 'react'
import { Button } from 'antd'
import { CheckCircleFilled } from '@ant-design/icons'
import { millisecondsToDate } from '@/utils/utils'

import styles from './style.module.less'

interface IProps {
  close: () => void
  data: any
}

const SuccessRender = (props: IProps) => {
  const { close, data } = props
  const { number, cost, successNumber, failNumber, failTaskNames = [] } = data
  return (
    <div className={styles.success}>
      <div className={styles.title}>
        <CheckCircleFilled className={styles.icon} />
        <div className={styles.name}>导入成功</div>
      </div>
      <div className={styles.content}>
        <div>
          {`导入${failNumber ? '成功' : ''}${
            number || successNumber || 0
          }条，用时：${millisecondsToDate(cost)}`}
        </div>
        {failNumber && (
          <>
            <div>{`导入失败${failNumber}条：因任务状态无法导入，以下任务盘点对象导入失败：`}</div>
            <div>{`${failTaskNames.join('、')}共${failTaskNames.length}个任务`}</div>
          </>
        )}
      </div>
      <div className={styles.footer}>
        <Button type="primary" onClick={close} className={styles.btn}>
          知道了
        </Button>
      </div>
    </div>
  )
}

export default SuccessRender
