import React from 'react'
import { SchemaTable } from '@amazebird/antd-schema-table'
import { millisecondsToDate } from '@/utils/utils'

import styles from './style.module.less'

interface IProps {
  data: any
}

const Error = (props: IProps) => {
  const { data } = props
  const { importFailDetails = [], number, cost } = data
  const dataSource = importFailDetails.map((detail, index) => ({
    ...detail,
    index,
  }))

  const columns = [
    {
      title: '序号',
      width: 10,
      key: 'index',
      cell: ({ record }) => record.index + 1,
    },
    {
      title: '错误信息',
      dataIndex: 'failMessage',
      formComponent: 'Input',
      cell: 'Text',
      hideInSearch: true,
    },
    {
      title: '错误字段',
      width: 50,
      dataIndex: 'failField',
      formComponent: 'Input',
      cell: 'Text',
      hideInSearch: true,
    },
  ]
  return (
    <>
      <p className={styles.tip}>{`导入失败${number}条，用时：${millisecondsToDate(cost)}`}</p>
      <SchemaTable
        rowKey="failMessage"
        stripe={false}
        tableClassName="custom-table-class-name"
        dataSource={dataSource}
        columns={columns}
        pagination={{
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total) => `总共 ${total} 个项目`,
        }}
        footer={() => null}
      />
    </>
  )
}

export default Error
