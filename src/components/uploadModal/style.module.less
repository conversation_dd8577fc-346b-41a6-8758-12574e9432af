.errorModal {
  .tip {
    font-size: 12px;
    margin-bottom: 20px;
  }

  :global {
    .@{ant-prefix}-modal-body {
      padding: 10px 24px;
    }

    .@{ant-prefix}-card-body {
      padding: 0;
    }

    .@{ant-prefix}-modal-footer {
      .@{ant-prefix}-btn:nth-of-type(1) {
        display: none;
      }
    }
  }
}

.modalWrap {
  :global {
    .@{ant-prefix}-form-item-explain-error {
      white-space: nowrap;
    }
  }
}

.success {
  .title {
    display: flex;
    .icon {
      font-size: 28px;
      color: #52C41A;
      margin-right: 8px;
    }
    .name {
      font-size: 18px;
      font-weight: 500;
    }
  }
  .content {
    margin-left: 37px;
    margin-top: 24px;
    max-height: 150px;
    overflow-y: auto;
  }
  .footer {
    width: 100%;
    display: flex;
    justify-content: end;
    margin-top: 24px;
  }
}

.content {
  .form {
    padding: 0 24px 24px;
    .item {
      margin-bottom: 24px;
      display: flex;
      align-items: center;
      .label {
        display: inline-block;
        width: 100px;
        direction: rtl;
        margin-right: 24px;
      }
    }
  }
  .tip {
    .content {
      padding: 0 24px 24px;
    }
  }
}

.red {
  color: red;
}

.uploadContent {
  display: flex;
  align-items: center;
  .name {
    display: inline-block;
    max-width: 400px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-left: 8px;
    margin-top: 8px;
  }
}
