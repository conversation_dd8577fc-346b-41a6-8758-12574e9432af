// import 'babel-polyfill' // 兼容性代码
import 'normalize.css' // css样式重制
import React, { useEffect } from 'react'
import 'moment/locale/zh-cn'
import uc from '@galaxy/uc'
import { init as rbacInit, getEntity } from '@galaxy/rbac'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/lib/locale/zh_CN'
import { init as orgInit } from '@galaxy/org-selector'
import { init as userInit } from '@galaxy/user-selector'
import '@amazebird/antd-field'
import init from '@galaxy/foundation'
import { init as dictInit } from '@galaxy/dict'
import { init as uploadInit } from '@galaxy/upload'
import GalaxyConfigProvider from '@galaxy/config-provider'
import dayjs from 'dayjs'
import { isEmpty } from 'lodash-es'
import config from '@/config'
import AppRouter from '@/router'
import ErrorBoundary from '@/components/base/ErrorBoundary'
import useCreateStore from './stores/useCreateStore'
import './utils/request'
// Theme
import './themes/theme.less'
// 引入样式文件
import './themes/theme-main.less'
import 'dayjs/locale/zh-cn'
import { pdfjs } from 'react-pdf/dist/esm/entry.webpack5'
import pdfjsWorker from 'react-pdf/dist/cjs/pdf.worker.entry'
import '@pupu/brick/dist/index.css'

pdfjs.GlobalWorkerOptions.workerSrc = pdfjsWorker

dayjs.locale('zh-cn')

const { environment, serverUrl } = config

GalaxyConfigProvider.config({
  env: environment,
})

ConfigProvider.config({
  prefixCls: 'contract',
})

uc.init({
  env: config.environment,
})

rbacInit({
  env: config.environment,
})

// 初始化问卷
init({
  env: environment,
  origin: `${serverUrl}/user_evaluation`,
})

// 初始化组织选择器环境
orgInit({
  env: environment,
})

// 初始化人员选择器环境
userInit({
  env: environment,
})

// 初始化字典组件环境
dictInit({
  env: config.environment,
  scope: 'CONTRACT',
})

// 上传 SDK 初始化
uploadInit({
  env: config.environment,
})

const APP = function () {
  const container = document.querySelector('#microRoot') as HTMLElement
  const setPermissions = useCreateStore((state: any) => state.setPermissions)

  useEffect(() => {
    const entity = getEntity()
    const authorization = uc.getAuthHeader()
    if (!isEmpty(entity) && !isEmpty(authorization)) {
      // 有实体权限和token的时候才去获取所有按钮的权限
      // 获取按钮权限
      setPermissions()
    }
  }, [])

  return (
    <ErrorBoundary>
      <ConfigProvider locale={zhCN} getPopupContainer={() => container} prefixCls="contract">
        <AppRouter />
      </ConfigProvider>
    </ErrorBoundary>
  )
}

export default APP
