import { isMicroApp } from './constants/index'
import defaultConfig from '../config'

const isMultiVersion = window.location.pathname.indexOf('/multi-version') > -1
const prefix = isMultiVersion ? '/multi-version' : ''
const multiRoute = isMultiVersion ? '/contract' : ''
const suffix = isMultiVersion ? `/${defaultConfig.BUILD_VERSION}` : ''

const config = {
  ...defaultConfig,
  baseRoute: prefix + (isMicroApp ? defaultConfig.baseRoute : multiRoute) + suffix,
}
export default config
