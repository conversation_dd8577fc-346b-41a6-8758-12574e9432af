import { IOperateLog } from '@/types/operateLog'
import { IPage } from '@/types/search'
import { req } from '@/utils/request'
import { downloadExcelFile } from '@/utils/utils'

import type { AxiosResponse } from 'axios'
import type { ActionContext } from '@galaxy/business-request'

const services: ActionContext = req

// 分页查询操作日志
export const getOperateLogPage = (
  params: Partial<IOperateLog> & IPage,
): Promise<AxiosResponse<any, any>> => services.get('/operation_logs', params)

// 查询功能模块
export const getFunctionModules = (): Promise<AxiosResponse<any, any>> =>
  services.get('/operation_logs/function_modules')

// 获取用户行为列表
export const getUserBehaviors = (): Promise<AxiosResponse<any, any>> =>
  services.get('/operation_logs/user_behaviors')

// 下载附件
export const downloadAccessory = (id: number): Promise<unknown> =>
  services
    .request({
      url: `/operation_logs/download/accessory/${id}`,
      method: 'GET',
      responseType: 'blob',
    })
    .then((res) => {
      downloadExcelFile(res)
    })
