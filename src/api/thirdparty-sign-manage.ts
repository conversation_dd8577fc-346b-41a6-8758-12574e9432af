import { IContractCheck, IContractOperate } from '@/types/sign-manage'
import type { AxiosResponse } from 'axios'
import { req as services } from '@/utils/request'
import { downloadExcelFile } from '@/utils/utils'

// 已签署列表
export const getSignedList = (params) => services.get('/outsourcing_sign/list/signed', params)

// 已失效列表
export const getSignExpiredList = (params) => services.get('/outsourcing_sign/list/invalid', params)

// 签署中列表
export const getSigningList = (params) => services.get('/outsourcing_sign/list/signing', params)

// 已作废列表
export const getSignVoidList = (params) => services.get('/outsourcing_sign/list/canceled', params)

// 发起签署
export const startSign = (params) => services.post('/outsourcing_sign/starting', params)

// 查看详情
export const getSignDetail = (id) => services.get(`/outsourcing_sign/detail/${id}`)

// 重发
export const resendApi = (params) => services.post('/outsourcing_sign/link/send', params)

// 发起签署获取业务项列表
export const getBusinessListApi = (): Promise<AxiosResponse<any, any>> =>
  services.get('/business_param_rule/sign', { internalEmployee: false })

// 失效
export const invalidApi = (params) => services.post('/outsourcing_sign/link/invalid', params)

// 获取签署的合同PDF
export const getSignFileApi = (id) => services.get(`/outsourcing_sign/contract/file/${id}`)

// 获取签署的关联合同PDF
export const getSignRelatedFileApi = (id) => services.get(`/sign/contract/related_file/${id}`)

// 获取模板内容及变量
export const getTemplateContent = (params) => services.post('/sign/template/content', params)

// 预览合同（操作日志记录）
export const previewTemplateForLog = (id) =>
  services.get(`/outsourcing_sign/contract/preview/${id}`)

// 下载合同（操作日志记录）
export const downloadTemplateForLog = (id) =>
  services.get(`/outsourcing_sign/contract/download/${id}`)

// 复制链接
export const linkCopy = (id) => services.put(`/outsourcing_sign/link/copy/${id}`)

// 查询签署文件下拉
export const getFilesList = (status) =>
  services
    .get('/outsourcing_sign/sign_file', {
      status,
    })
    .then(({ data: resData }) => {
      if (resData?.data) {
        return resData.data
      }
      return []
    })

// 查询合同主体
export const getContractSubject = (status) =>
  services
    .get('/outsourcing_sign/contract_subject', {
      status,
    })
    .then(({ data: resData }) => {
      if (resData?.data) {
        return resData.data
      }
      return []
    })

// 查询关联合同
export const getRelatedContract = (status) =>
  services
    .get('/outsourcing_sign/related_apply_contract', {
      status,
    })
    .then(({ data: resData }) => {
      if (resData?.data) {
        return resData.data
      }
      return []
    })

// 查询关联申请单号
export const getRelatedNumber = (status) =>
  services
    .get('/outsourcing_sign/related_apply_number', {
      status,
    })
    .then(({ data: resData }) => {
      if (resData?.data) {
        return resData.data
      }
      return []
    })

// 查询合同包名称列表
export const getContractPackageList = (params) => services.get('/packages/name/list', params)

// 下载合同导入模板
export const downloadTemplate = () =>
  services
    .request({
      url: '/outsourcing_sign/sign_import_template',
      method: 'GET',
      responseType: 'blob',
    })
    .then((res) => {
      downloadExcelFile(res)
    })

// 导入合同模板
export const uploadTemplate = (params) => services.post('/outsourcing_sign/import_contract', params)

// 作废
export const invalidContract = (params: IContractOperate) =>
  services.post('/outsourcing_sign/invalid', params)

// 合同解除
export const dischargeContract = (params: IContractOperate) =>
  services.post('/outsourcing_sign/discharge', params)

// 获取签署（解除合同、作废）模板内容及变量值
export const getTemplateAndVar = ({
  signId,
  templateId,
}: {
  signId: number | string
  templateId: number
}) => services.get(`/outsourcing_sign/invalid/${signId}/template/${templateId}/content`)

// 解除合同和作废前的判断
export const checkContract = (params: IContractCheck) =>
  services.get('/outsourcing_sign/contract/check_invalid', params)

// 查询数据源下拉项
export const getDataSourceList = (status) =>
  services.get('/outsourcing_sign/sing_data_source', { status }).then(({ data: resData }) => {
    if (resData?.data) {
      return resData.data
    }
    return []
  })

// 导出签署信息
export const downloadSign = (params) =>
  services
    .request({
      url: '/sign/variable/export',
      method: 'post',
      responseType: 'blob',
      data: params,
    })
    .then((res) => {
      downloadExcelFile(res)
    })

// 获取变量模板
export const getVariablesApi = (): Promise<AxiosResponse<any, any>> =>
  services.get('/template/variables')

// 获取签署日志信息
export const getSignLogApi = (contractId): Promise<AxiosResponse<any, any>> =>
  services.get(`/sign/log/${contractId}`)
