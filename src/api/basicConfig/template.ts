import { req } from '@/utils/request'
import { downloadExcelFile } from '@/utils/utils'
import type { AxiosResponse } from 'axios'
import type { ActionContext } from '@galaxy/business-request'

const services: ActionContext = req

// 获取合同列表分页
export const getTemplateList = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/template/list', params)

// 查询模板名称是否存在
export const isNameExist = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/template/name', params)

// 查询模板编码是否存在
export const isNumberExist = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/template/exist_number', params)

// 停用模板
export const disableTemplateApi = (id): Promise<AxiosResponse<any, any>> =>
  services.put(`/template/disable/${id}`)

// 启用模板
export const enableTemplateApi = (id): Promise<AxiosResponse<any, any>> =>
  services.put(`/template/enable/${id}`)

// 删除模板
export const deleteTemplateApi = (id): Promise<AxiosResponse<any, any>> =>
  services.delete(`/template/${id}`)

// 文件上传
export const uploadFileApi = (params): Promise<AxiosResponse<any, any>> =>
  services.post('/template/upload', params)

// 新增模板
export const createTemplateApi = (params): Promise<AxiosResponse<any, any>> =>
  services.post('/template', params)

// 编辑模板
export const editTemplateApi = (id, params): Promise<AxiosResponse<any, any>> =>
  services.put(`/template/${id}`, params)

// 获取模板详情
export const getTemplateDetailApi = (id): Promise<AxiosResponse<any, any>> =>
  services.get(`/template/detail/${id}`)

// 查询引用情况
export const getTemplateReference = (id): Promise<AxiosResponse<any, any>> =>
  services.get(`/template/reference/${id}`)

// 模板上传法大大
export const uploadFdd = (templateIds: number[]): Promise<AxiosResponse<any, any>> =>
  services.put(`/template/upload_fdd/batch?templateIds=${templateIds}`)

// 导出模板变量（批量）
export const downloadVar = (templateIds: number[]): Promise<unknown> =>
  services
    .get(
      `/template/variable/download`,
      { templateIds },
      {
        responseType: 'blob',
      },
    )
    .then((res) => {
      return downloadExcelFile(res)
    })

// 合同模板信息（根据印章定位方式筛选）- 合同包模板弹窗tab
export const getTemplateInfo = (params: {
  sealLocateModeCode: string
}): Promise<AxiosResponse<any, any>> => services.get(`/template/info`, params)

// 合同模板信息（根据关联业务项、印章定位方式筛选）
export const getTemplateInfoList = (params) => {
  return services.get('/template/info/list', {
    businessItemCode: params.businessItemCode,
    sealLocateModeCode: params.sealLocateModeCode,
    isShowSealKeyWord2: params.isShowSealKeyWord2,
  })
}

// 校验多选模板有效
export const validTemplate = (templateIds: number[]): Promise<AxiosResponse<any, any>> =>
  services.get(`/template/valid`, {
    templateIds,
  })

// 下拉选项-关联业务项
export const getRelatedBusiness = () => services.get('/select_item/related_business_item')
