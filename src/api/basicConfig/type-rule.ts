import { req } from '@/utils/request'
import type { AxiosResponse } from 'axios'
import type { ActionContext } from '@galaxy/business-request'

const services: ActionContext = req

// 获取合同类型规则
export const getTypeRules = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/contract/type_rule/page', params)

// 删除合同规则
export const deleteTypeRule = (id): Promise<AxiosResponse<any, any>> =>
  services.delete(`/contract/type_rule/${id}`)

// 复制规则
export const copyTypeRule = (id, params): Promise<AxiosResponse<any, any>> =>
  services.post(`/contract/type_rule/copy/${id}`, params)

// 查询启用的合同规则类型
export const getEnableList = (id): Promise<AxiosResponse<any, any>> =>
  services.get(`/contract/type_rule/enabled/list/${id}`)

// 查看合同类型详情
export const getTypeRuleDetail = (id): Promise<AxiosResponse<any, any>> =>
  services.get(`/contract/type_rule/detail/${id}`)

// 禁用合同类型规则
export const disabledTypeRule = (params): Promise<AxiosResponse<any, any>> =>
  services.put('/contract/type_rule/disabled', params)

// 启用合同类型规则
export const enabledTypeRule = (id, version): Promise<AxiosResponse<any, any>> =>
  services.put(`/contract/type_rule/enabled/${id}/${version}`)

// 新建合同规则类型
export const createTypeRule = (params): Promise<AxiosResponse<any, any>> =>
  services.post('/contract/type_rule', params)

// 编辑合同规则类型
export const editTypeRule = (id, params): Promise<AxiosResponse<any, any>> =>
  services.put(`/contract/type_rule/${id}`, params)

// 查询合同规则类型（启用中，且已经到达生效日期的规则）
export const getTypeRuleList = (params) =>
  services.get('/contract/type_rule/enabled/efficient/list', {
    isSupportInner: params.isSupportInner,
    isSupportOuter: params.isSupportOuter,
  })

// 查询名称是否存在
export const getNameExists = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/contract/type_rule/exists', params)
