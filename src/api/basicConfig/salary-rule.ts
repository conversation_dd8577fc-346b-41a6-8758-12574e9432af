import { req } from '@/utils/request'
import type { AxiosResponse } from 'axios'
import type { ActionContext } from '@galaxy/business-request'

const services: ActionContext = req

// 获取分页工资规则
export const getSalaryRules = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/salary/rule/page', params)

// 复制工资规则
export const copySalaryRule = (id, params): Promise<AxiosResponse<any, any>> =>
  services.post(`/salary/rule/copy/${id}`, params)

// 改变规则状态
// export const changeSalaryRule = (id, version, status) =>
//   services.put(`/salary/rule/status/${id}/${status}/${version}`)

// 停用签订规则
export const disableSalaryRule = (id, version): Promise<AxiosResponse<any, any>> =>
  services.put(`/salary/rule/disabled/${id}/${version}`)

// 启用签订规则
export const enableSalaryRule = (id, version): Promise<AxiosResponse<any, any>> =>
  services.put(`/salary/rule/enabled/${id}/${version}`)

// 删除工资规则
export const deleteSalaryRule = (id): Promise<AxiosResponse<any, any>> =>
  services.delete(`/salary/rule/${id}`)

// 新增工资规则
export const createSalaryRule = (params): Promise<AxiosResponse<any, any>> =>
  services.post('/salary/rule', params, {
    noMessage: true,
  })

// 编辑工资规则
export const editSalaryRule = (id, params): Promise<AxiosResponse<any, any>> =>
  services.put(`/salary/rule/${id}`, params, {
    noMessage: true,
  })

// 查询工资规则详情
export const getSalaryRuleDetail = (id): Promise<AxiosResponse<any, any>> =>
  services.get(`/salary/rule/detail/${id}`)

// 查询名称是否重复
export const getNameExists = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/salary/rule/exists', params)
