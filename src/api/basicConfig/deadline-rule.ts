import { req } from '@/utils/request'
import type { AxiosResponse } from 'axios'
import type { ActionContext } from '@galaxy/business-request'

const services: ActionContext = req

// 查询合同期限规则（启用中，且已经到达生效日期的规则）
export const getDeadLineRuleList = (params) =>
  services.get('/contract/deadline_rule/enabled/efficient/list', {
    isSupportInner: params.isSupportInner,
    isSupportOuter: params.isSupportOuter,
  })

// 查询合同期限规则列表(分页)
export const getDeadlineRule = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/contract/deadline_rule/page', params)

// 新增期限合同规则
export const createDeadlineRule = (params): Promise<AxiosResponse<any, any>> =>
  services.post('/contract/deadline_rule', params, {
    noMessage: true,
  })

// 复制期限合同规则
export const copyDeadlineRule = (id, params): Promise<AxiosResponse<any, any>> =>
  services.post(`/contract/deadline_rule/copy/${id}`, params, {
    noMessage: true,
  })

// 查询合同期限规则详情
export const getDeadlineRuleDetail = (id): Promise<AxiosResponse<any, any>> =>
  services.get(`/contract/deadline_rule/detail/${id}`)

// 禁用合同期限规则
export const disabledDeadlineRule = (params): Promise<AxiosResponse<any, any>> =>
  services.put('/contract/deadline_rule/disabled', params)

// 启用合同规则
export const enabledDeadlineRule = (id, version): Promise<AxiosResponse<any, any>> =>
  services.put(`/contract/deadline_rule/enabled/${id}/${version}`, {
    noMessage: true,
  })

// 编辑合同期限规则
export const editDeadlineRule = (id, params): Promise<AxiosResponse<any, any>> =>
  services.put(`/contract/deadline_rule/${id}`, params, {
    noMessage: true,
  })

// 删除合同期限规则
export const deleteDeadlineRule = (id): Promise<AxiosResponse<any, any>> =>
  services.delete(`/contract/deadline_rule/${id}`)

// 查询期限规则名称是否重复
export const getNameExists = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/contract/deadline_rule/name/exist', params)
