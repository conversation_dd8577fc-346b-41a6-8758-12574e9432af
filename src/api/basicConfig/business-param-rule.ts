import { req } from '@/utils/request'
import type { AxiosResponse } from 'axios'
import type { ActionContext } from '@galaxy/business-request'

const services: ActionContext = req

// 查询参数规则(关联业务项)
export const getParamRule = (): Promise<AxiosResponse<any, any>> =>
  services.get('/business_param_rule')

// 新增/编辑参数规则
export const dealParamRule = (params): Promise<AxiosResponse<any, any>> =>
  services.post('/business_param_rule', params)
