import { req } from '@/utils/request'
import { IPage } from '@/types/search'
import { EnablePackageType, RuleItem, IPackagePage, IPackageCreate } from '@/types/package'
import { downloadExcelFile } from '@/utils/utils'
import type { AxiosResponse } from 'axios'
import type { ActionContext } from '@galaxy/business-request'

import config from '@/config'

const { basicServerUrl } = config

const services: ActionContext = req

// 获取合同包分页
export const getContractPackagePage = (
  params: Partial<IPackagePage> & IPage,
): Promise<AxiosResponse<any, any>> => services.get('/packages', params)

// 创建合同包
export const createContractPackage = (params: IPackageCreate): Promise<AxiosResponse<any, any>> =>
  services.post('/packages', params, {
    // isCustom: true,
  })

// 编辑合同包
export const editContractPackage = (
  id: number | string,
  params: IPackageCreate,
): Promise<AxiosResponse<any, any>> =>
  services.put(`/packages/${id}`, params, {
    // isCustom: true,
  })

// 获取合同包详情
export const getContractPackage = (id: number): Promise<AxiosResponse<any, any>> =>
  services.get(`/packages/detail/${id}`)

// 删除合同包
export const deleteContractPackage = (id: number): Promise<AxiosResponse<any, any>> =>
  services.delete(`/packages/${id}`)

// 停用合同包
export const disablePackage = (id): Promise<AxiosResponse<any, any>> =>
  services.put(`/packages/disabled/${id}`)

// 启用合同包
export const enablePackage = (id): Promise<AxiosResponse<any, any>> =>
  services.put(`/packages/enabled/${id}`)

// 查询合同包优先级列表
export const getPriorityList = (): Promise<AxiosResponse<any, any>> =>
  services.get('/packages/prioritys')

// 查询人员范围名称列表
export const getUserRangeNames = (): Promise<AxiosResponse<any, any>> =>
  services.get('/packages/user_range_names')

// 获取范围人员分页
export const getRangePeoplePage = (
  params: {
    keyword?: string
    contractPackageId?: number
    userRangeRules?: RuleItem[]
  } & IPage,
): Promise<AxiosResponse<any, any>> =>
  services.post(`/packages/user_ranges?page=${params.page}&size=${params.size}`, params)

// 删除范围人员
export const deleteRangePeople = (
  num: number,
  contractPackageId: number,
): Promise<AxiosResponse<any, any>> =>
  services.put('/packages/remove_user_ranges', {
    contractPackageId,
    num,
  })

// 查询合同包关联的规则、模板是否存在禁用
export const hasDisabledRelaData = (contractPackageId: number): Promise<AxiosResponse<any, any>> =>
  services.get(`/packages/rela_data/disabled/${contractPackageId}`)

// 弹窗-启用合同包
export const enableContractPackage = (
  contractPackageId: number,
  params: EnablePackageType,
): Promise<AxiosResponse<any, any>> => services.put(`/packages/${contractPackageId}/enable`, params)

// 查询合同期限关联的合同包
export const getPackageByDeadlineRuleId = (
  deadlineRuleId: number,
): Promise<AxiosResponse<any, any>> => services.get(`/packages/deadline_rule/${deadlineRuleId}`)

// 查询合同类型关联的合同包
export const getPackageByTypeRuleId = (typeRuleId: number): Promise<AxiosResponse<any, any>> =>
  services.get(`/packages/type_rule/${typeRuleId}`)

// 查询合同包名称是否重复
export const getNameExists = (params: {
  id?: number | string
  name: string
}): Promise<AxiosResponse<any, any>> => services.get('/packages/name/exists', params)

// 查询模板列表
export const getTemplates = (): Promise<AxiosResponse<any, any>> =>
  services.get('/packages/templates')

// 根据业务项获取优先级
export const getPriorityByBusinessId = (
  businessItemCode: string,
): Promise<AxiosResponse<any, any>> =>
  services.get(`/packages/business_param/priority/${businessItemCode}`)

// 导出模板变量（批量）
export const downloadVar = (packageIds: number[]): Promise<unknown> =>
  services
    .get(
      `/packages/variable/download`,
      { packageIds },
      {
        responseType: 'blob',
      },
    )
    .then((res) => {
      return downloadExcelFile(res)
    })

// 查询合同关联的业务项列表
export const getRelatedItems = () => services.get('/packages/related_business_items')

// 合同应用列表
export const getApplicationList = () => services.get('/application/list')

// 获取人员范围选择条件字段
export const getSettingPersonOptionsApi = (code) =>
  services.get(`/packages/user_range_fields/${code}`)

// 人员范围校验
export const userRangeCheck = (params) => services.post(`/packages/user_range_check`, params)

// 设置人员范围自定义请求部门树
export const getOrgOptions = (params) =>
  services.get(
    `${basicServerUrl}/admin/organization/organizations/1/sub_organization_nodes`,
    params,
  )

// 设置人员范围自定义请求部门树
export const getSearchOrgOptions = (params) =>
  services.get(`${basicServerUrl}/admin/organization/organizations/1/search`, params)

// 查询合同包人员范围选择条件字段
export const getUserRangeFields = () => services.get(`/packages/user_range_fields`)
