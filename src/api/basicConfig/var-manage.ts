import { req } from '@/utils/request'
import { moveToFront } from '@/utils/utils'
import type { AxiosResponse } from 'axios'
import type { ActionContext } from '@galaxy/business-request'

const services: ActionContext = req

// 获取变量管理列表
export const getVarList = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/var/list', params)

// 新增变量管理
export const createVar = (params): Promise<AxiosResponse<any, any>> =>
  services.post('/var/variables', params)

// 查询适用的模板列表
export const getTemplateList = (id): Promise<AxiosResponse<any, any>> =>
  services.get(`/var/ref/${id}`)

// 停用变量
export const disabledVar = (id): Promise<AxiosResponse<any, any>> =>
  services.post(`/var/disable/${id}`)

// 启用变量
export const enabledVar = (id): Promise<AxiosResponse<any, any>> =>
  services.post(`/var/enable/${id}`)

// 删除变量
export const deleteVar = (id): Promise<AxiosResponse<any, any>> =>
  services.delete(`/var/variables/${id}`)

// 修改变量
export const editVar = (id, params): Promise<AxiosResponse<any, any>> =>
  services.put(`/var/variables/${id}`, params)

// 变量详情
export const getVarDetail = (id): Promise<AxiosResponse<any, any>> =>
  services.get(`/var/detail/${id}`)

// 查询变量编码是否重复
export const getCodeExist = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/var/exist_code', params)

// 查询变量名称是否重复
export const getNameExist = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/var/exist', params)

// 查询数据来源聚合数据
export const getDataSource = (): Promise<AxiosResponse<any, any>> =>
  services.get('/select_item/variable_source').then(({ data: resData }) => {
    if (resData?.data) {
      return moveToFront(resData.data, '--')
    }
    return []
  })

// 查询变量分组列表
export const getVarGroups = (): Promise<AxiosResponse<any, any>> => services.get('/var/group/list')

// 新增二级分类
export const createVarGroup = (params): Promise<AxiosResponse<any, any>> =>
  services.post('/var/group', params)

// 查询原分组名称是否重复
export const validateGroupName = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/var/group/name', params)

// 编辑变量分组名称
export const editVarGroup = (groupId, params): Promise<AxiosResponse<any, any>> =>
  services.put(`/var/group/${groupId}/name?`, params)

// 查看分组下模版引用信息
export const getGroupRefList = (groupId): Promise<AxiosResponse<any, any>> =>
  services.get(`/var/group/ref/${groupId}`)

// 删除二级分类
export const deleteVarGroup = (groupId): Promise<AxiosResponse<any, any>> =>
  services.delete(`/var/group/${groupId}`)
