import { req } from '@/utils/request'
import type { AxiosResponse } from 'axios'
import type { ActionContext } from '@galaxy/business-request'
import config from '@/config'

const services: ActionContext = req

const { basicServerUrl } = config

// 查询当前实体所拥有的组织权限
export const getOrgPermission = (): Promise<AxiosResponse<any, any>> =>
  services.get(`${basicServerUrl}/admin/contract/v0.1/org_permissions`)

// 查询项目下所有按钮权限
export const getBtnPermission = (): Promise<AxiosResponse<any, any>> =>
  services.get(`${basicServerUrl}/admin/contract/v0.1/permission/button`)
