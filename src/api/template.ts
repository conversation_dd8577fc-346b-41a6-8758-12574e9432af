import { req } from '@/utils/request'
import { downloadExcelFile } from '@/utils/utils'
import type { AxiosResponse } from 'axios'
import type { ActionContext } from '@galaxy/business-request'

const services: ActionContext = req

// 获取模板分页
export const getTemplatePage = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/template/list', params)

// 创建模板
export const createTemplate = (params): Promise<AxiosResponse<any, any>> =>
  services.post('/template', params)

// 编辑模板
export const editTemplate = (id, params): Promise<AxiosResponse<any, any>> =>
  services.put(`/template/${id}`, params)

// 获取模板详情
export const getTemplate = (id): Promise<AxiosResponse<any, any>> =>
  services.get(`/template/detail/${id}`)

// 删除模板
export const deleteTemplate = (id): Promise<AxiosResponse<any, any>> =>
  services.delete(`/template/${id}`)

// 获取模板列表
export const getTemplateList = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/template/simple', params)

// 合同模板信息（根据关联业务项、印章定位方式筛选）
export const getTemplateInfoList = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/template/info/list', {
    params,
  })

export const getAppListApi = (): Promise<AxiosResponse<any, any>> =>
  services.get(`/application/list`)

// 查询合同模板详情（批量）
export const getBatchTemplate = (templateIds: string): Promise<AxiosResponse<any, any>> =>
  services.get(`/template/detail/batch?templateIds=${templateIds}`)

// 根据 url 下载模板通用接口
export const downloadByUrl = (url, params = {}): Promise<unknown> =>
  services
    .request({
      url,
      method: 'GET',
      params,
      responseType: 'blob',
    })
    .then((res) => {
      downloadExcelFile(res)
    })
