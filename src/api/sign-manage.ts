import { IContractCheck, IContractOperate } from '@/types/sign-manage'
import { req } from '@/utils/request'
import { downloadExcelFile } from '@/utils/utils'
import type { AxiosResponse } from 'axios'
import type { ActionContext } from '@galaxy/business-request'

const services: ActionContext = req

// 已签署列表
export const getSignedList = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/sign/list/signed', params)

// 已失效列表
export const getSignExpiredList = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/sign/list/invalid', params)

// 签署中列表
export const getSigningList = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/sign/list/signing', params)

// 已作废列表
export const getSignVoidList = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/sign/list/canceled', params)

// 发起签署
export const startSign = (params): Promise<AxiosResponse<any, any>> =>
  services.post('/sign/starting', params)

// 查看详情
export const getSignDetail = (id): Promise<AxiosResponse<any, any>> =>
  services.get(`/sign/detail/${id}`)

// 重发
export const resendApi = (params): Promise<AxiosResponse<any, any>> =>
  services.post('/sign/link/send', params)

// 失效
export const invalidApi = (params): Promise<AxiosResponse<any, any>> =>
  services.post('/sign/link/invalid', params)

// 获取签署的合同PDF
export const getSignFileApi = (id): Promise<AxiosResponse<any, any>> =>
  services.get(`/sign/contract/file/${id}`)

// 获取签署的关联合同PDF
export const getSignRelatedFileApi = (id): Promise<AxiosResponse<any, any>> =>
  services.get(`/sign/contract/related_file/${id}`)

// 获取模板内容及变量
export const getTemplateContent = (params): Promise<AxiosResponse<any, any>> =>
  services.post('/sign/template/content', params)

// 预览合同（操作日志记录）
export const previewTemplateForLog = (id): Promise<AxiosResponse<any, any>> =>
  services.get(`/sign/contract/preview/${id}`)

// 下载合同（操作日志记录）
export const downloadTemplateForLog = (id): Promise<AxiosResponse<any, any>> =>
  services.get(`/sign/contract/download/${id}`)

// 查询签署文件下拉
export const getFilesList = (status): Promise<AxiosResponse<any, any>> =>
  services
    .get('/select_item/sign_file', {
      status,
    })
    .then(({ data: resData }) => {
      if (resData?.data) {
        return resData.data
      }
      return []
    })

// 查询合同主体
export const getContractSubject = (status): Promise<AxiosResponse<any, any>> =>
  services
    .get('/select_item/contract_subject', {
      status,
    })
    .then(({ data: resData }) => {
      if (resData?.data) {
        return resData.data
      }
      return []
    })

// 查询关联合同
export const getRelatedContract = (status) =>
  services
    .get('/select_item/related_apply_contract', {
      status,
    })
    .then(({ data: resData }) => {
      if (resData?.data) {
        return resData.data
      }
      return []
    })

// 查询关联申请单号
export const getRelatedNumber = (status): Promise<AxiosResponse<any, any>> =>
  services
    .get('/select_item/related_apply_number', {
      status,
    })
    .then(({ data: resData }) => {
      if (resData?.data) {
        return resData.data
      }
      return []
    })

// 查询合同包名称列表
export const getContractPackageList = (params): Promise<AxiosResponse<any, any>> =>
  services.get('/packages/name/list', params)

// 下载合同导入模板
export const downloadTemplate = (): Promise<unknown> =>
  services
    .request({
      url: '/sign/sign_import_template',
      method: 'GET',
      responseType: 'blob',
    })
    .then((res) => {
      downloadExcelFile(res)
    })

// 导入合同模板
export const uploadTemplate = (params): Promise<AxiosResponse<any, any>> =>
  services.post('/sign/import_contract', params)

// 作废
export const invalidContract = (params: IContractOperate): Promise<AxiosResponse<any, any>> =>
  services.post('/sign/invalid', params)

// 合同解除
export const dischargeContract = (params: IContractOperate): Promise<AxiosResponse<any, any>> =>
  services.post('/sign/discharge', params)

// 获取签署（解除合同、作废）模板内容及变量值
export const getTemplateAndVar = ({
  signId,
  templateId,
}: {
  signId: number | string
  templateId: number
}): Promise<AxiosResponse<any, any>> =>
  services.get(`/sign/invalid/${signId}/template/${templateId}/content`)

// 复制链接
export const linkCopy = (id): Promise<AxiosResponse<any, any>> =>
  services.put(`/sign/link/copy/${id}`)

// 解除合同和作废前的判断
export const checkContract = (params: IContractCheck): Promise<AxiosResponse<any, any>> =>
  services.get('/sign/contract/check_invalid', params)

// 查询数据源下拉项
export const getDataSourceList = (status): Promise<AxiosResponse<any, any>> =>
  services.get('/select_item/sing_data_source', { status }).then(({ data: resData }) => {
    if (resData?.data) {
      return resData.data
    }
    return []
  })

// 发起签署获取业务项列表
export const getBusinessListApi = (): Promise<AxiosResponse<any, any>> =>
  services.get('/business_param_rule/sign', { internalEmployee: true })

// 导出签署信息
export const downloadSign = (params) =>
  services
    .request({
      url: '/sign/variable/export',
      method: 'post',
      responseType: 'blob',
      data: params,
    })
    .then((res) => {
      downloadExcelFile(res)
    })
// 获取变量模板
export const getVariablesApi = (): Promise<AxiosResponse<any, any>> =>
  services.get('/template/variables')

// 获取签署日志信息
export const getSignLogApi = (contractId): Promise<AxiosResponse<any, any>> =>
  services.get(`/sign/log/${contractId}`)
