import { getFunctionModules, getUserBehaviors } from '@/api/operate-log'
/** 操作日志 */

// 查询功能模块
export const getFunctionModuleList = async () => {
  const {
    data: { data },
  } = await getFunctionModules()
  return data.map((name) => ({
    label: name,
    value: name,
  }))
}

// 查询用户操作行为
export const getUserBehaviorList = async () => {
  const {
    data: { data },
  } = await getUserBehaviors()
  return data.map((name) => ({
    label: name,
    value: name,
  }))
}
