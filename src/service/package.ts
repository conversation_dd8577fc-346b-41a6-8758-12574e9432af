// 接口
import { getPriorityList, getUserRangeNames, getTemplates } from '@/api/basicConfig/package'
import { getTemplateList, getTemplateInfoList } from '@/api/template'
import { getParamRule } from '@/api/basicConfig/business-param-rule'
import { getTypeRuleList } from '@/api/basicConfig/type-rule'
import { getDeadLineRuleList } from '@/api/basicConfig/deadline-rule'
import { getPositionApi } from '@/api/basic-server'
import { WARNING } from '@/constants/package'
/** 合同包 */

// 获取合同模板
export const getContractTemplate = async (disabledValue?: string | string[]) => {
  const {
    data: { data },
  } = await getTemplateList()
  return data.map((option) => ({
    ...option,
    label: option.name,
    value: option.id,
    title: option.name,
    disabled: Array.isArray(disabledValue)
      ? disabledValue.includes(option.id)
      : disabledValue === option.id,
  }))
}

// 获取合同模板
export const getContractTemplateInfo = async (params) => {
  const {
    data: { data },
  } = await getTemplateInfoList(params)
  return data.map((option) => ({
    ...option,
    label: option.name,
    value: option.id,
    title: option.name,
  }))
}

// 关联业务项
export const getRelatedBusinessItem = async (isFilter?: boolean) => {
  const {
    data: { data },
  } = await getParamRule()
  let delIndex = -1
  const list = data.map((option, index) => {
    if (isFilter && delIndex === -1 && option.businessNameCode === WARNING) {
      delIndex = index
    }
    return {
      label: option.businessName,
      value: option.businessNameCode,
      ...option,
    }
  })
  if (delIndex !== -1) {
    list.splice(delIndex, 1)
  }
  // 为了处理后端返回数据，没有id的情况
  return list.filter((item) => item.value !== undefined)
}

// 人员范围
export const getUserRangeNameList = async () => {
  const {
    data: { data },
  } = await getUserRangeNames()
  return data.map((val) => ({
    label: String(val),
    value: String(val),
  }))
}

// 优先级
export const getPrioritys = async () => {
  const {
    data: { data },
  } = await getPriorityList()
  return data.map((val) => ({
    label: String(val),
    value: String(val),
  }))
}

// 获取合同类型规则
export const getRelatedContractType = async (classification) => {
  const {
    data: { data },
  } = await getTypeRuleList(classification)
  return data.map((option) => ({
    label: option.name,
    value: option.id,
  }))
}

// 获取合同期限规则
export const getRelatedContractTimeLimit = async (classification) => {
  const {
    data: { data },
  } = await getDeadLineRuleList(classification)
  return data.map((option) => ({
    label: option.name,
    value: option.id,
  }))
}

// 获取岗位列表
export const getPositionList = async () => {
  const {
    data: { data },
  } = await getPositionApi()
  return data.map((option) => ({
    label: option.name,
    value: option.id,
    title: option.fullName || option.name,
  }))
}

// 获取模板列表
export const getContractTemplateList = async () => {
  const {
    data: { data },
  } = await getTemplates()
  return data.map((option) => ({
    label: String(option.name),
    value: option.id,
  }))
}
