import UploadSdk from '@galaxy/upload'
import config from '@/config'
import { req as services } from '@/utils/request'

const { tenantId } = config

const uploadSdk = new UploadSdk({
  clientCode: 'CONTRACT',
  tenantId,
  oss: { service: '/admin/contract' },
})

function _arrayBufferToBase64(buffer) {
  let binary = ''
  const bytes = new Uint8Array(buffer)
  const len = bytes.byteLength
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  return window.btoa(binary)
}

export const uploadServiceApi = async (file) => {
  const res = await uploadSdk.upload({ file })
  return res.uuid
}

// 获取文件 url
export const getFileUrlServiceApi = async (id: string, options?: Record<string, any>) => {
  const { url = '' } = await uploadSdk.download(id, options)
  return url
}

// 获取文件 base64 数据
export const getFileDataServiceApi = async (id) => {
  const url = await getFileUrlServiceApi(id)
  if (!url) {
    throw new Error('获取不到文件 url')
  }
  const result = await services.request({
    url,
    method: 'GET',
    responseType: 'arraybuffer',
  })
  return _arrayBufferToBase64(result.data)
}
