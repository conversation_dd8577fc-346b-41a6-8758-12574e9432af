import React, { useEffect, useState, useMemo, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { useStore } from '@/stores'
import { <PERSON><PERSON>, Divider, Tooltip } from 'antd'
import classNames from 'classnames'
import { QuestionCircleOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons'
import { SchemaTable, createAction, Delete } from '@amazebird/antd-schema-table'
import type { SchemaColumnType } from '@amazebird/antd-schema-table'
import PermissionAction from '@/components/permissionAction'
import config from '@/config'
import { getVarList, deleteVar } from '@/api/basicConfig/var-manage'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import { VAR_MANAGE } from '@/constants/rbac-code'
import useCreateStore from '@/stores/useCreateStore'
import TypeTree from './components/typeTree/index'
import { tableColumns, searchColumns } from './Columns'
import { disableVar, enableVar, createVar, editVar } from './Actions'

import styles from './styles.module.less'

type ColumnsItem = SchemaColumnType[number]

const { baseRoute } = config

const VariableManage = () => {
  const store = useStore((state) => state)
  const { setTitle } = store

  const [total, setTotal] = useState(0)
  const [isFold, setIsFold] = useState(false)
  const navigate = useNavigate()
  const action = createAction()
  const typeTreeRef = useRef<any>()
  const permissions = useCreateStore((state: any) => state.permissions)

  const operateColumns: ColumnsItem = {
    title: '操作',
    key: 'operate',
    fixed: 'right',
    width: 150,
    cell: ({ record }) => (
      <>
        <PermissionAction code={VAR_MANAGE.LIST.VIEW} permissions={record.permissions}>
          <Button
            type="link"
            size="small"
            onClick={() => {
              dhrReport.trace(REPORT_EVENT_TYPE.VARIABLE_VIEW, record)
              navigate(`${baseRoute}/contract/basic-config/variable-manage/detail?id=${record.id}`)
            }}
          >
            查看
          </Button>
        </PermissionAction>
        <PermissionAction code={VAR_MANAGE.LIST.EDIT} permissions={record.permissions}>
          <Button
            type="link"
            size="small"
            onClick={() => {
              dhrReport.trace(REPORT_EVENT_TYPE.VARIABLE_EDIT, record)
              editVar(record.id, () => action.refresh())
            }}
          >
            编辑
          </Button>
        </PermissionAction>
        {record.isEnable ? (
          <PermissionAction code={VAR_MANAGE.LIST.DISABLED} permissions={record.permissions}>
            <Button
              type="link"
              size="small"
              onClick={() => {
                disableVar(record, (actionName, data) => {
                  if (actionName === 'redirect') {
                    navigate(`${baseRoute}/contract/basic-config/template?codes=${data}`)
                  } else {
                    action.refresh()
                  }
                })
              }}
            >
              停用
            </Button>
          </PermissionAction>
        ) : (
          <PermissionAction code={VAR_MANAGE.LIST.ENABLED} permissions={record.permissions}>
            <Button
              type="link"
              size="small"
              onClick={() => {
                enableVar(record, () => action.refresh())
              }}
            >
              启用
            </Button>
          </PermissionAction>
        )}
        <PermissionAction code={VAR_MANAGE.LIST.DELETE} permissions={record.permissions}>
          <Delete
            record={record}
            onDelete={() => deleteVar(record.id)}
            content="删除后不可恢复，确定要删除变量吗？"
            buttonProps={{
              onClick: () => {
                dhrReport.trace(REPORT_EVENT_TYPE.VARIABLE_DELETE, record)
              },
            }}
          />
        </PermissionAction>
      </>
    ),
  }

  const columns: SchemaColumnType = useMemo(() => tableColumns.concat(operateColumns), [])

  const requestApi = ({ filter, pagination }) => {
    const { current, pageSize } = pagination
    const { isEnable, time, ...rest } = filter
    const params = {
      page: current,
      size: pageSize,
      isEnable: isEnable !== 'all' ? isEnable : undefined,
      disableBeginTime: time?.[0]?.startOf('day')?.valueOf(),
      disableEndTime: time?.[1]?.endOf('day')?.valueOf(),
      ...rest,
    }
    // 变量分类筛选
    if (
      typeTreeRef.current &&
      Array.isArray(typeTreeRef.current.selectedKeys) &&
      typeTreeRef.current.selectedKeys.length &&
      typeTreeRef.current.selectedKeys[0] !== 'all'
    ) {
      if (typeof typeTreeRef.current.selectedKeys[0] === 'string') {
        params.type = typeTreeRef.current.selectedKeys[0]
      } else {
        params.groupId = typeTreeRef.current.selectedKeys[0]
      }
    }

    return getVarList(params)
      .then(({ data: resData }) => {
        if (resData.data) {
          setTotal(resData.count)
          return {
            success: true,
            data: resData.data,
            total: resData.count,
          }
        }
        return {
          rows: [],
          total: 0,
        }
      })
      .catch(() => ({
        rows: [],
        total: 0,
      }))
  }

  useEffect(() => {
    setTitle(
      <div className={styles.title}>
        <span className={styles.name}>变量管理</span>
        <Tooltip
          title={
            <>
              1、变量定义：变量指的是没有固定值，本身可变动的参数。在合同模板设置变量，发起签署将根据人员信息进行赋值展示。
              <br />
              2、变量分类：目前仅支持添加至<span className={styles.tag}>二级</span>分类。
              <br />
              1）基础变量：具备通用性，系统预置，不可编辑或删除；
              <br />
              2）自定义变量：灵活性高，上游系统可以根据变量编码传输赋值；或手动发起签署，用户可直接给变量赋值。
            </>
          }
        >
          <QuestionCircleOutlined className={styles.icon} />
        </Tooltip>
      </div>,
    )
    return () => {
      setTitle(null)
    }
  }, [])

  return (
    <div className={styles.varManage}>
      {isFold ? (
        <RightOutlined className={styles.rightIcon} onClick={() => setIsFold(false)} />
      ) : (
        <LeftOutlined className={styles.leftIcon} onClick={() => setIsFold(true)} />
      )}
      <div className={classNames(styles.left, { [styles.fold]: isFold })}>
        <div className={styles.title}>变量分类</div>
        <Divider style={{ margin: 0 }} />
        <TypeTree ref={typeTreeRef} action={action} />
      </div>

      <div className={styles.right}>
        <div className={styles.title}>
          <span className={styles.total}>{`变量管理（共${total}个）`}</span>
          <PermissionAction code={VAR_MANAGE.CREATE} permissions={permissions}>
            <Button
              type="primary"
              onClick={() => {
                createVar(() => action.refresh(), typeTreeRef)
                dhrReport.trace(REPORT_EVENT_TYPE.VARIABLE_CREATE, {})
              }}
            >
              新增变量
            </Button>
          </PermissionAction>
        </div>
        <Divider style={{ margin: 0 }} />
        <SchemaTable
          action={action}
          request={requestApi}
          columns={columns}
          searchColumns={searchColumns}
          toolbarOptions={{
            setting: true,
          }}
          searchProps={{
            initialValues: {
              isEnable: true,
            },
          }}
        />
      </div>
    </div>
  )
}

export default VariableManage
