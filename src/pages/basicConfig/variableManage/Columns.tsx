import React from 'react'
import type { SchemaColumnType } from '@amazebird/antd-schema-table'
import { getDataSource } from '@/api/basicConfig/var-manage'
import RedirectToTemplate from './components/RedirectToTemplate'

const tableColumns: SchemaColumnType = [
  {
    title: '变量名称',
    dataIndex: 'variableName',
    cell: {
      type: 'Text',
      props: {
        ellipsis: true,
      },
    },
    fixed: 'left',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'isEnable',
    cell: 'Status',
    options: [
      {
        value: true,
        label: '启用中',
        color: '#52c41a',
      },
      {
        value: false,
        label: '已停用',
        color: '#a0a0a0',
      },
    ],
  },
  {
    dataIndex: 'variableCode',
    title: '变量编码',
  },
  {
    dataIndex: 'typeName',
    title: '变量分类',
    cell: ({ text, record }) => `${text}/${record.groupName}`,
    width: 200,
  },
  {
    dataIndex: 'source',
    title: '数据来源',
  },
  {
    dataIndex: 'refCount',
    title: '适用模板',
    cell: ({ text, record }) => <RedirectToTemplate number={text} id={record.id} record={record} />,
    width: 100,
  },
  {
    dataIndex: 'disableTime',
    title: '停用日期',
    cell: 'Date',
  },
  {
    dataIndex: 'sponsorTime',
    title: '创建时间',
    cell: 'DateTime',
  },
  {
    dataIndex: 'sponsorName',
    title: '创建人',
    cell: ({ text, record }) =>
      text ? `${text}${record.sponsorNum ? `（${record.sponsorNum}）` : ''}` : '--',
    width: 150,
  },
]

const searchColumns = [
  {
    dataIndex: 'variableName',
    label: '变量名称',
    component: 'Input',
    props: {
      allowClear: true,
    },
  },
  {
    dataIndex: 'variableCode',
    label: '变量编码',
    component: 'Input',
    props: {
      allowClear: true,
    },
  },
  {
    dataIndex: 'isEnable',
    label: '状态',
    component: 'Select',
    options: [
      {
        value: 'all',
        label: '全部',
      },
      {
        value: true,
        label: '启用中',
      },
      {
        value: false,
        label: '已停用',
      },
    ],
  },
  // {
  //   dataIndex: 'type',
  //   label: '变量分类',
  //   component: Dict,
  //   props: {
  //     placeholder: '请选择变量分类',
  //     showSearch: true,
  //     code: 'VARIABLE_TYPE',
  //     allowClear: true,
  //   },
  // },
  // {
  //   dataIndex: 'groupType',
  //   label: '变量分组',
  //   component: Dict,
  //   props: {
  //     placeholder: '请选择变量分组',
  //     showSearch: true,
  //     code: 'VARIABLE_GROUP',
  //     allowClear: true,
  //   },
  // },
  {
    dataIndex: 'source',
    title: '数据来源',
    component: 'Select',
    options: () => getDataSource(),
    props: {
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
  {
    dataIndex: 'time',
    title: '停用日期',
    component: 'RangePicker',
  },
]

export { tableColumns, searchColumns }
