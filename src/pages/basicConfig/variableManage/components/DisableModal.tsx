import React, { useEffect, useState } from 'react'
import { Modal } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { getTemplateList } from '@/api/basicConfig/var-manage'

interface IProps {
  record: any
  visible: boolean
  close: () => void
  successFn: (actionName?: string, data?: any) => void
}

const DisableModal = (props: IProps) => {
  const { record, visible, close, successFn } = props
  const [template, setTemplate] = useState<any>()

  const onOk = () => {
    close()
    const codes = template?.map((i) => i.templateNumber).join(',')
    successFn?.('redirect', codes)
  }

  useEffect(() => {
    getTemplateList(record.id).then(({ data: resData }) => {
      if (resData?.data) {
        setTemplate(resData.data)
      }
    })
  }, [])

  return template ? (
    <Modal
      title={
        <span>
          <ExclamationCircleOutlined
            style={{
              color: '#faad14',
              marginRight: 8,
            }}
          />
          停用
        </span>
      }
      okText="前往操作"
      visible={visible}
      onCancel={close}
      onOk={onOk}
      closable
    >
      <p>
        当前有
        {template.length}
        个模板正在使用该变量，不能直接停用。在模板将该变量移除后，可正常停用！
      </p>
      <p>
        关联模板：
        {template.map((i) => i.templateName).join('、')}
      </p>
    </Modal>
  ) : null
}

export default DisableModal
