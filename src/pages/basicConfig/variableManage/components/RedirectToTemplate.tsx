import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from 'antd'
import { getTemplateList } from '@/api/basicConfig/var-manage'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import config from '@/config'

const { baseRoute } = config

const RedirectToTemplate = ({
  number,
  id,
  record,
}: {
  number: number
  id: number
  record: any
}) => {
  const navigate = useNavigate()

  const redirect = async () => {
    dhrReport.trace(REPORT_EVENT_TYPE.VARIABLE_TEMPLATE_VIEW, record)
    // 跳转模板页
    const { data: resData } = await getTemplateList(id)
    if (resData?.data) {
      const ids = resData.data.map((i) => i.templateId).join(',')
      navigate(`${baseRoute}/contract/basic-config/template?ids=${ids}`)
    }
  }

  return number ? (
    <Button
      type="link"
      size="small"
      style={{
        paddingLeft: 0,
        verticalAlign: 'middle',
      }}
      onClick={() => redirect()}
    >
      {number}
      &nbsp;
      <span
        style={{
          color: 'rgba(0, 0, 0, 0.65)',
        }}
      >
        个模板正使用
      </span>
    </Button>
  ) : (
    <>--</>
  )
}

export default RedirectToTemplate
