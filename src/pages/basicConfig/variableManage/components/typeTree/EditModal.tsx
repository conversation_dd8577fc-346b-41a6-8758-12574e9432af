import React, { useState, useEffect } from 'react'
import { Modal } from 'antd'
import { SchemaForm, Item } from '@amazebird/antd-schema-form'
import { VAR_TYPE } from '@/constants/var-manage'
import { validateGroupName } from '@/api/basicConfig/var-manage'

interface IProps {
  visible: boolean
  close: () => void
  groupId?: number
  onOk: (any) => Promise<any>
  type: string
  name?: string
  [filedName: string]: any
}

const EditModal = (props: IProps) => {
  const { visible, close, groupId, onOk: okClick, type, name } = props
  const form = SchemaForm.createForm()
  const [loading, setLoading] = useState(false)

  // 名称是否重复
  const nameValidator = async (_, value) => {
    if (value) {
      const params = groupId
        ? {
            originName: name,
            newName: value,
            type,
          }
        : {
            newName: value,
            type,
          }
      return validateGroupName(params).then(({ data: result }) => {
        if (result.data) {
          return Promise.reject(new Error('二级分类名称重复，请重新命名'))
        }
        return Promise.resolve()
      })
    }
    return Promise.resolve()
  }

  const schema = {
    groupName: {
      label: '二级分类名称',
      component: 'Input',
      required: true,
      max: 20,
      rules: [
        {
          validator: nameValidator,
        },
      ],
    },
  }

  const onOk = async () => {
    try {
      const values = await form?.validateFields()
      setLoading(true)

      okClick(values)
        .then(() => {
          close()
        })
        .finally(() => {
          setLoading(false)
        })
    } catch (error) {
      console.error(error)
    }
  }

  useEffect(() => {
    form?.setFieldsValue({
      groupName: name,
    })
  }, [name])
  return (
    <Modal
      title={
        groupId
          ? '编辑二级分类'
          : `${VAR_TYPE.find((i) => i.value === type)?.label || '--'}-添加二级分类`
      }
      visible={visible}
      onCancel={close}
      cancelText="取消"
      okText="确定"
      onOk={onOk}
      okButtonProps={{
        loading,
      }}
      closable
    >
      <SchemaForm schema={schema} form={form}>
        <Item field="groupName" />
      </SchemaForm>
    </Modal>
  )
}

export default EditModal
