.typeTree {
  padding: 24px;
}

.treeContainer {
  :global {
    .@{ant-prefix}-tree-treenode {
      width: 100%;
      height: 40px;
      line-height: 40px;
      .@{ant-prefix}-tree-switcher {
        line-height: 40px;
      }
      .@{ant-prefix}-tree-node-content-wrapper {
        height: 40px;
        line-height: 40px;
        flex: 1;
        overflow: hidden;
        .@{ant-prefix}-tree-title {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }
    }
  }
}

.title {
  width: 100%;
  display: flex;
  .name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .operate {
    width: 36px;
    display: none;
  }
  &:hover {
    .operate {
      display: block;
    }
  }
}
