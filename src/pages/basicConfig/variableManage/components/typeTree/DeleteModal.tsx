import React from 'react'
import { Modal } from 'antd'
import { SchemaTable } from '@amazebird/antd-schema-table'
import type { SchemaColumnType } from '@amazebird/antd-schema-table'

interface TemplateDetail {
  templateId: number
  templateName: string
  templateNumber: number
}

interface Item {
  refs: Array<TemplateDetail>
  id: number
  groupName: string
}

interface IProps {
  visible: boolean
  close: () => void
  data: Array<Item>
  successFn?: (codes: any) => void
  [filedName: string]: any
}

const DeleteModal = (props: IProps) => {
  const { visible, close, data, successFn } = props
  const refData = data.filter((i) => i.refs.length !== 0)

  const onOk = () => {
    close()
    const codes = refData
      .map((i) => {
        return i.refs.map((j) => j.templateNumber).join(',')
      })
      .join(',')
    successFn?.(codes)
  }

  const columns: SchemaColumnType = [
    {
      title: '序号',
      key: 'index',
      cell: 'Index',
    },
    {
      title: '变量名称',
      dataIndex: 'variableName',
      cell: 'Text',
    },
    {
      title: '关联模版',
      dataIndex: 'refs',
      cell: ({ text }) => text?.map((i) => i.templateName)?.join('、') || '--',
    },
  ]
  return (
    <Modal
      title="删除"
      visible={visible}
      onCancel={close}
      closable
      okText="前往操作"
      onOk={onOk}
      width={650}
      bodyStyle={{ padding: '24px 0' }}
    >
      <div
        style={{ marginBottom: 24, padding: '0 24px' }}
      >{`该分类已有 ${data.length} 个变量，其中有 ${refData.length} 个变量正被模板使用，不能直接删除。修改变量分类，或在模板将变量移除后，可正常删除分类！`}</div>
      <SchemaTable dataSource={refData} columns={columns} pagination={false} />
    </Modal>
  )
}

export default DeleteModal
