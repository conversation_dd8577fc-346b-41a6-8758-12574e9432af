import React, { useState, useEffect, useMemo, forwardRef, useImperativeHandle } from 'react'
import { useNavigate } from 'react-router-dom'
import { Input, Tree, Modal, Button, message, Tooltip, Empty } from 'antd'
import { getPermission } from '@galaxy/rbac'
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import modalWrapperHoc from '@/components/modalWrapperHoc'
import {
  getVarGroups,
  createVarGroup,
  editVarGroup,
  getGroupRefList,
  deleteVarGroup,
} from '@/api/basicConfig/var-manage'
import { VAR_TYPE } from '@/constants/var-manage'
import { VAR_MANAGE } from '@/constants/rbac-code'
import config from '@/config'
import DeleteModal from './DeleteModal'
import EditModal from './EditModal'
import styles from './styles.module.less'

const { baseRoute } = config

const { Search } = Input

const TypeTree = (props: any, ref) => {
  const [data, setData] = useState<any>()
  const [search, setSearch] = useState<any>()
  const [expandedKeys, setExpandedKeys] = useState<any>(['all'])
  const [selectedKeys, setSelectedKeys] = useState<any>([])
  const navigate = useNavigate()
  const { action } = props

  // 获取变量分组列表
  const getList = () => {
    getVarGroups().then(({ data: resData }) => {
      if (resData.data) {
        setData(resData.data)
      }
    })
  }

  // 增加
  const addClick = (e, type) => {
    e.stopPropagation()
    modalWrapperHoc(EditModal)({
      type,
      onOk: (values) =>
        createVarGroup({
          ...values,
          type,
        }).then(() => {
          message.success('添加成功')
          getList()
        }),
    })
  }

  // 编辑
  const editClick = (e, record) => {
    e.stopPropagation()
    modalWrapperHoc(EditModal)({
      type: record.type,
      groupId: record.id,
      name: record.groupName,
      onOk: (values: any) =>
        editVarGroup(record.id, { newGroupName: values.groupName }).then(() => {
          message.success('编辑成功')
          action.refresh()
          getList()
        }),
    })
  }

  // 删除
  const deleteClick = async (e, record) => {
    e.stopPropagation()
    // 请求接口判断该二级分类下的变量是否有模版引用
    const { data: result } = await getGroupRefList(record.id)

    if (result.data) {
      const hasRef = result.data.some((i) => i.refs.length !== 0)
      if (!hasRef) {
        // 没有模版引用
        Modal.confirm({
          title: '删除',
          okText: '确定',
          cancelText: '取消',
          content: (
            <span>
              {result.data.length
                ? `该分类已有${result.data.length}个变量，删除后该分类及其变量都会被删除不可恢复，确定要删除`
                : '分类删除后不可恢复，确定要删除'}
              <Button type="link" style={{ padding: 0 }}>
                {record.groupName}
              </Button>
              吗？
            </span>
          ),
          onOk: () =>
            deleteVarGroup(record.id).then(() => {
              message.success('删除成功')
              getList()
              setSelectedKeys([])
              action.refresh()
            }),
        })
      } else {
        // 有模版引用
        modalWrapperHoc(DeleteModal)({
          data: result.data,
          successFn: (codes) => {
            navigate(`${baseRoute}/contract/basic-config/template?codes=${codes}`)
          },
        })
      }
    }
  }

  // 获取变量分类数据
  const treeData = useMemo(() => {
    const defaultData: any = [
      {
        title: '全部',
        key: 'all',
        children: [
          {
            title: '基础变量',
            key: 'CM_VT_001',
            children: [],
          },
          // {
          //   title: (
          //     <>
          //       <span>业务变量</span>
          //       {getPermission(VAR_MANAGE.GROUP_TYPE.ADD) ? (
          //         <PlusOutlined onClick={(e) => addClick(e, 'CM_VT_002')} />
          //       ) : null}
          //     </>
          //   ),
          //   key: 'CM_VT_002',
          //   children: [],
          // },
          {
            title: (
              <div className={styles.title}>
                <span className={styles.name}>自定义变量</span>
                {getPermission(VAR_MANAGE.GROUP_TYPE.ADD) ? (
                  <span className={styles.operate}>
                    <PlusOutlined onClick={(e) => addClick(e, 'CM_VT_003')} />
                  </span>
                ) : null}
              </div>
            ),
            key: 'CM_VT_003',
            children: [],
          },
        ],
      },
    ]
    if (data) {
      // 处理数据
      const dataSource = search ? data.filter((i) => i.groupName.indexOf(search) !== -1) : data

      dataSource.forEach((i) => {
        const index = defaultData[0].children.findIndex((j) => j.key === i.type)
        if (index !== -1) {
          defaultData[0].children[index].children.push({
            title:
              i.type !== 'CM_VT_001' ? (
                <div className={styles.title}>
                  <Tooltip title={i.groupName} placement="topLeft">
                    <span className={styles.name}>{i.groupName}</span>
                  </Tooltip>
                  <span className={styles.operate}>
                    {getPermission(VAR_MANAGE.GROUP_TYPE.EDIT) ? (
                      <EditOutlined style={{ marginRight: 8 }} onClick={(e) => editClick(e, i)} />
                    ) : null}
                    {getPermission(VAR_MANAGE.GROUP_TYPE.DELETE) ? (
                      <DeleteOutlined onClick={(e) => deleteClick(e, i)} />
                    ) : null}
                  </span>
                </div>
              ) : (
                <span className={styles.title}>{i.groupName}</span>
              ),
            key: i.id,
          })
        }
      })

      // 搜索过滤一级分类
      if (search) {
        const children = defaultData[0].children.filter(
          (i) =>
            i.children.length !== 0 ||
            VAR_TYPE.find((j) => j.value === i.key)?.label.indexOf(search) !== -1,
        )
        defaultData[0].children = children
      }
    }

    if (defaultData[0].children?.length === 0) {
      // 没有搜索到不展示全部
      return []
    }
    return defaultData
  }, [data, search])

  const onSearch = (value) => {
    setSearch(value)
    // 设置展开项
    if (value) {
      const searchExpandedKeys = [
        ...new Set(data.filter((i) => i.groupName.indexOf(value) !== -1).map((j) => j.type)),
      ]
      setExpandedKeys(['all', ...searchExpandedKeys])
    } else {
      setExpandedKeys(['all'])
    }
  }

  const onSelect = (keys) => {
    setSelectedKeys(keys)
    action.refresh({ resetPage: true })
  }

  const onExpand = (keys) => {
    setExpandedKeys(keys)
  }

  useImperativeHandle(
    ref,
    () => {
      return {
        selectedKeys,
      }
    },
    [selectedKeys],
  )

  useEffect(() => {
    // 获取变量分类数据，目前只有二级
    getList()
  }, [])
  return (
    <div className={styles.typeTree}>
      <Search onSearch={onSearch} placeholder="请输入搜索内容" />
      {treeData?.length ? (
        <Tree
          motion={null}
          treeData={treeData}
          style={{ marginTop: 24 }}
          onSelect={onSelect}
          expandedKeys={expandedKeys}
          onExpand={onExpand}
          rootClassName={styles.treeContainer}
          selectedKeys={selectedKeys}
        />
      ) : (
        <Empty style={{ marginTop: 24 }} />
      )}
    </div>
  )
}

export default forwardRef(TypeTree)
