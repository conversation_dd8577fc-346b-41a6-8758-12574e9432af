import React from 'react'
import { Select } from 'antd'

type ValueType = {
  source: any
  field: any
}
interface IProps {
  value?: ValueType
  onChange: (any) => void
  disabled: boolean
  [fieldName: string]: any
}

const DataSource = (props: IProps) => {
  const { value, onChange, disabled = false } = props

  return (
    <div
      style={{
        display: 'flex',
      }}
    >
      <Select
        placeholder="数据源"
        value={value?.source}
        onChange={(values) => {
          onChange({
            ...value,
            source: values,
          })
        }}
        style={{
          marginRight: '8px',
        }}
        disabled={disabled}
      />
      <Select
        placeholder="对应字段"
        value={value?.field}
        onChange={(values) => {
          onChange({
            ...value,
            field: values,
          })
        }}
        disabled={disabled}
      />
    </div>
  )
}

export default DataSource
