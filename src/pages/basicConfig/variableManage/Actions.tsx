import { Modal, message } from 'antd'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import modalWrapperHoc from '@/components/modalWrapperHoc'
import {
  disabledVar,
  enabledVar,
  deleteVar as deleteVarApi,
  createVar as createVar<PERSON>pi,
  editVar as editVarApi,
} from '@/api/basicConfig/var-manage'
import DisableModal from './components/DisableModal'
import EditModal from './edit'

// 停用
export const disableVar = (record: any, successFn?: (actionName?: string, data?: any) => void) => {
  dhrReport.trace(REPORT_EVENT_TYPE.VARIABLE_DEACTIVATE, record)
  if (record.refCount) {
    // 关联模板
    modalWrapperHoc(DisableModal)({
      record,
      successFn,
    })
  } else {
    // 未关联模板
    Modal.confirm({
      content: '停用后将不能被引用，确定要停用变量吗？',
      title: '停用',
      closable: true,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        return disabledVar(record.id).then(() => {
          message.success('停用成功')
          successFn?.()
        })
      },
    })
  }
}

// 启用
export const enableVar = (record: any, successFn?: () => void) => {
  dhrReport.trace(REPORT_EVENT_TYPE.VARIABLE_ENABLE, record)
  Modal.confirm({
    title: '启用',
    content: `确定要启用【${record.variableName}】吗？`,
    closable: true,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      return enabledVar(record.id).then(() => {
        message.success('启用成功')
        successFn?.()
      })
    },
  })
}

// 删除
export const deleteVar = (record: any, successFn?: () => void) => {
  dhrReport.trace(REPORT_EVENT_TYPE.VARIABLE_DELETE, record)
  Modal.confirm({
    title: '删除',
    content: '删除后不可恢复，确定要删除变量吗？',
    okText: '确定',
    cancelText: '取消',
    closable: true,
    onOk: () => {
      return deleteVarApi(record.id).then(() => {
        message.success('删除成功')
        successFn?.()
      })
    },
  })
}

// 新增
export const createVar = (successFn?, typeTreeRef?) => {
  modalWrapperHoc(EditModal)({
    onOk: (values) => {
      return createVarApi(values).then(() => {
        message.success('新增成功')
        successFn?.()
      })
    },
    typeTreeRef,
  })
}

// 编辑
export const editVar = (id, successFn?) => {
  modalWrapperHoc(EditModal)({
    onOk: (values) => {
      return editVarApi(id, values).then(() => {
        message.success('编辑成功')
        successFn?.()
      })
    },
    id,
  })
}
