import React, { useEffect, useState } from 'react'
import { SchemaForm, Item } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/antd-schema-form'
import { Modal, Alert } from 'antd'
import {
  getVarDetail,
  getCodeExist,
  getNameExist,
  getVarGroups,
} from '@/api/basicConfig/var-manage'
// import DataSource from '../components/DataSource'
import TypeTreeSelect from './TypeTreeSelect'

interface IProps {
  visible: boolean
  close: () => void
  id?: string
  onOk: (any) => Promise<any>
  typeTreeRef?: any
}

const EditModal = (props: IProps) => {
  const { visible, close, onOk: okClick, id, typeTreeRef } = props
  const [loading, setLoading] = useState(false)
  const form = SchemaForm.createForm()

  // 变量编码规则校验
  const codeValidator = (_, value) => {
    const regex = /^[a-zA-Z0-9_]+$/
    if (!value) {
      return Promise.resolve()
    }
    const params = {
      code: value,
    }
    return getCodeExist(params).then((res) => {
      const {
        data: { data },
      } = res
      if (data) {
        return Promise.reject(new Error('变量编码已存在，请重新命名'))
      }
      if (regex.test(value)) {
        return Promise.resolve()
      }
      return Promise.reject(new Error('仅支持英文、数字、下划线'))
    })
  }

  // 名称是否重复
  const nameValidator = async (_, value) => {
    const groupId = form?.getFieldValue('groupId')
    if (value && groupId) {
      const params = {
        variableName: value,
        groupId,
        id,
      }

      return getNameExist(params).then(({ data: result }) => {
        if (result.data) {
          return Promise.reject(new Error('变量名称重复，请重新命名'))
        }
        return Promise.resolve()
      })
    }
    return Promise.resolve()
  }

  const schema: SchemaType = {
    variableCode: {
      label: '变量编码',
      component: 'Input',
      required: true,
      max: 20,
      rules: [
        {
          validator: id ? null : codeValidator,
        },
      ],
      disabled: !!id,
      itemProps: {
        tooltip: '仅支持英文、数字、下划线，建议以英文开头。不支持二次修改',
      },
    },
    groupId: {
      label: '变量分类',
      component: TypeTreeSelect,
      rules: [{ required: true, message: '请选择二级分类' }],
    },
    variableName: {
      label: '变量名称',
      component: 'Input',
      required: true,
      max: 20,
      rules: [
        {
          validator: nameValidator,
        },
      ],
    },
    // source: {
    // label: '数据来源',
    // component: DataSource,
    // disabled: true,
    // observer: Observer({
    //   watch: 'type',
    //   action: (value) => {
    //     if (value === 'CM_VT_002') {
    //       // 业务变量
    //       return {
    //         required: true,
    //         disabled: false,
    //       }
    //     }
    //     if (value === 'CM_VT_003') {
    //       // 自定义变量
    //       return {
    //         disabled: true,
    //         required: false,
    //       }
    //     }
    //     return {
    //       disabled: false,
    //       required: false,
    //     }
    //   },
    // }),
    // },
    remark: {
      label: '备注',
      component: 'Input.TextArea',
      max: 200,
      props: {
        rows: 4,
      },
    },
  }

  const onOk = async () => {
    try {
      const values = await form?.validateFields()
      setLoading(true)
      okClick(values)
        ?.then(() => {
          close()
        })
        .finally(() => {
          setLoading(false)
        })
    } catch (error) {
      console.error(error)
    }
  }

  useEffect(() => {
    if (id) {
      // 编辑
      getVarDetail(id).then(({ data: resData }) => {
        if (resData.data) {
          form?.setFieldsValue({ ...resData.data })
        }
      })
    } else {
      getVarGroups().then(({ data: resData }) => {
        if (typeTreeRef.current.selectedKeys.length > 0) {
          resData.data.forEach((item) => {
            if (item.id === typeTreeRef.current.selectedKeys[0] && item.type === 'CM_VT_003') {
              form?.setFieldsValue({
                groupId: typeTreeRef.current.selectedKeys[0],
              })
            }
          })
        }
      })
    }
  }, [])

  return (
    <Modal
      title={!id ? '新增变量' : '编辑变量'}
      visible={visible}
      onCancel={close}
      onOk={onOk}
      okButtonProps={{
        loading,
      }}
      width={650}
    >
      {id ? (
        <Alert
          message="修改变量会更新关联的所有模板，模板将按照最新的内容显示。"
          type="info"
          style={{ marginBottom: 24 }}
        />
      ) : null}
      <SchemaForm form={form} schema={schema}>
        <Item field="variableCode" />
        <Item field="groupId" />
        {/* <Item field="groupType" /> */}
        <Item field="variableName" />
        {/* <Item field="source" /> */}
        <Item field="remark" />
      </SchemaForm>
    </Modal>
  )
}

export default EditModal
