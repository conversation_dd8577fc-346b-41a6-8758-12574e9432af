import React, { useEffect, useState } from 'react'
import { TreeSelect } from 'antd'
import { getVarGroups } from '@/api/basicConfig/var-manage'

const TypeTreeSelect = (props) => {
  const [data, setData] = useState<any>([])

  useEffect(() => {
    getVarGroups().then(({ data: resData }) => {
      if (resData.data) {
        const defaultData: any = [
          // {
          //   title: '基础变量',
          //   value: 'CM_VT_001',
          //   selectable: false,
          //   children: [],
          // },
          // {
          //   title: '业务变量',
          //   value: 'CM_VT_002',
          //   selectable: false,
          //   children: [],
          // },
          {
            title: '自定义变量',
            value: 'CM_VT_003',
            selectable: false,
            children: [],
          },
        ]
        resData.data.forEach((i) => {
          const index = defaultData.findIndex((j) => j.value === i.type)
          if (index !== -1) {
            defaultData[index].children.push({
              title: i.groupName,
              value: i.id,
            })
          }
        })

        setData(defaultData)
      }
    })
  }, [])
  return <TreeSelect treeData={data} {...props} placeholder="请选择二级分类" />
}

export default TypeTreeSelect
