import React from 'react'
import { useNavigate } from 'react-router-dom'
import { RollbackOutlined } from '@ant-design/icons'
import { Button } from 'antd'
import { isLocalNetwork } from '@/utils/utils'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import PermissionAction from '@/components/permissionAction'
import PermissionDropdown from '@/components/permissionDropdown'
import { VAR_MANAGE } from '@/constants/rbac-code'
import { disableVar, enableVar, deleteVar, editVar } from '../Actions'

import styles from './styles.module.less'

const Extra = ({ data, refresh }: any) => {
  const navigate = useNavigate()
  const getActions = () => {
    const actions = [
      data.isEnable
        ? {
            code: VAR_MANAGE.LIST.DISABLED,
            component: '停用',
          }
        : {
            code: VAR_MANAGE.LIST.ENABLED,
            component: '启用',
          },
      {
        code: VAR_MANAGE.LIST.DELETE,
        component: '删除',
      },
    ]
    return actions
  }

  const operate = ({ key }) => {
    if (key === VAR_MANAGE.LIST.DISABLED) {
      // 停用
      disableVar(data, () => {
        refresh()
      })
    }
    if (key === VAR_MANAGE.LIST.ENABLED) {
      // 启用
      enableVar(data, () => {
        refresh()
      })
    }
    if (key === VAR_MANAGE.LIST.DELETE) {
      // 删除
      deleteVar(data, () => {
        navigate(-1)
      })
    }
  }

  return data ? (
    <div className={styles.extra}>
      {(window.__POWERED_BY_QIANKUN__ || isLocalNetwork()) && (
        <span
          className={styles.back}
          onClick={() => {
            navigate(-1)
          }}
        >
          <RollbackOutlined className={styles.icon} />
          返回
        </span>
      )}

      <PermissionAction code={VAR_MANAGE.LIST.EDIT} permissions={data.permissions}>
        <Button
          onClick={() => {
            dhrReport.trace(REPORT_EVENT_TYPE.VARIABLE_EDIT, data)
            editVar(data.id, () => {
              refresh()
            })
          }}
        >
          编辑
        </Button>
      </PermissionAction>

      <PermissionDropdown
        actions={getActions()}
        permissions={data.permissions}
        showIcon={false}
        onClick={operate}
      >
        <Button>更多</Button>
      </PermissionDropdown>
    </div>
  ) : null
}

export default Extra
