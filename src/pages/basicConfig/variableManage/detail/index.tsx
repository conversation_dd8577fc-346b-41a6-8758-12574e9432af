import React, { useEffect, useState } from 'react'
import { useStore } from '@/stores'
import { useLocation } from 'react-router-dom'
import { SchemaForm, Item, FORM_MODE } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/antd-schema-form'
import queryString from 'query-string'
import moment from 'moment'
import { getVarDetail } from '@/api/basicConfig/var-manage'
import Extra from './Extra'
import RedirectToTemplate from '../components/RedirectToTemplate'

import styles from './styles.module.less'

const VarDetail = () => {
  const store = useStore((state) => state)
  const { setExtra } = store

  const location = useLocation()
  const { id } = queryString.parse(location.search)
  const [detail, setDetail] = useState<any>()
  const form = SchemaForm.createForm()

  const schema: SchemaType = {
    typeName: {
      label: '变量分类',
      component: 'Text',
    },
    variableName: {
      label: '变量名称',
      component: 'Text',
    },
    variableCode: {
      label: '变量编码',
      component: 'Text',
    },
    source: {
      label: '数据来源',
      component: 'Text',
    },
    refCount: {
      label: '适用模板',
      component: ({ value }) => (
        <RedirectToTemplate number={value} id={detail?.id} record={detail} />
      ),
    },
    sponsorName: {
      label: '创建人',
      component: 'Text',
    },
    sponsorTime: {
      label: '创建时间',
      component: 'Text',
    },
    isEnable: {
      label: '状态',
      component: 'Text',
    },
    disableTime: {
      label: '停用日期',
      component: 'Text',
    },
    remark: {
      label: '备注',
      component: 'Input.TextArea.Text',
    },
  }

  const getDetail = () => {
    getVarDetail(id).then(({ data: resData }) => {
      if (resData.data) {
        const {
          remark,
          source,
          sponsorName,
          sponsorNum,
          sponsorTime,
          disableTime,
          isEnable,
          typeName,
          groupName,
          ...rest
        } = resData.data
        const formData = {
          typeName: `${typeName || '--'}/${groupName || '--'}`,
          remark: remark || '--',
          source: source || '--',
          sponsorName: sponsorName
            ? `${sponsorName}${sponsorNum ? `（${sponsorNum}）` : ''}`
            : '--',
          sponsorTime: sponsorTime ? moment(sponsorTime).format('YYYY-MM-DD HH:mm:ss') : '--',
          disableTime: disableTime ? moment(disableTime).format('YYYY-MM-DD') : '--',
          isEnable: isEnable ? '启用中' : '已停用',
          ...rest,
        }
        form.setFieldsValue(formData)
        setDetail(resData.data)
      }
    })
  }

  useEffect(() => {
    getDetail()
  }, [])

  useEffect(() => {
    setExtra(<Extra data={detail} refresh={getDetail} />)
    return () => {
      setExtra(null)
    }
  }, [detail])

  return detail ? (
    <div className={styles.detail}>
      <SchemaForm schema={schema} form={form} mode={FORM_MODE.DETAIL} initialValues={detail}>
        <Item field="typeName" />
        <Item field="variableName" />
        <Item field="variableCode" />
        <Item field="source" />
        <Item field="refCount" />
        <Item field="sponsorName" />
        <Item field="sponsorTime" />
        <Item field="isEnable" />
        <Item field="disableTime" />
        <Item field="remark" />
      </SchemaForm>
    </div>
  ) : null
}

export default VarDetail
