.varManage {
  margin: 24px;
  display: flex;
  position: relative;
  .fold {
    display: none;
  }
  .rightIcon {
    position: absolute;
    cursor: pointer;
    top: 50%;
    left: -20px;
    display: inline-block;
    height: 50px;
    width: 20px;
    line-height: 50px;
    text-align: center;
    background-color: white;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
  }
  .leftIcon {
    position: absolute;
    top: 50%;
    left: 300px;
    cursor: pointer;
    display: inline-block;
    height: 50px;
    width: 20px;
    line-height: 50px;
    text-align: center;
    background-color: white;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
  }
  .left {
    width: 300px;
    margin-right: 24px;
    background-color: white;
    .title {
      font-size: 16px;
      font-weight: 500;
      width: 300px;
      line-height: 50px;
      padding-left: 24px;
    }
  }
  .right {
    flex: 1;
    overflow-x: hidden;
    background-color: white;
    .title {
      padding: 0 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .total {
        font-size: 16px;
        font-weight: 500;
        width: 100%;
        line-height: 50px;
      }
    }
  }
}

.detail {
  margin: 24px;
  background-color: white;
  height: calc(100vh - 205px);
  padding-top: 100px;
  position: relative;
  .btn {
    position: absolute;
    bottom: 24px;
    right: 24px;
    .cancel {
      margin-right: 8px;
    }
  }
}

.title {
  display: flex;
  align-items: center;
  .name {
    margin-right: 8px;
  }
  .icon {
    font-size: 16px;
    font-weight: 400;
  }
}

.tag {
  color: orange
}
