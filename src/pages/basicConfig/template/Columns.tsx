import React from 'react'
import { SchemaColumnType } from '@amazebird/antd-schema-table'
import { TreeSelect as Dict } from '@galaxy/dict'
import { Tooltip, Button, message, Modal } from 'antd'

import PermissionAction from '@/components/permissionAction'
import PermissionDropdown, {
  ActionProps,
  PermissionDropdownProps,
} from '@/components/permissionDropdown'
import { STATUS, FDD_STATUS } from '@/constants'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import { TEMPLATE_MANAGE } from '@/constants/rbac-code'
import { ITemplateItem } from '@/types/template'
import { getRelatedBusiness } from '@/api/basicConfig/template'
import { RedirectToPackagePage } from '../contractPackage/common'
import styled from './styles.module.less'

interface IProps {
  toDetail: (record) => void
  toEdit: (record) => void
  toDelete: (record) => void
  moreActionClick: (key, record) => void
  exportVariable: (record: ITemplateItem) => Promise<any>
  uploadFdd: (record: ITemplateItem) => void
}

// 变量
const CellToolTip = ({ text }: any) => (
  <Tooltip title={text} placement="topLeft">
    {text}
  </Tooltip>
)

const getRelated = async () => {
  const {
    data: { data },
  } = await getRelatedBusiness()
  const result: any = []
  data.forEach((item) => {
    const obj = {
      label: `${item.applicationName}-${item.businessItemName}`,
      value: `${item.applicationCode}-${item.businessItemCode}`,
    }
    result.push(obj)
  })
  return result
}

// 更多
export const MoreAction = ({
  actions = [],
  isDetailPage = false,
  ...restProps
}: { actions: ActionProps[]; isDetailPage?: boolean } & Omit<
  PermissionDropdownProps,
  'actions'
>) => (
  <PermissionDropdown
    actions={actions}
    ucPermissionControl={false}
    workPermissionControl={false}
    {...restProps}
  >
    {isDetailPage ? <Button>更多</Button> : '更多'}
  </PermissionDropdown>
)

export const createColumns: (args: IProps) => SchemaColumnType = ({
  toDetail,
  toEdit,
  toDelete,
  moreActionClick,
  exportVariable,
  uploadFdd,
}) => {
  const moreActions = (
    status: number | string,
    record: { id: number; name: string; status: number; [field: string]: any },
  ) => {
    const isExistPermissions = record.permissions?.filter(
      (item) => item.code === TEMPLATE_MANAGE.LIST.DELETE,
    )
    const permissions = Array.isArray(isExistPermissions) ? isExistPermissions[0] : undefined
    const isEnabled = String(status) === '1'
    const actionParams = isEnabled
      ? {
          component: '停用',
          code: TEMPLATE_MANAGE.LIST.DISABLED,
          onClick: () => moreActionClick(TEMPLATE_MANAGE.LIST.DISABLED, record),
        }
      : {
          component: '启用',
          code: TEMPLATE_MANAGE.LIST.ENABLED,
          onClick: () => moreActionClick(TEMPLATE_MANAGE.LIST.ENABLED, record),
        }
    const actions = [
      actionParams,
      {
        component: (
          <span style={permissions?.hasPermission ? { color: 'red' } : { color: '#BFBFBF' }}>
            删除
          </span>
        ),
        code: TEMPLATE_MANAGE.LIST.DELETE,
        onClick: () => {
          dhrReport.trace(REPORT_EVENT_TYPE.TEMPLATE_DELETE, record)
          Modal.confirm({
            title: '删除',
            content: '删除后不可恢复，确定要删除模板吗？',
            okText: '确定',
            cancelText: '取消',
            onOk: () => toDelete(record),
          })
        },
      },
    ]
    return actions
  }
  return [
    {
      dataIndex: 'name',
      title: '模板名称',
      cell: CellToolTip,
      fixed: 'left',
      ellipsis: true,
      width: 150,
    },
    {
      dataIndex: 'status',
      title: '状态',
      cell: 'Status',
      options: [...STATUS],
    },
    {
      dataIndex: 'fddStatus',
      title: '上传法大大状态',
      cell: 'Status',
      options: FDD_STATUS,
    },
    {
      dataIndex: 'number',
      title: '模板编号',
    },
    {
      dataIndex: 'isSupportInner',
      title: '分类',
      cell: {
        type: 'Text',
        render: ({ record }) => {
          const arr: string[] = []
          if (record.isSupportInner) {
            arr.push('朴朴员工模板')
          }
          if (record.isSupportOuter) {
            arr.push('自营第三方模板')
          }
          return arr.length ? arr.join('、') : '--'
        },
      },
    },
    {
      dataIndex: 'typeName',
      title: '模板类型',
      cell: 'Text',
    },
    {
      dataIndex: 'signMethodName',
      title: '签署方式',
      width: 180,
      cell: 'Text',
    },
    {
      dataIndex: 'sealName',
      title: '关联印章',
      cell: 'Text',
    },
    {
      dataIndex: 'sealLocateModeName',
      title: '印章定位方式',
      cell: 'Text',
    },
    {
      dataIndex: 'relationList',
      title: '关联业务项',
      cell: {
        type: 'Text',
        render: ({ record }) => {
          const arr: string[] = record.relationList.map((item) => {
            if (item.applicationName && item.businessItemName) {
              return `${item.applicationName}-${item.businessItemName}`
            }
            if (!item.applicationName && item.businessItemName) {
              return item.businessItemName
            }
            if (item.applicationName && !item.businessItemName) {
              return item.applicationName
            }
            return '--'
          })
          return arr.length > 0 ? (
            <Tooltip title={arr.join('、')} className={styled.ellipsis}>
              <div>{arr.join('、')}</div>
            </Tooltip>
          ) : (
            '--'
          )
        },
      },
    },
    {
      dataIndex: 'referenceCount',
      title: '适用合同包',
      width: 180,
      cell: ({ text, record }) => (
        <RedirectToPackagePage number={text} id={record.id} type={3} record={record} />
      ),
    },
    {
      dataIndex: 'disableDate',
      title: '停用日期',
      cell: 'Date',
    },
    {
      dataIndex: 'timeCreate',
      title: '创建时间',
      cell: 'DateTime',
    },
    {
      dataIndex: 'userNameCreate',
      title: '创建人',
      width: 200,
      render: (value, record) => (value ? `${value}（${record.userIdCreate || '--'}）` : '--'),
    },
    {
      title: '操作',
      key: 'operator',
      width: 300,
      fixed: 'right',
      cell: {
        type: 'Operator',
        render: ({ record }) => (
          <>
            <PermissionAction code={TEMPLATE_MANAGE.LIST.VIEW} permissions={record.permissions}>
              <Button
                type="link"
                size="small"
                style={{
                  paddingLeft: 0,
                }}
                onClick={() => toDetail(record)}
              >
                查看
              </Button>
            </PermissionAction>
            <PermissionAction code={TEMPLATE_MANAGE.LIST.EDIT} permissions={record.permissions}>
              <Button type="link" size="small" onClick={() => toEdit(record)}>
                编辑
              </Button>
            </PermissionAction>
            <PermissionAction
              code={TEMPLATE_MANAGE.LIST.DOWNLOAD_VAR}
              permissions={record.permissions}
            >
              <Tooltip
                title={
                  <div>
                    <div>增删模板变量，需要导出变量表发给对应开发同学。</div>
                  </div>
                }
              >
                <Button
                  type="link"
                  size="small"
                  onClick={() =>
                    exportVariable(record)?.then(() => {
                      message.success('导出成功')
                    })
                  }
                >
                  导出变量
                </Button>
              </Tooltip>
            </PermissionAction>
            <PermissionAction
              code={TEMPLATE_MANAGE.LIST.UPLOAD}
              permissions={record.permissions}
              tooltip={{
                title: '模板未更新或已被停用，无需上传法大大',
              }}
            >
              <Button type="link" size="small" onClick={() => uploadFdd(record)}>
                上传法大大
              </Button>
            </PermissionAction>
            <PermissionDropdown
              actions={moreActions(record.status, record)}
              permissions={record.permissions}
            >
              更多
            </PermissionDropdown>
            {/* {operates(record.status).map((c) => (
              <PermissionAction key={c.code} code={c.code} permissions={record.permissions}>
                <Button type="link" size="small" onClick={() => moreActionClick(c.code, record)}>
                  {c.component}
                </Button>
              </PermissionAction>
            ))} */}
            {/* <PermissionAction code={TEMPLATE_MANAGE.LIST.DELETE} permissions={record.permissions}>
              <Delete
                onDelete={toDelete}
                record={record}
                content="删除后不可恢复，确定要删除模板吗？"
                buttonProps={{
                  onClick: () => dhrReport.trace(REPORT_EVENT_TYPE.TEMPLATE_DELETE, record),
                }}
              />
            </PermissionAction> */}
          </>
        ),
      },
    },
  ]
}

export const createSearchColumns = () => [
  {
    dataIndex: 'name',
    title: '模板名称',
    component: 'Input',
    props: {
      allowClear: true,
    },
  },
  {
    dataIndex: 'number',
    title: '模板编号',
    component: 'Input',
    props: {
      allowClear: true,
    },
  },
  {
    dataIndex: 'status',
    title: '状态',
    component: 'Select',
    options: [
      {
        label: '全部',
        value: '',
      },
      ...STATUS,
    ],
  },
  {
    dataIndex: 'classification',
    title: '分类',
    component: 'Select',
    options: [
      {
        label: '朴朴员工模板',
        value: 0,
      },
      {
        label: '自营第三方模板',
        value: 1,
      },
    ],
    props: {
      mode: 'multiple',
    },
  },
  {
    dataIndex: 'fddStatus',
    title: '上传法大大状态',
    component: 'Select',
    options: FDD_STATUS,
  },
  {
    title: '模板类型',
    dataIndex: 'typeCode',
    component: Dict,
    props: {
      showSearch: true,
      allowClear: true,
      code: 'TEMPLATE_TYPE',
      placeholder: '请选择模板类型',
    },
  },
  {
    dataIndex: 'signMethodCode',
    title: '签署方式',
    component: Dict,
    props: {
      showSearch: true,
      allowClear: true,
      code: 'SIGN_TYPE',
      placeholder: '请选择签署方式',
    },
  },
  {
    dataIndex: 'sealCode',
    title: '关联印章',
    component: Dict,
    props: {
      showSearch: true,
      allowClear: true,
      code: 'RELATED_SEAL',
      placeholder: '请选择关联印章',
    },
  },
  {
    dataIndex: 'sealLocateModeCode',
    title: '印章定位方式',
    component: Dict,
    props: {
      showSearch: true,
      allowClear: true,
      code: 'LOCATE_MODE',
      placeholder: '请选择印章定位方式',
    },
  },
  {
    dataIndex: 'relationBusinessItem',
    title: '关联业务项',
    component: 'Select',
    options: getRelated,
    props: {
      placeholder: '请选择关联业务项',
      showSearch: true,
      allowClear: true,
      optionFilterProp: 'label',
    },
  },
  {
    dataIndex: 'disableDate',
    title: '停用日期',
    component: 'RangePicker',
  },
]
