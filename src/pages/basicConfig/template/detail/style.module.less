.templateDetailWrapper {
  overflow: hidden;

  .pdfWrapper {
    margin: 0px 20px 0px 20px;

    .pdfEditor {
      width: 100%
    }
  }
}

.step {
  margin: 20px;
  padding: 18px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;

  .steps {
    width: 40%;
  }
}

.schemaWrapper {
  background-color: white;
  margin: 20px 20px 40px 20px;
  min-height: 45vh;

  &.loading {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .schema {
    padding: 40px 20px;
    width: 60vw;
    margin: 0 auto;
  }
}

.configPanelDetail {
  width: 350px;
  border-left: 1px solid #d2d2d2;

  .formItemWrapper {
    display: flex;
    padding: 18px 22px;

    p {
      margin: 0;
    }

    .key {
      color: #9c9c9c;
      min-width: fit-content;
    }

    .desc {
      margin-top: 5px;
      font-size: 12px;
      color: #9c9c9c;
    }
  }

  .keywordItem {
    display: flex;
    align-items: center;
    margin: 0px 16px 20px;
  }

  .desc {
    color: #aeaeae;
    font-size: 12px;
  }

  .positionInfo {
    margin: 5px 14px 30px;
    font-size: 12px;
    color: #a3a3a3;
  }
}

.extendSlot {
  display: inline-block;
  margin-right: 10px;
}

.switch {
  z-index: -1;
  opacity: 0;
  position: absolute;
  height: 0;
  overflow: hidden;
}