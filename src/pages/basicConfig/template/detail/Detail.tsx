import React, { useState, useEffect, useMemo } from 'react'
import { dictLocateMode } from '@/api/dict'
import { SealBox, STAMP_VAR_TYPE, SEAL_TYPE, SIGN_TYPE } from '@/components/pdfEditor'
import type { StampVarType, Store } from '@/components/pdfEditor'
import styles from './style.module.less'

type CalcPositionFunc = (str: string) => [number, number[]]

interface IProps {
  store: Store
  sealCode: string
  signType: string
}

const Keyword = ({ store, sealCode, signType }: IProps) => {
  const [locateModeOptions, setLocateModeOptions] = useState<any>([])
  const boxes = store((state) => state.boxes)
  const search = store((state) => state.search)
  const content = store((state) => state.content)
  const locateMode = store((state) => state.locateMode)
  const isEnterprise = useMemo(
    () => signType === SIGN_TYPE.BOTH_SIGN || signType === SIGN_TYPE.ENTERPRISE_SIGN,
    [signType],
  )
  const isPerson = useMemo(
    () => signType === SIGN_TYPE.BOTH_SIGN || signType === SIGN_TYPE.PERSON_SIGN,
    [signType],
  )

  const calcPosition: CalcPositionFunc = (str) => {
    let occurNum = 0
    const pageNumbers: Array<number> = []

    for (let i = 0; i < content.length; i++) {
      const text = content[i].content
      const occur = text.split(str).length - 1

      if (occur) {
        occurNum += occur
        pageNumbers.push(i + 1)
      }
    }
    return [occurNum, pageNumbers]
  }

  const getPositionInfo = (value) => {
    const [signatureCount, signaturePage] = calcPosition(value)
    return !signatureCount
      ? '没有对应的关键字信息'
      : `共 ${signatureCount} 处，${signatureCount === 1 ? '' : '分别'}在第 ${signaturePage.join(
          '、',
        )} 页`
  }

  const getLocateMode = async () => {
    const { LOCATE_MODE } = await dictLocateMode()
    setLocateModeOptions(LOCATE_MODE)
  }

  /**
   * 生成印章、签名的位置信息
   */
  const generatePositionInfo = (type: StampVarType) => {
    const filterBoxes = boxes.filter((box) => box.type === type)
    const page = Array.from(new Set(filterBoxes.map((b) => b.page + 1)))
    return page.length > 0
      ? `共 ${filterBoxes.length} 处，${page.length === 1 ? '' : '分别'}在第 ${page.join('、')} 页`
      : ''
  }

  const StampElement = () => (
    <>
      {sealCode && (
        <div className={styles.keywordItem}>
          <SealBox
            type={1}
            sealType={sealCode === 'CM_RS_001' ? SEAL_TYPE.EnterpriseSeal : SEAL_TYPE.HrSeal}
            disabled
          />
          <span className={styles.desc}>{generatePositionInfo(STAMP_VAR_TYPE.SEAL)}</span>
        </div>
      )}

      {(!sealCode || (sealCode && signType === 'CM_ST_001')) && (
        <div className={styles.keywordItem}>
          <SealBox type={2} sealType={SEAL_TYPE.SignatureSeal} disabled />
          <span className={styles.desc}>{generatePositionInfo(STAMP_VAR_TYPE.SIGNATURE)}</span>
        </div>
      )}
    </>
  )

  useEffect(() => {
    getLocateMode()
  }, [])

  return (
    <div className={styles.configPanelDetail}>
      <div className={styles.formItemWrapper}>
        <span className={styles.key}>印章定位方式：</span>
        <span className={styles.value}>
          {locateModeOptions.find((v) => v.code === locateMode)?.name || ''}
        </span>
      </div>
      {locateMode === 'CM_LM_001' ? (
        StampElement()
      ) : (
        <>
          {isEnterprise && (
            <div className={styles.formItemWrapper}>
              <p className={styles.key}>企业印章关键字：</p>
              <div className={styles.value}>
                <p>{search[0] || '--'}</p>
                {search[0] && <p className={styles.desc}>{getPositionInfo(search[0])}</p>}
              </div>
            </div>
          )}
          {isEnterprise && (
            <div className={styles.formItemWrapper}>
              <p className={styles.key}>企业印章关键字2：</p>
              <div className={styles.value}>
                <p>{search[2] || '--'}</p>
                {search[2] && <p className={styles.desc}>{getPositionInfo(search[2])}</p>}
              </div>
            </div>
          )}
          {isPerson && (
            <div className={styles.formItemWrapper}>
              <p className={styles.key}>员工签名关键字：</p>
              <div className={styles.value}>
                <p>{search[1] || '--'}</p>
                {search[1] && <p className={styles.desc}>{getPositionInfo(search[1])}</p>}
              </div>
            </div>
          )}
        </>
      )}
    </div>
  )
}

export default Keyword
