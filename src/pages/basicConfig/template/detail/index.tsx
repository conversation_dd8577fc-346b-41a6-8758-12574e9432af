import React, { useEffect, useMemo, useState } from 'react'
import { useStore } from '@/stores'
import { useSearchParams } from 'react-router-dom'
import { Button, Steps } from 'antd'
import { toCamelCase } from '@galaxy/utils'
import moment from 'moment'
import { getTemplateDetailApi } from '@/api/basicConfig/template'
import { getFileDataServiceApi } from '@/service/fileService'
import PdfEdit, { FileProps } from '@/components/pdfEditor'
import BasicInfo from './BasicInfo'
import Extra from './component/Extra'
import styles from './style.module.less'
import Detail from './Detail'

const TemplateDetail = () => {
  const store = useStore((state) => state)
  const { setExtra } = store

  const [search] = useSearchParams()
  const [baseInfoLoading, setBaseInfoLoading] = useState(false)
  const [baseInfo, setBaseInfo] = useState<any>({})
  const [initBoxes, setInitBoxes] = useState<any>([])
  const [initSearch, setInitSearch] = useState<any>([])
  const [initLocateMode, setInitLocateMode] = useState<any>([])
  const [initFontParams, setInitFontParams] = useState<any>({} as any)
  const [file, setFile] = useState<FileProps>({} as any)
  const [current, setCurrent] = useState(0)
  const [total, setTotal] = useState(0)

  const steps = [
    {
      title: '设置基础信息',
      content: '设置基础信息',
    },
    {
      title: '维护模板内容',
      content: '维护模板内容',
    },
  ]

  const items = steps.map((item) => ({
    key: item.title,
    title: item.title,
  }))

  const ExtendSlotElement = (
    <div className={styles.extendSlot}>
      {current === 1 && (
        <Button className={styles.btn} type="primary" onClick={() => setCurrent((num) => num - 1)}>
          上一步
        </Button>
      )}
      {current === 0 && (
        <Button className={styles.btn} type="primary" onClick={() => setCurrent((num) => num + 1)}>
          下一步
        </Button>
      )}
    </div>
  )

  // 初始化合同模板
  const initParams = async (id) => {
    setBaseInfoLoading(true)
    try {
      const { data: result } = await getTemplateDetailApi(id)
      const {
        fileId,
        fileName,
        templateContent = {},
        variableNameMap = {},
        ...restProps
      } = result.data
      const { sealList = [], variableList = [] } = templateContent
      const seal = sealList.map((v) => JSON.parse(v.info))
      const vars = variableList.map((v) => ({
        ...JSON.parse(v.info || '{}'),
        title: variableNameMap[toCamelCase(v.variableCode)],
      }))
      const fileData = await getFileDataServiceApi(fileId)
      setFile({ name: fileName, data: `data:application/pdf;base64,${fileData}` })

      const useCategory = ([] as Array<string>)
        .concat(restProps.isSupportInner ? ['朴朴员工模板'] : [])
        .concat(restProps.isSupportOuter ? ['自营第三方模板'] : [])
        .join('、')

      const useRestProps = {
        ...restProps,
        code: restProps.code || '--',
        category: useCategory || '--',
        sealName: restProps.sealName || '--',
        remark: restProps.remark || '--',
        status: restProps.status === 1 ? '启用中' : '已停用',
        relationList:
          Array.isArray(restProps.relationList) && restProps.relationList?.length > 0
            ? restProps.relationList
                .map((v) => `${v.applicationName || ''}-${v.businessItemName || ''}`)
                .join('、')
            : '--',
        timeCreate: restProps.timeCreate
          ? moment(restProps.timeCreate).format('YYYY-MM-DD HH:mm:ss')
          : '--',
        disableDate: restProps.disableDate
          ? moment(restProps.disableDate).format('YYYY-MM-DD')
          : '--',
        userNameCreate: restProps.userNameCreate
          ? `${restProps.userNameCreate}(${restProps.userIdCreate})`
          : '--',
      }
      const { sealKeyWord = '', signatureKeyWord = '', sealKeyWord2 } = templateContent
      setInitBoxes([...seal, ...vars])
      setBaseInfo(useRestProps)
      setInitSearch([sealKeyWord, signatureKeyWord, sealKeyWord2])
      setInitFontParams({ fontType: restProps.fontType, fontSize: restProps.fontSize })
      setInitLocateMode(restProps.sealLocateModeCode)
    } finally {
      setBaseInfoLoading(false)
    }
  }

  const docLoaded = (pageTotal: number) => setTotal(pageTotal)

  const pageHeaderParams = useMemo(
    () => ({
      total,
      preTotal: 0,
      pageIndex: 0,
    }),
    [total],
  )

  useEffect(() => {
    const templateId = search.get('id')
    if (templateId) {
      initParams(templateId)
    }
  }, [])

  useEffect(() => {
    const id = search.get('id')
    setExtra(
      <Extra
        id={id}
        data={baseInfo}
        extendSlot={ExtendSlotElement}
        refresh={() => initParams(id)}
      />,
    )

    return () => {
      setExtra(null)
    }
  }, [baseInfo, current])

  return (
    <div className={styles.templateDetailWrapper}>
      <div className={styles.step}>
        <Steps current={current} items={items} className={styles.steps} />
      </div>
      <BasicInfo
        initValues={baseInfo}
        loading={baseInfoLoading}
        extraClassName={current !== 0 ? styles.switch : ''}
      />
      <div className={styles.pdfWrapper}>
        <PdfEdit
          extraClassName={current !== 1 ? styles.switch : styles.pdfEditor}
          readOnly
          file={file}
          useVirtual={false}
          initSearch={initSearch}
          initBoxes={initBoxes}
          initLocateMode={initLocateMode}
          initFontParams={initFontParams}
          signType={baseInfo.signType}
          sealCode={baseInfo.sealCode}
          pageHeaderConfig={pageHeaderParams}
          docLoaded={docLoaded}
          detailSlot={(_store) => (
            <Detail
              store={_store}
              signType={baseInfo.signMethodCode}
              sealCode={baseInfo.sealCode}
            />
          )}
        />
      </div>
    </div>
  )
}

export default TemplateDetail
