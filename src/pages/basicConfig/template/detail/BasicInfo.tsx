import React, { useEffect, useMemo } from 'react'
import { Spin } from 'antd'
import type { SchemaType } from '@amazebird/schema-form'
import { SchemaForm, Item, FORM_MODE, FormItemGrid } from '@amazebird/antd-schema-form'

import classNames from 'classnames'
import { templateFormatTip } from '@/constants/template'
import { FDD_STATUS } from '@/constants'

import styles from './style.module.less'

interface IProps {
  loading: any
  initValues: any
  extraClassName?: string
}

const BasicInfo = ({ loading, extraClassName, initValues = {} }: IProps) => {
  const form = SchemaForm.createForm()

  const schema: SchemaType = useMemo(
    () => ({
      name: {
        label: '模板名称',
        component: 'Text',
      },
      number: {
        label: '模板编号',
        component: 'Text',
      },
      category: {
        label: '分类',
        component: 'Text',
      },
      typeName: {
        label: '模板类型',
        component: 'Text',
      },
      signMethodName: {
        label: '签署方式',
        component: 'Text',
      },
      sealName: {
        label: '关联印章',
        component: 'Text',
      },
      code: {
        label: '对应合同编码',
        component: 'Text',
      },
      relationList: {
        label: '关联业务项',
        component: 'Text',
      },
      userNameCreate: {
        label: '创建人',
        component: 'Text',
      },
      timeCreate: {
        label: '创建时间',
        component: 'Text',
      },
      fddStatus: {
        label: '上传法大大状态',
        component: 'Select',
        options: FDD_STATUS,
      },
      status: {
        label: '状态',
        component: 'Text',
      },
      disableDate: {
        label: '停用日期',
        component: 'Text',
      },
      remark: {
        label: '模板说明',
        component: 'Text',
      },
    }),
    [],
  )

  useEffect(() => {
    form.setFieldsValue(initValues)
  }, [initValues])

  return (
    <div
      className={classNames(styles.schemaWrapper, loading ? styles.loading : '', extraClassName)}
    >
      {loading ? (
        <Spin />
      ) : (
        <SchemaForm
          schema={schema}
          form={form}
          className={styles.schema}
          mode={FORM_MODE.DETAIL}
          labelCol={{
            span: 8,
          }}
          wrapperCol={{
            span: 16,
          }}
        >
          <FormItemGrid colCount={2}>
            <Item field="name" tooltip={templateFormatTip} />
            <Item field="number" />
            <Item field="category" />
            <Item field="typeName" />
            <Item field="signMethodName" />
            <Item field="sealName" />
            <Item field="code" tooltip="该字段主要用于合同编号规则中的二级编码生成。" />
            <Item field="relationList" />
            <Item field="fddStatus" />
            <Item field="status" />
            <Item field="disableDate" />
            <Item field="userNameCreate" />
          </FormItemGrid>
          <Item field="timeCreate" labelCol={{ span: 4 }} wrapperCol={{ span: 8 }} />
          <Item field="remark" labelCol={{ span: 4 }} wrapperCol={{ span: 8 }} />
        </SchemaForm>
      )}
    </div>
  )
}

export default BasicInfo
