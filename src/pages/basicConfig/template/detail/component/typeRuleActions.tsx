import React from 'react'
import { Modal, message } from 'antd'
import modal<PERSON>rapperHoc from '@/components/modalWrapperHoc'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import {
  getTemplateReference,
  disableTemplateApi,
  enableTemplateApi,
  deleteTemplateApi,
  downloadVar,
  uploadFdd,
} from '@/api/basicConfig/template'
import { TEMPLATE_MANAGE } from '@/constants/rbac-code'
import EndModal from './EndModal'

// 停用类型规则
export const endTypeRuleFn = async (
  record: any,
  successCallback?: (actionName?: string, data?: any) => void,
) => {
  // 需要先调用详情接口判断是否有关联合同包
  const { data: result } = await getTemplateReference(record.id)

  const contractPackage = result.data

  const execute = (content?: any) =>
    Modal.confirm({
      title: '停用',
      content: content || '停用后将不能被引用，确定要停用模板吗？',
      okText: '确定',
      cancelText: '取消',
      width: content ? 650 : undefined,
      onOk: () =>
        new Promise((resolve, reject) => {
          disableTemplateApi(record.id)
            .then(() => {
              successCallback?.()
              message.success('停用成功')
              resolve(true)
            })
            .catch(() => {
              reject()
            })
        }),
    })

  if (contractPackage.length > 0) {
    // 有合同包使用规则
    modalWrapperHoc(EndModal)({
      data: contractPackage,
      record,
      type: 'type',
      onOk: () => {
        successCallback?.('redirect', record.id)
      },
    })
  } else if (record.relationList !== '--') {
    const relationList = record.relationList.split('、').map((item) => ({
      applicationName: item.split('-')[0],
      businessItemName: item.split('-')[1],
    }))
    const content = (
      <div>
        <div
          style={{
            marginBottom: 8,
          }}
        >
          {`当前有 ${relationList?.length} 个业务项正在使用该模板，停用后该业务项发起签署模板将会缺失！确定停用后，请及时新增该业务项模板。`}
        </div>
        <div>{`关联业务项：${relationList
          ?.map((i) => {
            if (i.applicationName && i.businessItemName) {
              return `${i.applicationName}-${i.businessItemName}`
            }
            if (!i.applicationName && i.businessItemName) {
              return i.businessItemName
            }
            if (i.applicationName && !i.businessItemName) {
              return i.applicationName
            }
            return '--'
          })
          .join('、')}`}</div>
      </div>
    )
    execute(content)
  } else {
    // 无合同包使用规则
    execute()
  }
}

// 启用模板
export const startTypeRuleFn = (
  record: { name: string; id: number; [field: string]: any },
  successCallback?: (actionName?: string, data?: any) => void,
) => {
  Modal.confirm({
    title: '启用',
    content: `确定要启用【${record.name}】吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: () =>
      new Promise((resolve, reject) => {
        enableTemplateApi(record.id)
          .then(() => {
            successCallback?.()
            message.success('启用成功')
            resolve(true)
          })
          .catch(() => {
            reject()
          })
      }),
  })
}

// 删除模板
export const deleteTypeRuleFn = (
  id: number,
  successCallback?: (actionName?: string, data?: any) => void,
) => {
  Modal.confirm({
    title: '删除',
    content: '删除后不可恢复，确定要删除吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () =>
      new Promise((resolve, reject) => {
        deleteTemplateApi(id)
          .then(() => {
            successCallback?.()
            message.success('删除成功')
            resolve(true)
          })
          .catch(() => {
            reject()
          })
      }),
  })
}

// 类型规则的操作项
export const operateTypeRule = (
  key: string,
  record,
  successCallback?: (actionName?: string, data?: any) => void,
) => {
  // 停用
  if (key === TEMPLATE_MANAGE.LIST.DISABLED) {
    dhrReport.trace(REPORT_EVENT_TYPE.TEMPLATE_DEACTIVATE, record)
    endTypeRuleFn(record, successCallback)
  }
  // 启用
  if (key === TEMPLATE_MANAGE.LIST.ENABLED) {
    dhrReport.trace(REPORT_EVENT_TYPE.TEMPLATE_ENABLE, record)
    startTypeRuleFn(record, successCallback)
  }
  if (key === TEMPLATE_MANAGE.LIST.DELETE) {
    dhrReport.trace(REPORT_EVENT_TYPE.TEMPLATE_DELETE, record)
    deleteTypeRuleFn(record.id, successCallback && (() => successCallback(key, record)))
  }
  if (key === TEMPLATE_MANAGE.LIST.UPLOAD) {
    // 上传法大大
    dhrReport.trace(REPORT_EVENT_TYPE.TEMPLATE_VARIABLE_UPLOAD, record)
    uploadFdd([record.id]).then(() => {
      message.success('上传成功')
      successCallback?.()
    })
  }
  if (key === TEMPLATE_MANAGE.LIST.DOWNLOAD_VAR) {
    // 导出变量
    dhrReport.trace(REPORT_EVENT_TYPE.TEMPLATE_VARIABLE_EXPORT, record)
    downloadVar([record?.id])
  }
}
