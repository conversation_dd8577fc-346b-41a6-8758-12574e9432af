import React from 'react'
import { useNavigate } from 'react-router-dom'
import { But<PERSON> } from 'antd'
import { RollbackOutlined } from '@ant-design/icons'
import PermissionAction from '@/components/permissionAction'
import PermissionDropdown from '@/components/permissionDropdown'
import config from '@/config'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
// 样式
import styles from '@/pages/basicConfig/ruleConfig/detail/style.module.less'
import { TEMPLATE_MANAGE } from '@/constants/rbac-code'
import { operateTypeRule } from './typeRuleActions'

const { baseRoute } = config

const Extra = (props: any) => {
  const { id, data, refresh, extendSlot } = props
  const navigate = useNavigate()

  const getActions = (record) => [
    {
      code: TEMPLATE_MANAGE.LIST.DOWNLOAD_VAR,
      component: '导出变量',
    },
    {
      code: TEMPLATE_MANAGE.LIST.UPLOAD,
      component: '上传法大大',
    },
    record?.status === '已停用'
      ? {
          code: TEMPLATE_MANAGE.LIST.ENABLED,
          component: '启用',
        }
      : {
          code: TEMPLATE_MANAGE.LIST.DISABLED,
          danger: true,
          component: '停用',
        },
    {
      code: TEMPLATE_MANAGE.LIST.DELETE,
      component: '删除',
      danger: true,
    },
  ]

  const operateActions = (key) =>
    operateTypeRule(key, data, (actionKey?: string, templateId?: string) => {
      if (actionKey === TEMPLATE_MANAGE.LIST.DELETE) {
        navigate(-1)
      } else if (actionKey === 'redirect') {
        navigate(`${baseRoute}/contract/basic-config/contract-package?templateId=${templateId}`)
      } else {
        refresh()
      }
    })

  return (
    <div className={styles.extra}>
      {extendSlot}
      {window.__POWERED_BY_QIANKUN__ && (
        <span
          className={styles.back}
          onClick={() => {
            navigate(-1)
          }}
        >
          <RollbackOutlined className={styles.icon} />
          返回
        </span>
      )}
      <PermissionAction code={TEMPLATE_MANAGE.LIST.EDIT} permissions={data.permissions}>
        <Button
          onClick={() => {
            dhrReport.trace(REPORT_EVENT_TYPE.TEMPLATE_EDIT, data)
            navigate(`${baseRoute}/contract/basic-config/template/edit?id=${id}`)
          }}
        >
          编辑
        </Button>
      </PermissionAction>
      <PermissionDropdown
        actions={getActions(data)}
        permissions={data.permissions}
        onClick={({ key }) => operateActions(key)}
        showIcon={false}
      >
        <Button>更多</Button>
      </PermissionDropdown>
    </div>
  )
}

export default Extra
