import React, { useEffect, useState } from 'react'
import { But<PERSON>, message, Space } from 'antd'
import { SchemaTable } from '@amazebird/antd-schema-table'
import { useStore } from '@/stores'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { useNavigate, useLocation } from 'react-router-dom'
import queryString from 'query-string'
import { map } from 'lodash-es'
import useSelection from '@/hooks/useSelection'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
// 接口
import { getTemplateList, deleteTemplateApi } from '@/api/basicConfig/template'
import PermissionAction from '@/components/permissionAction'

// 路由
import config from '@/config'
import { TEMPLATE_MANAGE } from '@/constants/rbac-code'
// 变量
import { handleSort, isEmptyValue } from '@/utils/utils'
import useCreateStore from '@/stores/useCreateStore'
import { ITemplateItem } from '@/types/template'
import { operateActions, uploadFdd, exportVariable } from './common'

import { createColumns, createSearchColumns } from './Columns'

export { MoreAction } from './Columns'

const { baseRoute } = config

function Template() {
  const store = useStore((state) => state)
  const { setExtra } = store

  const action = SchemaTable.createAction()
  const form = SchemaForm.createForm()
  const [total, setTotal] = useState(0)
  const navigate = useNavigate()
  const location = useLocation()
  const permissions = useCreateStore((state: any) => state.permissions)

  // const codes = location.state?.codes || queryString.parse(location.search).codes || '' // 跳转过来的合同包编号
  const ids = location.state?.ids || queryString.parse(location.search).ids || '' // 跳转过来的合同包ids
  const { selectedRows, rowSelection, clearSelection } = useSelection<ITemplateItem>()
  const [loading, setLoading] = useState(false)

  // 请求列表
  const requestApi = ({ pagination, filter, sorter }) => {
    const { name, number, disableDate, status, classification, ...restFilter } = filter
    const { current, pageSize } = pagination
    let isFilterAllEmpty = true
    map(filter, (item) => {
      if (!isEmptyValue(item)) {
        isFilterAllEmpty = false
      }
    })
    const nameList = name
      ?.replace(/,|，/g, ',')
      ?.split(',')
      ?.map((v) => v?.trim())
      ?.join(',')
    const numberList = number
      ?.replace(/,|，/g, ',')
      ?.split(',')
      ?.map((v) => v?.trim())
      ?.join(',')
    const idsList = ids
      ?.replace(/,|，/g, ',')
      ?.split(',')
      ?.map((v) => v?.trim())

    const params: any = {
      page: current,
      size: pageSize,
      name: nameList,
      number: numberList,
      disableDateBegin: disableDate?.[0]?.startOf('day')?.valueOf(),
      disableDateEnd: disableDate?.[1]?.endOf('day')?.valueOf(),
      status,
      ...(isFilterAllEmpty ? { ids: idsList } : {}),
      ...restFilter,
    }
    if (classification) {
      if (classification.indexOf(0) !== -1) {
        params.isSupportInner = true
      }
      if (classification.indexOf(1) !== -1) {
        params.isSupportOuter = true
      }
    }
    params.sort = handleSort(sorter)
    // console.log(filter, isFilterAllEmpty, params)
    return getTemplateList(params)
      .then(({ data: resData }) => {
        setTotal(resData.count)
        return {
          success: true,
          data: resData.data,
          total: resData.count,
        }
      })
      .catch(() => ({
        data: [],
        total: 0,
      }))
  }

  // 创建按钮
  useEffect(() => {
    const CreateButton = (
      <PermissionAction code={TEMPLATE_MANAGE.CREATE} permissions={permissions}>
        <Button
          type="primary"
          onClick={() => {
            dhrReport.trace(REPORT_EVENT_TYPE.TEMPLATE_CREATE, {})
            navigate(`${baseRoute}/contract/basic-config/template/create`)
          }}
        >
          新增模板
        </Button>
      </PermissionAction>
    )
    setExtra(CreateButton)
    return () => {
      setExtra(null)
    }
  }, [permissions])

  const getPermissions = (code) => {
    if (selectedRows.length === 0) {
      // 未选择
      return [
        {
          code,
          hasPermission: false,
        },
      ]
    }
    // 已选择
    // 条件满足一个，就可以进行批量操作
    const hasPermission = selectedRows.some(
      (i: any) => i.permissions?.find((j) => j.code === code)?.hasPermission,
    )
    return [
      {
        code,
        hasPermission,
      },
    ]
  }

  return (
    <div
      style={{
        backgroundColor: 'white',
      }}
    >
      <SchemaTable
        form={form}
        action={action}
        columns={createColumns({
          toDetail: (record) => {
            dhrReport.trace(REPORT_EVENT_TYPE.TEMPLATE_VIEW, record)
            navigate(
              `${baseRoute}/contract/basic-config/template/detail?id=${record.id}&isDetail=true`,
            )
          },
          toEdit: (record) => {
            dhrReport.trace(REPORT_EVENT_TYPE.TEMPLATE_EDIT, record)
            navigate(`${baseRoute}/contract/basic-config/template/edit?id=${record.id}`)
          },
          toDelete: (record) => {
            deleteTemplateApi(record.id).then(() => {
              message.success('删除成功')
              action.refresh()
            })
          },
          moreActionClick: (key, record) =>
            operateActions(key, record, (actionName: string) => {
              if (actionName === 'redirect') {
                navigate(
                  `${baseRoute}/contract/basic-config/contract-package?templateId=${record.id}`,
                )
              } else {
                action.refresh()
              }
            }),
          exportVariable,
          uploadFdd: (record) => {
            uploadFdd(record, () => {
              clearSelection()
              action.refresh()
            })
          },
        })}
        searchColumns={createSearchColumns()}
        request={requestApi}
        toolbar={{
          title: <span>{`模板管理（共 ${total} 个）`}</span>,
          action: (
            <Space size={16}>
              <PermissionAction
                code={TEMPLATE_MANAGE.BATCH_DOWNLOAD_VAR}
                permissions={getPermissions(TEMPLATE_MANAGE.BATCH_DOWNLOAD_VAR)}
              >
                <Button
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    if (selectedRows.length > 10) {
                      message.warning('导出数据限制最多10条')
                      return
                    }
                    setLoading(true)
                    exportVariable(selectedRows)
                      .then(() => {
                        message.success(
                          selectedRows.length === 10 ? '正在导出数据，请稍候…' : '导出成功',
                        )
                      })
                      .finally(() => setLoading(false))
                  }}
                >
                  导出变量
                </Button>
              </PermissionAction>
              <PermissionAction
                code={TEMPLATE_MANAGE.BATCH_UPLOAD}
                permissions={getPermissions(TEMPLATE_MANAGE.BATCH_UPLOAD)}
                tooltip={{
                  title:
                    selectedRows.length > 0 ? '模板未更新或已被停用，无需上传法大大' : undefined,
                }}
              >
                <Button
                  type="primary"
                  onClick={() =>
                    uploadFdd(selectedRows, () => {
                      clearSelection()
                      action.refresh()
                    })
                  }
                >
                  上传法大大
                </Button>
              </PermissionAction>
            </Space>
          ),
        }}
        rowSelection={rowSelection}
        searchProps={{
          labelWidth: 110,
          initialValues: ids
            ? {}
            : {
                status: 1,
              }, // 默认状态启用中,
        }}
        toolbarOptions={{
          setting: true,
        }}
      />
    </div>
  )
}

export default Template
