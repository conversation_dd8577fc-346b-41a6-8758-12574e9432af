import React from 'react'
import { Modal, message } from 'antd'
import modal<PERSON>rapperHoc from '@/components/modalWrapperHoc'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import { SchemaTable } from '@amazebird/antd-schema-table'

import {
  enableTemplateApi,
  disableTemplateApi,
  getTemplateReference,
  uploadFdd as uploadFddApi,
  downloadVar,
} from '@/api/basicConfig/template'
import { ITemplateItem } from '@/types/template'
import { TEMPLATE_MANAGE } from '@/constants/rbac-code'

import { ActionProps } from '@/components/permissionDropdown'
import styled from './styles.module.less'
// 组件
import EnableModal from './EnableModal'

export const operates: (status: number, isDetailPage?: boolean) => ActionProps[] = (status) => {
  const isEnabled = status === 1
  const statusDropItem = isEnabled
    ? {
        component: '停用',
        code: TEMPLATE_MANAGE.LIST.DISABLED,
        danger: true,
      }
    : {
        component: '启用',
        code: TEMPLATE_MANAGE.LIST.ENABLED,
      }
  return [statusDropItem]
}

// 更多
export const operateActions = (
  key: string,
  record: { id: number; name: string; status: number; [field: string]: any },
  successCallback?: (actionName: string, data?: any) => void,
) => {
  if (key === TEMPLATE_MANAGE.LIST.DELETE) {
    dhrReport.trace(REPORT_EVENT_TYPE.TEMPLATE_DELETE, record)
  }
  // 启用
  const execute = (content?: any) => {
    // 正常逻辑
    const text = record.status ? '停用' : '启用'
    const executeFn = record.status ? disableTemplateApi : enableTemplateApi
    Modal.confirm({
      title: text,
      width: content ? 650 : undefined,
      content:
        content ||
        (record.status
          ? '停用后将不能被引用，确定要停用模板吗？'
          : `确定要启用【${record.name}】吗？`),
      okText: '确定',
      cancelText: '取消',
      onOk() {
        executeFn(record.id).then(() => {
          message.success(`${text}成功`)
          successCallback?.(key)
        })
      },
    })
  }
  dhrReport.trace(
    record.status ? REPORT_EVENT_TYPE.TEMPLATE_DEACTIVATE : REPORT_EVENT_TYPE.TEMPLATE_ENABLE,
    record,
  )
  if (record.status) {
    // 调用接口写停用逻辑
    getTemplateReference(record.id).then(({ data: { data } }) => {
      // 是否存在模板
      if (data.length > 0) {
        modalWrapperHoc(EnableModal)({
          data,
          record,
          onOk: () => {
            const codes = data.map((item) => item.contractPackageNumber).join(',')
            successCallback?.('redirect', codes)
          },
        })
      } else if (record.relationList?.length) {
        const content = (
          <div>
            <div
              style={{
                marginBottom: 8,
              }}
            >
              {`当前有 ${record.relationList?.length} 个业务项正在使用该模板，停用后该业务项发起签署模板将会缺失！确定停用后，请及时新增该业务项模板。`}
            </div>
            <div>{`关联业务项：${record.relationList
              ?.map((i) => {
                if (i.applicationName && i.businessItemName) {
                  return `${i.applicationName}-${i.businessItemName}`
                }
                if (!i.applicationName && i.businessItemName) {
                  return i.businessItemName
                }
                if (i.applicationName && !i.businessItemName) {
                  return i.applicationName
                }
                return '--'
              })
              .join('、')}`}</div>
          </div>
        )
        execute(content)
      } else {
        execute()
      }
    })
  } else {
    execute()
  }
}

// 公用的判断
const getDisabledAndNames = (rowList: ITemplateItem[]) => {
  // 若勾选单条数据（数据状态=启用中，且上传法大大状态待上传），按钮显示为绿色可点击。
  // 不符合操作前提条件，按钮置灰不可操作，鼠标悬停按钮提示【模板未更新或已被停用，无需上传法大大】
  const names: string[] = []
  const rightList: ITemplateItem[] = []
  rowList.forEach((item) => {
    if (!(item.status === 1 && item.fddStatus === 0)) {
      names.push(item.name)
    } else {
      rightList.push(item)
    }
  })
  return {
    names,
    rightList,
    disabled: names.length === rowList.length,
  }
}

const columns = [
  {
    dataIndex: 'name',
    title: '模板名称',
    cell: 'Text',
  },
  {
    dataIndex: 'relationList',
    title: '关联业务项',
    cell: {
      type: 'Text',
      render: ({ record }) => {
        const arr: string[] = record.relationList?.map((item) => {
          if (item.applicationName && item.businessItemName) {
            return `${item.applicationName}-${item.businessItemName}`
          }
          if (!item.applicationName && item.businessItemName) {
            return item.businessItemName
          }
          if (item.applicationName && !item.businessItemName) {
            return item.applicationName
          }
          return '--'
        })
        return arr.length > 0 ? arr.join('、') : '--'
      },
    },
  },
]

// 上传法大大
export const uploadFdd = (
  record: ITemplateItem | ITemplateItem[],
  successCallback?: () => void,
) => {
  let modalInstance: any = null
  const onOk = (resultList: ITemplateItem | ITemplateItem[]) => {
    modalInstance.update({
      okButtonProps: {
        loading: true,
      },
    })
    uploadFddApi(!Array.isArray(resultList) ? [resultList.id] : resultList.map((item) => item.id))
      .then(() => {
        message.success('上传成功')
        successCallback?.()
      })
      .finally(() => {
        modalInstance?.destroy()
      })
  }
  const showModal = (relation) => {
    modalInstance = Modal.confirm({
      title: '提示',
      content: `上传法大大后，该模板关联业务项${relation}将会以最新模板发起签署。确定要上传吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        onOk(record)
        return Promise.reject()
      },
    })
  }
  const isSingle = !Array.isArray(record)
  if (isSingle) {
    // 单个
    dhrReport.trace(REPORT_EVENT_TYPE.TEMPLATE_VARIABLE_UPLOAD, record)
    const relation = record.relationList
      ?.map((item) => {
        if (item.applicationName && item.businessItemName) {
          return `【${item.applicationName}-${item.businessItemName}】`
        }
        if (!item.applicationName && item.businessItemName) {
          return `【${item.businessItemName}】`
        }
        if (item.applicationName && !item.businessItemName) {
          return `【${item.applicationName}】`
        }
        return '--'
      })
      .join(' ')
    showModal(relation)
  } else {
    dhrReport.trace(REPORT_EVENT_TYPE.TEMPLATE_VARIABLE_BATCH_UPLOAD, record)
    // 正常逻辑
    if (record.length === 1) {
      const relation = record[0].relationList
        ?.map((item) => {
          if (item.applicationName && item.businessItemName) {
            return `【${item.applicationName}-${item.businessItemName}】`
          }
          if (!item.applicationName && item.businessItemName) {
            return `【${item.businessItemName}】`
          }
          if (item.applicationName && !item.businessItemName) {
            return `【${item.applicationName}】`
          }
          return '--'
        })
        .join(' ')
      showModal(relation)
    } else if (record.length > 1) {
      if (record.length > 10) {
        message.warning('上传数据限制最多10条')
        return
      }
      const { names, rightList } = getDisabledAndNames(record)
      if (names.length > 0) {
        const title = `所选模板共${record.length}个，其中${names.length}个模板未更新或已被停用，无需上传法大大。确定后将忽略此类数据继续执行，所选模板运用业务将会以最新模板发起签署。确定要上传吗？`
        const Element = (
          <div>
            <p>{title}</p>
            <p>
              忽略模板：
              {names.join('、')}
            </p>
            <p>上传模板如下：</p>
            <SchemaTable
              stripe={false}
              dataSource={rightList}
              columns={columns}
              pagination={false}
              className={styled.tableConfig}
            />
          </div>
        )
        modalInstance = Modal.confirm({
          title: '提示',
          width: 800,
          content: Element,
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            onOk(rightList)
            return Promise.reject()
          },
        })
      } else {
        const title = `上传法大大后，选中的 ${record.length} 个模板所关联业务项将会以最新模板发起签署。确定要上传吗？`
        const Element = (
          <div>
            <p>{title}</p>
            <p>上传模板如下：</p>
            <SchemaTable
              stripe={false}
              dataSource={rightList}
              columns={columns}
              pagination={false}
              className={styled.tableConfig}
            />
          </div>
        )
        modalInstance = Modal.confirm({
          width: 800,
          title: '提示',
          content: Element,
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            onOk(rightList)
            return Promise.reject()
          },
        })
      }
    }
  }
}

// 导出变量
export const exportVariable = (record: ITemplateItem | ITemplateItem[]) => {
  if (Array.isArray(record)) {
    dhrReport.trace(REPORT_EVENT_TYPE.TEMPLATE_VARIABLE_BATCH_EXPORT, record)
    return downloadVar(record.map((item) => item.id))
  }
  // 单个
  dhrReport.trace(REPORT_EVENT_TYPE.TEMPLATE_VARIABLE_EXPORT, record)
  return downloadVar([record.id])
}
