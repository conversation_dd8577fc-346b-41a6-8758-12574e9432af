import React from 'react'
import { Modal } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'

const EndModal = (props: any) => {
  const { visible, close, record, data, onOk, ...restProp } = props
  const isAll = record.relationList.length > 0
  return (
    <Modal
      title={
        <div>
          <ExclamationCircleOutlined
            style={{
              color: '#ffa229',
              marginRight: 8,
              fontSize: '22px',
            }}
          />
          停用
        </div>
      }
      visible={visible}
      onCancel={close}
      width={650}
      cancelText="关闭"
      okText="前往合同包"
      {...restProp}
      onOk={() => {
        close?.()
        onOk?.()
      }}
    >
      {isAll ? (
        <div>
          <div>
            {`当前有 ${data.length} 个合同包、${record.relationList.length}个业务项正在使用该模板，不能直接停用。`}
          </div>
          <div>1、关联合同包：在合同包将该模板移除后，可正常停用！关联数据如下：</div>
          <div
            style={{
              marginBottom: 8,
            }}
          >
            {data.map((i) => i.contractPackageName).join('、')}
          </div>
          <div>
            2、关联业务项：停用后该业务项发起签署模板将会缺失！确定停用后，请及时新增该业务项模板。关联数据如下：
          </div>
          <div>
            {record.relationList
              .map((i) => `${i.applicationName}-${i.businessItemName}`)
              .join('、')}
          </div>
        </div>
      ) : (
        <div>
          <div
            style={{
              marginBottom: 8,
            }}
          >
            {`当前有${data.length}个合同包正在使用该模板，不能直接停用。在合同包将该模板移除后，可正常停用！`}
          </div>
          <div>{`关联的合同包：${data.map((i) => i.contractPackageName).join('、')}`}</div>
        </div>
      )}
    </Modal>
  )
}

export default EndModal
