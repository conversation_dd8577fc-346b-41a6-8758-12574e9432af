/* eslint-disable max-lines-per-function */
import React, { useMemo, useRef } from 'react'
import { TreeSelect as Dict } from '@galaxy/dict'
import { useDebounceFn } from 'ahooks'
import { Spin } from 'antd'
import type { SchemaType } from '@amazebird/schema-form'
import {
  SchemaForm,
  Item,
  Observer,
  OBSERVER_SOURCE,
  FormItemGrid,
} from '@amazebird/antd-schema-form'
import classNames from 'classnames'
import { STAFF_SIGN_CODE, PROOF_TEMPLATE_CODE } from '@/constants/template'
import { TEMPLATE_TYPE } from '@/constants/sign-manage'
import { isNameExist, isNumberExist } from '@/api/basicConfig/template'
import { cloneDeep } from 'lodash-es'
import BusinessSelect from './components/BusinessSelect'

import styles from './style.module.less'

const SEAL_CODE_RULES = [
  {
    required: true,
    message: '请选择关联印章',
  },
]

const CODE_RULES = [
  { required: true },
  {
    max: 10,
  }
]

interface IProps {
  isEdit: boolean
  form: any
  initValues?: any
  extraClassName?: string
  onSealCodeChange: () => void
}

const BasicInfo = ({ isEdit, form, extraClassName, initValues, onSealCodeChange }: IProps) => {
  const initNameRef = useRef(null)

  initNameRef.current = initValues?.name || ''

  const isNameExistValidator = async (_, name) => {
    if (!name) {
      return Promise.reject(new Error('请输入模板名称'))
    }
    if (isEdit && initNameRef.current === name) {
      return Promise.resolve()
    }
    // 判断格式
    if (
      /^[\u4E00-\u9FA5A-Za-z0-9（）()\s【】「」《》\-_、.&—]+$/.test(name) &&
      name?.trim().length !== 0
    ) {
      const { data: result } = await isNameExist({
        name,
      })
      if (result.data) {
        return Promise.reject(new Error('合同模板名称重复，请重新命名'))
      }
      return Promise.resolve()
    }
    return Promise.reject(new Error('模板名称格式有误'))
  }

  const repeatValidator = async (_, value) => {
    const list = cloneDeep(value) as Array<any>
    const exist: string[] = []

    const notPass = list.some((item) => {
      const current = `${item.applicationCode}_${item.businessItemCode}`
      if (exist.includes(current)) {
        return true
      }

      if (item.businessItemCode) {
        exist.push(current)
      }
      return false
    })
    if (notPass) {
      return Promise.reject(new Error('关联业务项存在重复选项'))
    }
    return Promise.resolve()
  }

  const businessRequiredValidator = (_, value) => {
    const list = cloneDeep(value) as Array<any>
    const isPass = list.every((item) => !!item.businessItemCode && !!item.applicationCode)
    if (isPass) {
      return Promise.resolve()
    }
    return Promise.reject(new Error('请选择关联业务项'))
  }

  const isNumberExistValidator = async (_, number) => {
    if (isEdit) {
      return Promise.resolve()
    }
    // 判断格式
    if (/^[a-zA-Z0-9_]+$/.test(number) && number?.trim().length !== 0) {
      const { data: result } = await isNumberExist({
        number,
      })
      if (result.data) {
        return Promise.reject(new Error('模板编号重复，请重新输入'))
      }
      return Promise.resolve()
    }
    return Promise.reject(new Error('模板编号格式有误'))
  }

  const { run: isNameExistValidatorDebounce } = useDebounceFn(isNameExistValidator, {
    wait: 500,
    leading: true,
  })

  const { run: isNumberExistValidatorDebounce } = useDebounceFn(isNumberExistValidator, {
    wait: 500,
    leading: true,
  })

  const isLoading = useMemo(
    () => isEdit && Object.keys(initValues).length === 0,
    [isEdit, initValues],
  )

  const schema: SchemaType = {
    name: {
      label: '模板名称',
      component: 'Input',
      rules: [
        {
          validator: isNameExistValidatorDebounce,
        },
        {
          max: 50,
        },
      ],
    },
    number: {
      label: '模板编号',
      component: 'Input',
      required: true,
      disabled: isEdit,
      max: 20,
      rules: [
        { pattern: /^[a-zA-Z0-9_]+$/, message: '仅支持英文、数字、下划线，建议以英文开头' },
        {
          validator: isNumberExistValidatorDebounce,
        },
      ],
    },
    category: {
      label: '分类',
      component: 'Checkbox',
      required: true,
      options: [
        {
          value: 'isSupportInner',
          label: '朴朴员工模板',
        },
        {
          value: 'isSupportOuter',
          label: '自营第三方模板',
        },
      ],
    },
    typeCode: {
      label: '模板类型',
      component: Dict,
      props: {
        showSearch: true,
        allowClear: true,
        code: 'TEMPLATE_TYPE',
        placeholder: '请选择模板类型',
      },
      required: [true, '请选择模板类型'],
    },
    signMethodCode: {
      label: '签署方式',
      component: Dict,
      observer: Observer({
        source: [OBSERVER_SOURCE.UPDATE],
        watch: 'typeCode',
        action: (value) => {
          const filter = (option) => {
            if (value !== PROOF_TEMPLATE_CODE) {
              return true
            }
            return option.key === 'CM_ST_002'
          }
          return {
            value: undefined,
            component: (props) => <Dict {...props} filter={filter} />,
          }
        },
      }),
      props: {
        showSearch: true,
        allowClear: true,
        code: 'SIGN_TYPE',
        placeholder: '请选择签署方式',
      },
      required: [true, '请选择签署方式'],
    },
    sealCode: {
      label: '关联印章',
      component: Dict,
      rules: SEAL_CODE_RULES,
      props: {
        showSearch: true,
        allowClear: true,
        code: 'RELATED_SEAL',
        placeholder: '请选择关联印章',
      },
      observer: Observer({
        source: [OBSERVER_SOURCE.UPDATE],
        watch: 'signMethodCode',
        action: (value) => ({
          value: undefined,
          disabled: value === STAFF_SIGN_CODE,
          rules: value === STAFF_SIGN_CODE ? [] : SEAL_CODE_RULES,
          props: {
            allowClear: true,
            code: 'RELATED_SEAL',
            placeholder:
              value === STAFF_SIGN_CODE ? '员工单方签署不需要选择该项' : '请选择关联印章',
          },
        }),
      }),
    },
    code: {
      label: '对应合同编码',
      component: 'Input',
      rules: CODE_RULES,
      observer: Observer({
        source: [OBSERVER_SOURCE.UPDATE],
        watch: 'typeCode',
        action: (value) => ({
          rules: value !== PROOF_TEMPLATE_CODE ? CODE_RULES : [],
          disabled: value === PROOF_TEMPLATE_CODE,
          placeholder: value === PROOF_TEMPLATE_CODE ? '证明不需要输入该项' : '请输入对应合同编码',
          ...(value === PROOF_TEMPLATE_CODE
            ? {
              value: value === PROOF_TEMPLATE_CODE && undefined,
            }
            : {}),
        }),
      }),
    },
    relationList: {
      label: '关联业务项',
      component: BusinessSelect,
      observer: Observer({
        watch: 'typeCode',
        action: (value) => {
          // 如果模板是 解除协议 或者 作废协议
          const isDisabled = [TEMPLATE_TYPE.REMOVE, TEMPLATE_TYPE.CANCEL].includes(value)
          return {
            required: !isDisabled,
            disabled: isDisabled,
            rules: isDisabled
              ? []
              : [
                { validator: repeatValidator },
                {
                  required: true,
                  validator: businessRequiredValidator,
                  validateTrigger: 'onBlur',
                },
              ],
          }
        },
      }),
    },
    remark: {
      label: '模板说明',
      component: 'Input.TextArea',
      max: 200,
    },
  }

  const onValuesChange = ({ sealCode, signMethodCode }) => {
    if (sealCode || (!sealCode && signMethodCode === STAFF_SIGN_CODE)) {
      onSealCodeChange()
    }
  }

  return (
    <div
      className={classNames(styles.schemaWrapper, extraClassName, isLoading ? styles.loading : '')}
    >
      {isLoading ? (
        <Spin />
      ) : (
        <SchemaForm
          initialValues={initValues}
          schema={schema}
          form={form}
          className={styles.schema}
          onValuesChange={(values) => onValuesChange(values)}
          labelCol={{
            span: 8,
          }}
          wrapperCol={{
            span: 16,
          }}
        >
          <FormItemGrid colCount={2}>
            <Item
              field="name"
              tooltip="仅支持中文、英文、数字，以及部分字符（）()【】「」《》-_、.&—"
              required
            />
            <Item
              field="number"
              tooltip="仅支持英文、数字、下划线，建议以英文开头。不支持二次修改"
              required
            />
            <Item field="category" />
            <Item field="typeCode" />
            <Item field="signMethodCode" />
            <Item field="sealCode" />
          </FormItemGrid>
          <Item
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 8 }}
            field="code"
            tooltip="该字段主要用于合同编号规则中的二级编码生成。"
          />
          <Item labelCol={{ span: 4 }} wrapperCol={{ span: 8 }} field="relationList" />
          <Item labelCol={{ span: 4 }} field="remark" />
        </SchemaForm>
      )}
    </div>
  )
}

export default BasicInfo
