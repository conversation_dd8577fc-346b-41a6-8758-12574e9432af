/**
 * 转换成 Base64
 */
export const toBase64 = (file) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = (error) => reject(error)
  })

/**
 * Base64 转 blob
 */
export const dataUrlToFile = async (dataUrl) => {
  const res = await fetch(dataUrl)
  const blob = await res.blob()
  return blob
}
