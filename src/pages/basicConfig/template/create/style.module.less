.createWrapper {
  overflow: hidden;

  .templateCreate {
    width: 100%;
  }


  .contentWrapper {
    overflow: hidden;
    margin: 20px 20px 40px 20px;


    .uploadWrapper {
      position: relative;
      display: flex;
      float: right;
      width: calc(100% - 40px);
      padding: 20px;
      margin: 20px 20px 0px 0px;
      align-items: center;
      justify-content: center;
      background-color: white;

      .choose {
        margin-top: 30px;
      }

      .desc {
        font-size: 14;
      }

      .tip {
        font-size: 12px;
        margin-top: 10px;
        color: #898989;
      }

      :global {
        .@{ant-prefix}-upload.@{ant-prefix}-upload-drag {
          width: 595px;
          height: 400px;
          border-radius: 10px;
        }
      }
    }
  }

}

.step {
  margin: 20px 20px 0px 20px;
  padding: 18px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;

  .steps {
    width: 40%;
  }
}

.schemaWrapper {
  background-color: white;
  margin: 20px;
  min-height: 35vh;

  .schema {
    padding: 40px 20px;
    width: 75vw;
    margin: 0 auto;
  }

  &.loading {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.footer {
  height: 50px;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: end;
  background-color: white;
  z-index: 99;

  .btn {
    margin-left: 10px;
    width: fit-content;

    :global {
      .@{ant-prefix}-btn-compact-last-item {
        background: white;
        color: black;
        border-color: #e7e7e7;
      }
    }

    &:last-of-type {
      margin-right: 10px;
    }
  }
}

.switch {
  z-index: -1;
  opacity: 0;
  position: absolute;
  height: 0;
  overflow: hidden;
}