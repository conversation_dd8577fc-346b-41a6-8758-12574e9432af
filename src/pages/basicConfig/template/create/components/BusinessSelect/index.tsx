import React, { useEffect, useRef, useState } from 'react'
import { Input } from 'antd'
import { cloneDeep } from 'lodash-es'
import Select from './Select'

interface SeleceProps {
  applicationCode: string | null
  applicationName: string | null
  businessItemCode: string | null
}

interface IProps {
  value?: any
  disabled?: boolean
  onChange?: (value: Record<number, SeleceProps>) => void
}

const defaultValue = [
  {
    applicationCode: null,
    applicationName: null,
    businessItemCode: null,
    businessItemName: null,
  },
]

const BusinessSelect = ({ value, disabled, onChange }: IProps) => {
  const [result, setResult] = useState<SeleceProps[]>(
    Array.isArray(value) && value.length > 0 ? value : defaultValue,
  )
  const currentSelect = useRef(null)

  const onAdd = async (index) => {
    await (currentSelect.current as any)?.validate()

    const res = result
      .slice(0, index + 1)
      .concat(defaultValue)
      .concat(...result.slice(index + 1))
    setResult(res)
  }

  const onDelete = (index) => {
    const res = result.slice(0, index).concat(...result.slice(index + 1))
    setResult(res)
  }

  const onValuesChange = (v, index) => {
    const res = cloneDeep(result)
    res[index] = v
    setResult(res)
  }

  useEffect(() => {
    onChange && onChange(result)
  }, [result])

  return disabled ? (
    <Input disabled placeholder="不需要选择该项" />
  ) : (
    result.map((v, index) => (
      <Select
        ref={currentSelect}
        value={v}
        index={index}
        // eslint-disable-next-line react/no-array-index-key
        key={`${v.applicationCode}_${index}`}
        canAdd={result.length < 10}
        canDelete={result.length !== 1}
        onAdd={onAdd}
        onDelete={onDelete}
        onValuesChange={onValuesChange}
      />
    ))
  )
}

export default BusinessSelect
