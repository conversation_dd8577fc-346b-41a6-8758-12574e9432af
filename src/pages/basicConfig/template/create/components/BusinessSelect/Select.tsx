import React, { useRef, forwardRef, useImperativeHandle, useEffect } from 'react'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons'
import { TreeSelect as Dict } from '@galaxy/dict'
import type { SchemaType } from '@amazebird/schema-form'
import { SchemaForm, Item, FormItemGrid } from '@amazebird/antd-schema-form'
import { isNil } from 'lodash-es'
import { getAppListApi } from '@/api/template'
import styles from '../style.module.less'

interface IProps {
  index: number
  value: any
  canAdd?: boolean
  canDelete?: boolean
  onAdd: (value: number) => void
  onDelete: (value: number) => void
  onValuesChange?: (value, index) => void
}

interface SeleceHandler {
  validate: () => Promise<void>
}

const Select = forwardRef<SeleceHandler, IProps>(
  (
    { canDelete = true, canAdd = true, index, onAdd, onDelete, onValuesChange, value }: IProps,
    ref,
  ) => {
    const form = SchemaForm.createForm()
    const applicationMap = useRef(null as any)

    // 向外暴露的方法
    useImperativeHandle(ref, () => ({
      validate: async () => form?.validateFields(),
    }))

    const getAppList = async () => {
      const { data: result } = await getAppListApi()
      const newApplicationMap = {}

      const appList = result.data.map((i) => {
        newApplicationMap[i.applicationCode] = i.applicationName

        return {
          value: i.applicationCode,
          label: i.applicationName,
        }
      })

      applicationMap.current = newApplicationMap
      return appList
    }

    const schema: SchemaType = {
      application: {
        label: '',
        component: 'Select',
        props: {
          placeholder: '请选择应用',
          labelInValue: true,
        },
        options: getAppList,
      },
      businessItem: {
        label: '',
        component: Dict,
        required: [true, '请选择业务项'],
        props: {
          showSearch: true,
          allowClear: true,
          code: 'BUSINESS_ITEM',
          placeholder: '请选择业务项',
          labelInValue: true,
        },
      },
    }

    useEffect(() => {
      form?.setFieldsValue({
        application: value.applicationCode
          ? { value: value.applicationCode, label: value.applicationName }
          : null,
        businessItem: value.businessItemCode
          ? { value: value.businessItemCode, label: value.businessItemName }
          : null,
      })
    }, [value])

    return (
      <div className={styles.selectWrapper}>
        <SchemaForm
          schema={schema}
          form={form}
          onValuesChange={(_, values) => {
            const useValues = { ...values } as any
            const { application, businessItem } = values || {}

            // // 后端传参需要添加应用名称
            if (!isNil(application)) {
              useValues.applicationName = application.label
              useValues.applicationCode = application.value
            }

            // // 后端传参需要添加业务项名称
            if (!isNil(businessItem)) {
              useValues.businessItemName = businessItem.label
              useValues.businessItemCode = businessItem.value
            }

            onValuesChange && onValuesChange(useValues, index)
          }}
          labelCol={{
            span: 0,
          }}
          wrapperCol={{
            span: 12,
          }}
        >
          <FormItemGrid colCount={2}>
            <Item field="application" />
            <Item field="businessItem" />
          </FormItemGrid>
        </SchemaForm>
        <div className={styles.action}>
          {canAdd && <PlusOutlined className={styles.addIcon} onClick={() => onAdd(index)} />}
          {canDelete && <DeleteOutlined onClick={() => onDelete(index)} />}
        </div>
      </div>
    )
  },
)

export default Select
