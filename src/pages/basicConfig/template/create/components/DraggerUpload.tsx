import React from 'react'
import { Upload, But<PERSON>, message } from 'antd'
import type { UploadProps } from 'antd'
import { CloudUploadOutlined } from '@ant-design/icons'
import { toBase64, dataUrlToFile } from '../utils'

import styles from '../style.module.less'

interface FileProps {
  name: string
  data: Blob
}

const { Dragger } = Upload

interface IProps {
  onFileUpload: (value: FileProps) => void
}

const DraggerUpload = ({ onFileUpload }: IProps) => {
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: true,
    showUploadList: false,
    beforeUpload: async (file) => {
      const name = file.name
      const format = name.slice(file.name.lastIndexOf('.') + 1)

      if (format !== 'pdf') {
        message.warn('请上传 pdf 格式文件')
        return false
      }

      if (file.size > 2 * 1024 * 1024) {
        message.warn('文件大小不超过 2 M')
        return false
      }
      const f = await toBase64(file)
      const f1 = await dataUrlToFile(f)

      onFileUpload &&
        onFileUpload({
          name,
          data: f1,
        })
      return true
    },
    customRequest: () => {
      // 不在这里做请求 但需要定义这个方法防止 upload 组件进行请求
    },
  }
  return (
    <div className={styles.uploadWrapper}>
      <Dragger {...uploadProps}>
        <p className="ant-upload-drag-icon">
          <CloudUploadOutlined />
        </p>
        <p className={styles.desc}>请选择或者拖拽文件到此处</p>
        <p className={styles.tip}>仅支持扩展名：PDF, 不超过2M</p>
        <Button className={styles.choose}>选择文件</Button>
      </Dragger>
    </div>
  )
}

export default DraggerUpload
