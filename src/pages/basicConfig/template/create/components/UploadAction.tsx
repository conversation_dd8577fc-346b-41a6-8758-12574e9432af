import React, { useMemo, useRef } from 'react'
import { UploadOutlined } from '@ant-design/icons'
import { Upload, UploadProps, message, Modal, Tooltip } from 'antd'
import { toBase64, dataUrlToFile } from '../utils'

interface FileProps {
  name: string
  data: Blob
}

interface IProps {
  onFileUpload: (file: FileProps) => void
}

const UploadAction = ({ onFileUpload }: IProps) => {
  const uploadRef = useRef(null)

  const props = useMemo<UploadProps>(
    () => ({
      name: 'file',
      multiple: true,
      showUploadList: false,
      beforeUpload: async (file) => {
        const name = file.name
        const format = name.slice(file.name.lastIndexOf('.') + 1)

        if (format !== 'pdf') {
          message.warn('请上传 pdf 格式文件')
          return false
        }

        if (file.size > 2 * 1024 * 1024) {
          message.warn('文件大小不超过 2 M')
          return false
        }
        const f = await toBase64(file)
        const f1 = await dataUrlToFile(f)

        let upload = true
        await Modal.confirm({
          title: '提示',
          content: '重新上传文件将会覆盖之前已编辑的模板，确定要继续操作吗？',
          okText: '确定',
          cancelText: '取消',
          onOk() {
            upload = true
            upload &&
              onFileUpload &&
              onFileUpload({
                name,
                data: f1,
              })
          },
          onCancel() {
            upload = false
          },
        })
        return upload
      },
      customRequest: () => {
        // 不在这里做请求 但需要定义这个方法防止 upload 组件进行请求
      },
    }),
    [],
  )

  return (
    <Upload {...props}>
      <Tooltip title="上传文件">
        <UploadOutlined
          ref={uploadRef}
          style={{
            cursor: 'pointer',
            marginRight: 10,
            fontSize: '20px',
          }}
        />
      </Tooltip>
    </Upload>
  )
}

export default UploadAction
