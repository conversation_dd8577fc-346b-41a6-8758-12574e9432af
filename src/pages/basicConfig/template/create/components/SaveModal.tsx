import React from 'react'
import { Modal } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'

const SaveModal = (props: any) => {
  const { visible, close, record, onOk, ...restProp } = props

  const TitleElement = (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <ExclamationCircleOutlined
        style={{
          color: '#ffa229',
          marginRight: 16,
          fontSize: '22px',
        }}
      />
      提示
    </div>
  )

  return (
    <Modal
      title={TitleElement}
      open={visible}
      onCancel={close}
      width={650}
      cancelText="关闭"
      okText="前往操作"
      {...restProp}
      onOk={() => {
        close?.()
        onOk?.()
      }}
    >
      <div
        style={{
          marginBottom: 8,
        }}
      >
        {`当前有 ${record.packageNum} 个合同包正在使用该模板，合同批量签署只能选择`}
        <span style={{ color: '#F59A23' }}>印章类型一致、关键字定位且关键字一致</span>
        的模板。请前往修改合同包所选模板，以免影响合同发起。
      </div>
      <div>{`关联的合同包：${record.relatedPackageNames.join('、')}`}</div>
    </Modal>
  )
}

export default SaveModal
