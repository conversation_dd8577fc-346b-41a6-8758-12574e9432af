/* eslint-disable max-lines-per-function */
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Button, message, Steps, Modal, Dropdown } from 'antd'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { toCamelCase } from '@galaxy/utils'
import { filter } from 'lodash-es'
import classNames from 'classnames'
import config from '@/config'
import {
  editTemplateApi,
  createTemplateApi,
  getTemplateDetailApi,
} from '@/api/basicConfig/template'
import { TEMPLATE_TYPE } from '@/constants/sign-manage'
import modalWrapperHoc from '@/components/modalWrapperHoc'
import PdfEdit, { FileProps, FontHandler, STAMP_VAR_TYPE } from '@/components/pdfEditor'
import { uploadServiceApi, getFileDataServiceApi } from '@/service/fileService'
import BasicInfo from './BasicInfo'
import DraggerUpload from './components/DraggerUpload'
import UploadAction from './components/UploadAction'
import SaveModal from './components/SaveModal'

import styles from './style.module.less'

const { baseRoute } = config

interface BasicInfoParams {
  name: string
  remark: string
  typeCode: string
  sealCode: string
  signMethodCode: string
  sealLocateModeCode: string
  relationList: any[]
}

const steps = [
  {
    title: '设置基础信息',
    content: '设置基础信息',
  },
  {
    title: '维护模板内容',
    content: '维护模板内容',
  },
]

const menuItems = [
  {
    key: 'save',
    label: '保存并上传法大大',
  },
]

// 印章固定位置定位
const FIXED_LOCATE = 'CM_LM_001'

// 企业-员工双方签署
const BOTH_SIGN = 'CM_ST_001'

const TemplateCreate = function () {
  const [search] = useSearchParams()
  const [loading, setLoading] = useState(false)
  const [current, setCurrent] = useState(0)
  const [basicInfo, setBaseInfo] = useState<BasicInfoParams>({} as BasicInfoParams)
  const [initBasicInfo, setInitBasicInfo] = useState<BasicInfoParams>({} as BasicInfoParams)
  const [uploadFile, setUploadFile] = useState<FileProps>(null as any)
  const [initBoxes, setInitBoxes] = useState<any>([])
  const [initSearch, setInitSearch] = useState<any>([])
  const [initLocateMode, setInitLocateMode] = useState<string>('CM_LM_002')
  const [initFontParams, setInitFontParams] = useState<{ fontType: string; fontSize: string }>({
    // 默认字号为 12, 字号为 微软雅黑
    fontType: '4',
    fontSize: 12,
  } as any)
  const [total, setTotal] = useState(0)
  const templateId = search.get('id')
  const form = SchemaForm.createForm()
  const keyForm = SchemaForm.createForm()
  const fontRef = useRef<FontHandler>(null)
  const navigate = useNavigate()

  const pdfRef = useRef(null)
  const pdfBoxes = useRef<Array<any>>([])
  const locateMode = useRef<string>('')
  const keyWordSearch = useRef<[string?, string?, string?]>(['', '', ''])
  const fontParams = useRef<{ fontType: string; fontSize: string }>({} as any)

  // 模板变量是否设置校验
  const validateVars = (next) => {
    const hasVars = pdfBoxes.current.filter((b) => !b.type).length > 0
    if (!hasVars) {
      Modal.confirm({
        title: '提示',
        content: '您还未设置模板变量，确定要保存吗？',
        onOk() {
          next()
        },
        onCancel() {
          setLoading(false)
        },
        okText: '确定',
        cancelText: '取消',
      })
    } else {
      next()
    }
  }

  // 固定印章是否定位校验
  const validateStamp = () => pdfBoxes.current.filter((b) => !!b.type).length > 0

  const toPositionSetting = (key, mode?) => {
    ;(pdfRef.current as any)?.updateTabsState({
      mode,
      tabsKey: key,
    })
  }

  const handleBeforeSubmit = (params) => {
    // 当板签署方式为“企业-员工双方签署”，印章定位方式为“固定位置定位”，需要校验「企业印章」和「员工印章」两者必须都存在。
    const { signMethodCode, sealLocateModeCode, templateContent } = params
    if (signMethodCode === BOTH_SIGN && sealLocateModeCode === FIXED_LOCATE) {
      const hasEnterprise = templateContent?.sealList?.some(
        (seal) => seal.type === STAMP_VAR_TYPE.SEAL,
      )
      const hasEmployee = templateContent?.sealList?.some(
        (seal) => seal.type === STAMP_VAR_TYPE.SIGNATURE,
      )
      if (!hasEnterprise || !hasEmployee) {
        if (!hasEnterprise) {
          message.warn('未设置企业印章，无法保存。')
        }
        if (!hasEmployee) {
          message.warn('未设置员工印章，无法保存。')
        }
        return false
      }
    }
    return true
  }

  /**
   * 保存合同模板
   */
  const handleSubmit = async (isSave?) => {
    const isEdit = search.get('id')
    // 校验文件是否上传
    if (!uploadFile) {
      message.warn('请上传文件')
      return
    }

    try {
      // 校验字体是否设置
      await fontRef.current?.fontValidate()
    } catch (error) {
      message.warn('请设置字体样式')
      toPositionSetting('排版样式')
      return
    }

    // 是否为固定定位
    const isFixedLocate = locateMode.current === FIXED_LOCATE
    if (isFixedLocate) {
      if (!validateStamp()) {
        toPositionSetting('印章定位', FIXED_LOCATE)
        message.warn('请设置印章位置，以免影响合同发起')
        return
      }
    } else {
      try {
        const keywords = keyForm?.getFieldsValue()
        const keywordsState = keyForm?.getFieldsState()
        const keywordKeys = filter(
          Object.keys(keywords),
          (key) => keywordsState[key].required && keywordsState[key].required.required,
        )
        const hasSetting = keywordKeys.length > 0 && keywordKeys.every((key) => !!keywords[key])
        /**
         * 没切到关键字面板时，未初始化表单，因此 keywords 永远是一个空对象
         * 1. 如果是编辑，没切到关键字面板说明没改动，不需要进行相关校验，会直接跳过该 try catch
         * 2. 如果是新增，没切到关键字就说明没有进行关键字填写，hasSetting永远为 false 且 isEdit 为 false，会进入校验
         */
        if (!hasSetting && !isEdit) {
          toPositionSetting('印章定位', 'CM_LM_002')
          message.warn('请设置正确的关键字')
          return
        }
        await keyForm?.validateFields()
      } catch (error) {
        toPositionSetting('印章定位', 'CM_LM_002')
        message.warn('请设置正确的关键字')
        return
      }
    }
    setLoading(true)
    validateVars(async () => {
      try {
        let fileId = ''
        if (uploadFile.id) {
          fileId = uploadFile.id
        } else {
          const file = new File([uploadFile.data], uploadFile.name || '', {
            type: (uploadFile.data as any).name,
          })
          fileId = await uploadServiceApi(file)
        }
        const params = {
          fileId,
          fileName: uploadFile.name,
          ...fontParams.current,
          ...basicInfo,
          // 如果是解除作废协议的话，不传 relationList
          relationList: [TEMPLATE_TYPE.CANCEL, TEMPLATE_TYPE.REMOVE].includes(
            basicInfo.typeCode as any,
          )
            ? []
            : (basicInfo.relationList || []).filter((v) => v.applicationCode && v.businessItemCode),
          uploadToFdd: !!isSave,
          sealLocateModeCode: locateMode.current,
          templateContent: {
            sealList: isFixedLocate
              ? pdfBoxes.current
                  .filter((box) => !!box.type)
                  .map((box: any) => ({
                    x: box.x,
                    y: box.y,
                    type: box.type,
                    page: box.page + 1,
                    width: box.pageWidth,
                    height: box.pageHeight,
                    info: JSON.stringify(box),
                  }))
              : [],
            variableList: pdfBoxes.current
              .filter((box) => !box.type)
              .sort((prev, next) => {
                if (prev.page !== next.page) {
                  return prev.page - next.page
                }
                return next.y - prev.y
              }) // 根据从上往下的顺序排列
              .map((box: any) => ({
                point1: {
                  x: box.x,
                  y: box.y,
                },
                point2: {
                  x: box.x2,
                  y: box.y2,
                },
                page: box.page + 1,
                variableCode: box.code,
                info: JSON.stringify(box),
              })),
            sealKeyWord: !isFixedLocate ? keyWordSearch.current[0] : '',
            signatureKeyWord: !isFixedLocate ? keyWordSearch.current[1] : '',
            sealKeyWord2: !isFixedLocate ? keyWordSearch.current[2] : '',
          },
        }
        if (!handleBeforeSubmit(params)) {
          return
        }
        const { data: result } = isEdit
          ? await editTemplateApi(search.get('id'), params)
          : await createTemplateApi(params)
        if (isEdit && !result.data?.isSuccess) {
          const { relatedPackageIds = [], packageNum = 0, relatedPackageNumbers = [] } = result.data
          const codes = relatedPackageNumbers.join(',')
          // 单个跳转合同包详情、多个跳转合同包列表
          const isSingle = packageNum === 1
          modalWrapperHoc(SaveModal)({
            record: result.data || {},
            type: 'type',
            onOk: () => {
              isSingle
                ? navigate(
                    `${baseRoute}/contract/basic-config/contract-package/edit?id=${relatedPackageIds?.[0]}`,
                  )
                : navigate(`${baseRoute}/contract/basic-config/contract-package?codes=${codes}`)
            },
          })
        } else {
          isEdit ? message.success('编辑成功') : message.success('保存成功')
        }
        navigate(-1)
      } finally {
        setLoading(false)
      }
    })
  }

  const items = steps.map((item) => ({
    key: item.title,
    title: item.title,
  }))

  const handleNext = async () => {
    try {
      await form?.validateFields()
      const { name, category, ...restBaseInfo } = form?.getFieldsValue() || {}

      if (name) {
        // 去前后空格
        restBaseInfo.name = name.trim()
      }

      // 区分 朴朴内部员工 or 自营第三方
      restBaseInfo.isSupportInner = category.includes('isSupportInner')
      restBaseInfo.isSupportOuter = category.includes('isSupportOuter')

      const params = {
        ...basicInfo,
        ...restBaseInfo,
      }

      setBaseInfo(params)
      setCurrent(current + 1)
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(error)
    }
  }

  const handleCancel = () => {
    const values = form?.getFieldsValue() || {}

    const isSameValuesInEdit = Object.keys(values).every(
      (key) => values[key] === initBasicInfo[key],
    )

    const notEmpty = Object.keys(values).some((key) => values[key])
    if (!notEmpty || isSameValuesInEdit) {
      navigate(-1)
      return
    }
    Modal.confirm({
      title: '提示',
      content: '您编辑的内容尚未保存，确定要离开此页面吗？',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        navigate(-1)
      },
    })
  }

  // 初始化合同模板
  const initParams = async (id) => {
    setLoading(true)
    try {
      const {
        data: { data },
      } = await getTemplateDetailApi(id)

      const info = {
        name: data.name,
        typeCode: data.typeCode,
        sealCode: data.sealCode,
        signMethodCode: data.signMethodCode,
        remark: data.remark,
        sealLocateModeCode: data.sealLocateModeCode,
        code: data.code,
        relationList: data.relationList,
        number: data.number,
        category: ([] as Array<string>)
          .concat(data.isSupportInner ? ['isSupportInner'] : [])
          .concat(data.isSupportOuter ? ['isSupportOuter'] : []),
      }

      const {
        sealList = [],
        variableList = [],
        signatureKeyWord = '',
        sealKeyWord = '',
        sealKeyWord2 = '',
      } = data.templateContent || {}
      const seal = sealList.map((v) => JSON.parse(v.info || '{}'))
      const vars = variableList.map((v) => ({
        ...JSON.parse(v.info || '{}'),
        title: data.variableNameMap[toCamelCase(v.variableCode)],
      }))

      setBaseInfo(info)
      setInitBasicInfo(info)
      setInitBoxes([...seal, ...vars])
      setInitSearch([sealKeyWord, signatureKeyWord, sealKeyWord2])
      setInitLocateMode(info.sealLocateModeCode)
      setInitFontParams({ fontType: data.fontType, fontSize: data.fontSize })

      const fileData = await getFileDataServiceApi(data.fileId)
      setUploadFile({
        id: data.fileId,
        name: data.fileName,
        data: `data:application/pdf;base64,${fileData}`,
      })

      // 初始值不会导致监听的变化，需要手动去设置状态
      if (info.signMethodCode === 'CM_ST_003') {
        form?.setFieldsState(['sealCode'], {
          value: undefined,
          rules: [],
          disabled: true,
          placeholder: '员工单方签署不需要选择该项',
          props: {
            placeholder: '员工单方签署不需要选择该项',
          },
        })
      }

      if (info.typeCode === 'CM_TT_002') {
        form?.setFieldsState(['code'], {
          value: undefined,
          rules: [],
          disabled: true,
          placeholder: '证明不需要输入该项',
          props: {
            placeholder: '证明不需要输入该项',
          },
        })
      }
    } finally {
      setLoading(false)
    }
  }

  const signType = useMemo(() => basicInfo.signMethodCode, [basicInfo])
  const sealCode = useMemo(() => basicInfo.sealCode, [basicInfo])

  const Footer = (
    <div className={styles.footer}>
      <Button className={styles.btn} onClick={handleCancel}>
        取消
      </Button>
      {current === 1 && (
        <>
          <Button className={styles.btn} onClick={() => setCurrent((num) => num - 1)}>
            上一步
          </Button>
          <Dropdown.Button
            style={{ marginLeft: '0' }}
            className={styles.btn}
            loading={loading}
            type="primary"
            onClick={() => handleSubmit()}
            menu={{ items: menuItems, onClick: () => handleSubmit('IS_SAVE') }}
          >
            保存
          </Dropdown.Button>
        </>
      )}
      {current === 0 && (
        <Button className={styles.btn} type="primary" onClick={() => handleNext()}>
          下一步
        </Button>
      )}
    </div>
  )

  const updateBoxes = (boxes = []) => {
    pdfBoxes.current = boxes
    setInitBoxes(boxes)
  }

  const onSealCodeChange = () => {
    const newPdfBox: any = pdfBoxes.current.filter((i) => !i.type)
    updateBoxes(newPdfBox)
    setInitSearch(['', '', ''])
  }

  const docLoaded = (pageTotal: number) => setTotal(pageTotal)

  // 防止当前 state 改变使 PDF 编辑组件 Document 重新渲染闪一下
  const pageHeaderParams = useMemo(
    () => ({
      total,
      pageIndex: 0,
      preTotal: 0,
    }),
    [total],
  )

  const updateFile = (file) => {
    setUploadFile(file)
    updateBoxes()
    setInitSearch(['', '', ''])
  }

  useEffect(() => {
    if (templateId) {
      initParams(templateId)
    }
  }, [])

  return (
    <div className={styles.createWrapper}>
      <div className={styles.step}>
        <Steps current={current} items={items} className={styles.steps} />
      </div>
      <BasicInfo
        extraClassName={current !== 0 ? styles.switch : ''}
        initValues={basicInfo}
        form={form}
        isEdit={!!search.get('id')}
        onSealCodeChange={onSealCodeChange}
      />
      <div className={classNames(styles.contentWrapper, current !== 1 ? styles.switch : '')}>
        {!uploadFile ? (
          <DraggerUpload onFileUpload={(file) => updateFile(file)} />
        ) : (
          <PdfEdit
            extraClassName={styles.templateCreate}
            ref={pdfRef}
            extendAction={<UploadAction onFileUpload={(file) => updateFile(file)} />}
            keyForm={keyForm}
            fontRef={fontRef}
            signType={signType}
            sealCode={sealCode}
            file={uploadFile}
            initSearch={initSearch}
            initBoxes={initBoxes}
            initLocateMode={initLocateMode}
            initFontParams={initFontParams}
            pageHeaderConfig={pageHeaderParams}
            docLoaded={docLoaded}
            onBoxChange={(v) => {
              pdfBoxes.current = v
            }}
            onLocateModeChange={(mode) => {
              locateMode.current = mode
            }}
            onSearchChange={(text) => {
              keyWordSearch.current = text
            }}
            onFontParamsChange={(values) => {
              fontParams.current = values
            }}
          />
        )}
      </div>
      {Footer}
    </div>
  )
}

export default TemplateCreate
