import React, { useState, useEffect, useRef } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Item, Observer } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/antd-schema-form'
import { message, Card } from 'antd'
import { isEqual, debounce } from 'lodash-es'
import BasicFooter from '@/components/pageFooter/BasicFooter'
import PageLoading from '@/components/pageLoading'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
// 常量
import { WARNING } from '@/constants/package'
import { OFFER_BUSINESS_CODE_INNER, OFFER_BUSINESS_CODE_OUTER } from '@/constants/sign-manage'
// 接口
import { getParamRule, dealParamRule } from '@/api/basicConfig/business-param-rule'
import ShortChain from '@/components/headerTitle/ShortChain'
import { getPermission } from '@galaxy/rbac'
import { RULE_CONFIG } from '@/constants/rbac-code'
import { toCamelCaseInner } from '@galaxy/utils'
import {
  SignMethodsTitle,
  ValidDurationTitle,
  AutomaticResendTimeTitle,
} from './components/TooltipsTitle'

const canEdit = getPermission(RULE_CONFIG.RULE_CHECK)

const afterList = [
  {
    label: 1,
    value: 1,
  },
  {
    label: 2,
    value: 2,
  },
  {
    label: 3,
    value: 3,
  },
]

const beforeList = [
  {
    label: '当天',
    value: 0,
  },
  {
    label: '前1天',
    value: 1,
  },
  {
    label: '前2天',
    value: 2,
  },
  {
    label: '前3天',
    value: 3,
  },
  {
    label: '前4天',
    value: 4,
  },
  {
    label: '前5天',
    value: 5,
  },
  {
    label: '前6天',
    value: 6,
  },
  {
    label: '前7天',
    value: 7,
  },
]

const disabledInner = (form) => {
  const inner = form.formData?.validDuration[form.index]?.businessNameCode
  if (OFFER_BUSINESS_CODE_INNER.indexOf(inner) !== -1) {
    return true
  }
  return false
}

const disabledOuter = (form) => {
  const outer = form.formData?.outerDuration[form.index]?.businessNameCode
  if (OFFER_BUSINESS_CODE_OUTER.indexOf(outer) !== -1) {
    return true
  }
  return false
}

const disabledOuterForOffer = (form) => {
  const outerCode = form.formData?.outerDuration[form.index]?.businessNameCode
  // offer 签署时禁用
  if (outerCode === 'CM_BI_014' || OFFER_BUSINESS_CODE_OUTER.indexOf(outerCode) !== -1) {
    return true
  }

  return false
}

const disabledInnerStatus = (form, key) => {
  const fieldConfig = form.formData?.fieldChangeConfigInner[form.index]
  return !fieldConfig?.[key]
}

const disabledOuterStatus = (form, key) => {
  const fieldConfig = form.formData?.fieldChangeConfigOuter[form.index]
  return !fieldConfig?.[key]
}

// 表单
const schema: SchemaType = {
  validDuration: {
    component: 'ArrayTable',
    type: 'array',
    props: {
      columns: [
        {
          key: 'serialNumber',
          title: '序号',
          width: 100,
        },
        {
          key: 'businessName',
          title: '业务项',
          width: 300,
        },
        {
          key: 'type',
          title: <SignMethodsTitle />,
          width: 300,
        },
        {
          key: 'validDuration',
          title: <ValidDurationTitle />,
          required: true,
          width: 200,
        },
        {
          key: 'automaticResendTime',
          title: <AutomaticResendTimeTitle />,
          width: 150,
        },
        {
          key: 'isSupportStartSignInner',
          title: '支持手动发起签署',
          width: 100,
        },
        /** V1.5.0 屏蔽配置 */
        // {
        //   key: 'isSupportDischargeInner',
        //   title: '支持解除合同',
        //   width: 100,
        // },
        {
          key: 'isSupportInvalidInner',
          title: '支持作废',
          width: 100,
        },
      ],
      operations: [
        {
          key: 'add',
          inline: true, // 添加的行都是最后一行
        },
      ],
    },
    item: {
      type: 'object',
      fields: {
        serialNumber: {
          component: 'Text',
        },
        businessName: {
          component: 'Text',
        },
        type: {
          component: ({ value }) => (value ? '快捷签署' : '普通签署'),
        },
        validDuration: {
          component: ShortChain,
          placeholder: '请选择有效时长',
          options: Observer({
            watch: './validDuration',
            action: (value) => (value.type === 'CM_ED_001' ? afterList : beforeList),
          }),
          disabled: !canEdit,
          props: {
            code: 'EFFECTIVE_DURATION',
          },
          required: [true, '请选择有效时长'],
          rules: [
            {
              validator: (
                _,
                value = {
                  type: null,
                  day: null,
                },
              ) => {
                if (value.type === null || value.type === undefined) {
                  return Promise.reject(new Error('请选择有效时长'))
                }
                if (value.day === null || value.day === undefined) {
                  return Promise.reject(new Error('请输入有效时长'))
                }
                if (value.day < 0) {
                  return Promise.reject(new Error('请输入大于 0 的数字'))
                }
                if (value.day > 7) {
                  return Promise.reject(new Error('请输入不超过 7 的数字'))
                }
                return Promise.resolve()
              },
              validateTrigger: 'onBlur',
            },
          ],
        },
        automaticResendTime: {
          component: 'Select',
          options: [
            { value: 1, label: 1 },
            { value: 2, label: 2 },
            { value: 3, label: 3 },
          ],
          disabled: !canEdit,
          props: {
            style: {
              width: '100px',
            },
          },
          placeholder: '',
        },
        isSupportStartSignInner: {
          component: 'Check',
          // disabled: true,
          disabled: (form) => disabledInnerStatus(form, 'isSupportStartSignInner'),
        },
        /** V1.5.0 屏蔽配置 */
        // isSupportDischargeInner: {
        //   component: 'Check',
        //   // disabled: disabledInner,
        //   disabled: (form) => disabledInnerStatus(form, 'isSupportDischargeInner'),
        // },
        isSupportInvalidInner: {
          component: 'Check',
          // disabled: disabledInner,
          disabled: (form) => disabledInnerStatus(form, 'isSupportInvalidInner'),
        },
      },
    },
  },
  outerDuration: {
    component: 'ArrayTable',
    type: 'array',
    props: {
      columns: [
        {
          key: 'serialNumber',
          title: '序号',
          width: 100,
        },
        {
          key: 'businessName',
          title: '业务项',
          width: 300,
        },
        {
          key: 'isSupportStartSignOuter',
          title: '支持手动发起签署',
          width: 200,
        },
        /** V1.5.0 屏蔽配置 */
        // {
        //   key: 'isSupportDischargeOuter',
        //   title: '支持解除合同',
        //   width: 200,
        // },
        {
          key: 'isSupportInvalidOuter',
          title: '支持作废',
          width: 200,
        },
      ],
      operations: [
        {
          key: 'add',
          inline: true, // 添加的行都是最后一行
        },
      ],
    },
    item: {
      type: 'object',
      fields: {
        serialNumber: {
          component: 'Text',
        },
        businessName: {
          component: 'Text',
        },
        isSupportStartSignOuter: {
          component: 'Check',
          // disabled: true,
          disabled: (form) => disabledOuterStatus(form, 'isSupportStartSignOuter'),
        },
        /** V1.5.0 屏蔽配置 */
        // isSupportDischargeOuter: {
        //   component: 'Check',
        //   // disabled: disabledOuterForOffer,
        //   disabled: (form) => disabledOuterStatus(form, 'isSupportDischargeOuter'),
        // },
        isSupportInvalidOuter: {
          component: 'Check',
          // disabled: disabledOuter,
          disabled: (form) => disabledOuterStatus(form, 'isSupportInvalidOuter'),
        },
      },
    },
  },
  // setting: {
  //   // 预警字典： CM_BI_013
  //   component: (props) => (
  //     <div
  //       style={{
  //         display: 'flex',
  //         alignItems: 'center',
  //       }}
  //     >
  //       合同到期前
  //       <InputNumber
  //         addonAfter="天"
  //         controls={false}
  //         precision={0}
  //         min={1}
  //         max={60}
  //         style={{
  //           margin: '0 10px',
  //         }}
  //         {...props}
  //         disabled={!canEdit}
  //       />
  //       预警（概览数据展示）
  //     </div>
  //   ),
  //   rules: [
  //     // {
  //     //   type: 'number', max: 60, transform: (value) => Number(value), message: '请输入不超过 60 的数字',
  //     // },
  //     // {
  //     //   type: 'number', min: 1, transform: (value) => Number(value), message: '请输入大于 0 的数字',
  //     // },
  //     {
  //       validator: (_, value) => {
  //         if (value) {
  //           if (value.day <= 0) {
  //             return Promise.reject(new Error('请输入大于 0 的数字'))
  //           }
  //           if (value.day > 60) {
  //             return Promise.reject(new Error('请输入不超过 60 的数字'))
  //           }
  //         }
  //         return Promise.resolve()
  //       },
  //     },
  //   ],
  // },
}

const ParamsRule = () => {
  const form = SchemaForm.createForm()
  const originRef = useRef({})
  const [loading, setLoading] = useState(true)
  const [disabled, setDisabled] = useState(true)

  const getParams = () => {
    getParamRule()
      .then(({ data: { data } }) => {
        // 预警过滤
        const validDuration: any = []
        const outerDuration: any = []
        const fieldChangeConfigInner: any = []
        const fieldChangeConfigOuter: any = []
        let setting = ''
        let validCurrent = 1
        let outerCurrent = 1
        data.forEach((item) => {
          const objInner: any = {
            ...item,
            validDuration: {
              type: item.validDurationCode || undefined,
              day: item.validDurationDay || undefined,
            },
            automaticResendTime: item.automaticResendTime || undefined,
            businessName: item.businessName,
            isSupportStartSignInner: item.isSupportStartSignInner || false,
            /** V1.5.0 屏蔽配置 */
            // isSupportDischargeInner: item.isSupportDischargeInner || false,
            isSupportInvalidInner: item.isSupportInvalidInner || false,
          }
          const objOuter: any = {
            ...item,
            businessName: item.businessName,
            isSupportStartSignOuter: item.isSupportStartSignOuter || false,
            /** V1.5.0 屏蔽配置 */
            // isSupportDischargeOuter: item.isSupportDischargeOuter || false,
            isSupportInvalidOuter: item.isSupportInvalidOuter || false,
          }
          // 合同补签
          if (item.businessNameCode === 'CM_BI_014') {
            objInner.isSupportStartSignInner = true
          }
          if (item.businessNameCode === WARNING) {
            setting = item.validDurationDay || undefined
          }
          if (item.isSupportInner) {
            objInner.serialNumber = validCurrent
            validDuration.push(objInner)
            validCurrent += 1
            if (item.fieldChangeConfig) {
              fieldChangeConfigInner.push(toCamelCaseInner(JSON.parse(item.fieldChangeConfig)))
            }
          }
          if (item.isSupportOuter) {
            objOuter.serialNumber = outerCurrent
            outerDuration.push(objOuter)
            outerCurrent += 1
            if (item.fieldChangeConfig) {
              fieldChangeConfigOuter.push(toCamelCaseInner(JSON.parse(item.fieldChangeConfig)))
            }
          }
        })
        const formData = {
          validDuration,
          outerDuration,
          setting,
          fieldChangeConfigInner,
          fieldChangeConfigOuter,
        }
        form.setFieldsValue(formData)
        originRef.current = formData
      })
      .finally(() => {
        setLoading(false)
      })
  }

  // 保存按钮
  const handleConfirm = async (data) => {
    const innerData = data.validDuration
    const outerData = data.outerDuration
    const list = innerData
      ? innerData?.map((item) => {
          const obj = {
            id: item.id,
            automaticResendTime: item.automaticResendTime,
            businessNameCode: item.businessNameCode,
            /** V1.5.0 屏蔽配置 */
            // isSupportDischargeInner: item.isSupportDischargeInner,
            // isSupportDischargeOuter: false,
            isSupportInner: item.isSupportInner,
            isSupportInvalidInner: item.isSupportInvalidInner,
            isSupportInvalidOuter: false,
            isSupportOuter: false,
            isSupportStartSignInner: item.isSupportStartSignInner,
            isSupportStartSignOuter: false,
            validDurationCode: item.validDuration.type,
            validDurationDay: item.validDuration.day,
          }
          if (item.businessNameCode === 'CM_BI_014' && item.isSupportInner) {
            obj.isSupportStartSignInner = true
          }
          return obj
        })
      : []
    for (let i = 0; i < outerData?.length; i++) {
      const isExist = innerData?.filter((v) => v.id === outerData[i].id)
      if (isExist?.length) {
        for (let j = 0; j < list.length; j++) {
          if (list[j].id === outerData[i].id) {
            /** V1.5.0 屏蔽配置 */
            // list[j].isSupportDischargeOuter = outerData[i].isSupportDischargeOuter
            list[j].isSupportInvalidOuter = outerData[i].isSupportInvalidOuter
            list[j].isSupportOuter = outerData[i].isSupportOuter
            list[j].isSupportStartSignOuter = outerData[i].isSupportStartSignOuter
          }
        }
      } else {
        const item = outerData[i]
        const obj = {
          id: item.id,
          automaticResendTime: item.automaticResendTime,
          businessNameCode: item.businessNameCode,
          /** V1.5.0 屏蔽配置 */
          // isSupportDischargeInner: false,
          // isSupportDischargeOuter: item.isSupportDischargeOuter,
          isSupportInner: false,
          isSupportInvalidInner: false,
          isSupportInvalidOuter: item.isSupportInvalidOuter,
          isSupportOuter: item.isSupportOuter,
          isSupportStartSignInner: false,
          isSupportStartSignOuter: item.isSupportStartSignOuter,
          validDurationCode: item.validDuration.type,
          validDurationDay: item.validDuration.day,
        }
        list.push(obj)
      }
    }
    list.push({
      businessNameCode: WARNING,
      business_name: '预警',
      validDurationDay: data.setting,
    })
    dhrReport.trace(REPORT_EVENT_TYPE.CONFIG_SAVE, list)
    setLoading(true)
    await dealParamRule(list)
    message.success('保存成功')
    originRef.current = data
    setDisabled(true)
    setLoading(false)
  }

  // 字段改变
  const onValuesChange = debounce(() => {
    const values = form.getFieldsValue(true)
    const isSame = isEqual(values, originRef.current)

    if (isSame !== disabled) {
      setDisabled(isSame)
    }
  }, 500)

  useEffect(() => {
    getParams()
  }, [])

  return (
    <SchemaForm
      form={form}
      schema={schema}
      style={{
        margin: '0 24px 50px',
      }}
      labelCol={{
        flex: '0 0 150px',
      }}
      onValuesChange={onValuesChange}
    >
      {loading ? (
        <PageLoading />
      ) : (
        <>
          <Card title="朴朴员工业务项" bordered={false}>
            <Item field="validDuration" />
          </Card>
          {/* <Card title="其他设置" bordered={false} style={{ marginTop: 24 }}>
            <Item field="setting" />
          </Card> */}
          <Card title="自营第三方业务项" bordered={false} style={{ marginTop: 24 }}>
            <Item field="outerDuration" />
          </Card>
          {canEdit && (
            <BasicFooter
              loading={loading}
              handleConfirm={handleConfirm}
              showCancel={false}
              confirmProps={{
                disabled,
              }}
            />
          )}
        </>
      )}
    </SchemaForm>
  )
}

export default ParamsRule
