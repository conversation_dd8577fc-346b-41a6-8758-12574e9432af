import React from 'react'
import { Tooltip } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'

export const AutomaticResendTimeTitle = () => (
  <>
    短链自动重发次数（次）
    <Tooltip title="当短链有效时长已经过期，但是员工还未签署合同/协议，系统将会根据该设置自动发送短链，再次发送的短链将继续沿用有效时长。">
      <QuestionCircleOutlined
        style={{
          marginLeft: '10px',
          cursor: 'help',
        }}
      />
    </Tooltip>
  </>
)

export const SignMethodsTitle = () => (
  <>
    签署方式
    <Tooltip
      title={
        <>
          法大大支持普通签署和快捷签署两种方式，每个业务项都需要确认采用哪种签署方式，暂不支持修改
          <br />
          1、快捷签署：仅有1条短信，实名认证和签署短信合并发送，短链有效期为3天；
          <br />
          2、普通签署：会有2条短信，实名认证和签署短信分开发送，短链有效期为7天。
        </>
      }
    >
      <QuestionCircleOutlined
        style={{
          marginLeft: '10px',
          cursor: 'help',
        }}
      />
    </Tooltip>
  </>
)

export const ValidDurationTitle = () => (
  <>
    短链有效时长（天）
    <Tooltip title="默认获取法大大签署方式对应的短链有效时长，支持在法大大限制的基础上再做调整。修改后，短链时长将会以该设置为准。">
      <QuestionCircleOutlined
        style={{
          marginLeft: '10px',
          cursor: 'help',
        }}
      />
    </Tooltip>
  </>
)
