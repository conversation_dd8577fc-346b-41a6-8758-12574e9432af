import React from 'react'
import type { SchemaColumnType } from '@amazebird/antd-schema-table'
import { RedirectToPackagePage } from '@/pages/basicConfig/contractPackage/common'
import { STATUS } from '@/constants'

const getTypeRulesColumns = () => {
  const columns: SchemaColumnType = [
    {
      title: '规则名称',
      dataIndex: 'name',
      cell: {
        type: 'Text',
        props: {
          ellipsis: true,
        },
      },
      fixed: 'left',
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'status',
      cell: 'Status',
      options: STATUS,
    },
    {
      title: '规则编号',
      dataIndex: 'ruleNo',
      cell: 'Text',
    },
    {
      title: '分类',
      dataIndex: 'isSupportInner',
      cell: {
        type: 'Text',
        render: ({ record }) => {
          const arr: string[] = []
          if (record.isSupportInner) {
            arr.push('朴朴员工规则')
          }
          if (record.isSupportOuter) {
            arr.push('自营第三方规则')
          }
          return arr.length ? arr.join('、') : '--'
        },
      },
    },
    {
      title: '生效日期',
      dataIndex: 'effectiveDate',
      cell: 'Date',
    },
    {
      title: '停用日期',
      dataIndex: 'disableDate',
      cell: 'Date',
    },
    {
      title: '创建时间',
      dataIndex: 'timeCreate',
      cell: 'DateTime',
    },
    {
      title: '创建人',
      dataIndex: 'userIdCreate',
      cell: ({ text, record }) => `${record.userNameCreate}（${text}）`,
      width: 150,
    },
  ]

  const searchColumns = [
    {
      dataIndex: 'name',
      title: '规则名称',
      component: 'Input',
      props: {
        allowClear: true,
      },
    },
    {
      title: '规则编号',
      dataIndex: 'ruleNo',
      component: 'Input',
      props: {
        allowClear: true,
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      component: 'Select',
      options: [
        {
          value: 'all',
          label: '全部',
        },
        ...STATUS,
      ],
    },
    {
      title: '分类',
      dataIndex: 'classification',
      component: 'Select',
      options: [
        {
          label: '朴朴员工规则',
          value: 0,
        },
        {
          label: '自营第三方规则',
          value: 1,
        },
      ],
      props: {
        mode: 'multiple',
      },
    },
    {
      title: '生效日期',
      dataIndex: 'effectiveDate',
      component: 'RangePicker',
    },
    {
      title: '停用日期',
      dataIndex: 'disableDate',
      component: 'RangePicker',
    },
  ]

  return {
    columns,
    searchColumns,
  }
}

export default getTypeRulesColumns
