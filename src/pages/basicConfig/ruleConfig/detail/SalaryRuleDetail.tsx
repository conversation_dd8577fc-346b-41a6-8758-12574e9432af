import React from 'react'
import { Descriptions } from 'antd'
import moment from 'moment'
import { Title } from '@/components/headerTitle'
import styles from './style.module.less'

const SalaryRuleDetail = (props: any) => {
  const { data } = props
  return (
    <div className={styles.detail}>
      <Title title="基础信息" />
      <Descriptions
        column={3}
        labelStyle={{
          color: '#8C8C8C',
          width: 100,
          display: 'flex',
          justifyContent: 'end',
        }}
      >
        <Descriptions.Item label="规则名称">{data.name || '--'}</Descriptions.Item>
        <Descriptions.Item label="规则编号">{data.ruleNo || '--'}</Descriptions.Item>
        <Descriptions.Item label="生效日期">
          {moment(data.effectiveDate).format('YYYY-MM-DD')}
        </Descriptions.Item>
        <Descriptions.Item label="停用日期">
          {data.disableDate ? moment(data.disableDate).format('YYYY-MM-DD') : '--'}
        </Descriptions.Item>
        <Descriptions.Item label="状态">{data.status ? '启用中' : '已停用'}</Descriptions.Item>
        <Descriptions.Item label="创建人">
          {`${data.userNameCreate}（${data.userIdCreate}）` || '--'}
        </Descriptions.Item>
        <Descriptions.Item label="创建时间">
          {moment(data.timeCreate).format('YYYY-MM-DD HH:mm:ss')}
        </Descriptions.Item>
      </Descriptions>
      <Title
        title="规则设置"
        style={{
          marginTop: '24px',
        }}
      />
      <Descriptions
        column={3}
        labelStyle={{
          color: '#8C8C8C',
          width: 100,
          display: 'flex',
          justifyContent: 'end',
        }}
      >
        <Descriptions.Item label="工作城市">{data.workCityName || '--'}</Descriptions.Item>
        <Descriptions.Item label="基本薪资">
          {data.salary ? `${data.salary}元` : '--'}
        </Descriptions.Item>
      </Descriptions>
    </div>
  )
}

export default SalaryRuleDetail
