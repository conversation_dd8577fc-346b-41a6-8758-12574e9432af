import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from 'antd'
import { RollbackOutlined } from '@ant-design/icons'
import { isUndefined } from 'lodash'
import PermissionAction from '@/components/permissionAction'
import PermissionDropdown from '@/components/permissionDropdown'
import config from '@/config'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import { isLocalNetwork, transformStringType } from '@/utils/utils'
import { RULE_CONFIG } from '@/constants/rbac-code'
import {
  copyTypeRuleFn,
  endTypeRuleFn,
  startTypeRuleFn,
  deleteTypeRuleFn,
} from '../actions/typeRuleActions'
import {
  copyDeadlineRuleFn,
  endDeadlineRuleFn,
  startDeadlineRuleFn,
  deleteDeadlineRuleFn,
} from '../actions/deadlineRuleActions'
import {
  copySalaryRuleFn,
  endSalaryRuleFn,
  startSalaryRuleFn,
  deleteSalaryRuleFn,
} from '../actions/salaryRuleActions'

import styles from './style.module.less'

const { baseRoute } = config

const Extra = (props: any) => {
  const { type, id, data, getDetail } = props
  const navigate = useNavigate()
  const ruleType = transformStringType(type)
  const codes = {
    edit: RULE_CONFIG[ruleType].LIST.EDIT,
    start: RULE_CONFIG[ruleType].LIST.ENABLED,
    end: RULE_CONFIG[ruleType].LIST.DISABLED,
    delete: RULE_CONFIG[ruleType].LIST.DELETE,
    copy: RULE_CONFIG[ruleType].LIST.COPY,
  }

  const getActions = (record) => {
    const actions: any[] = [
      record?.status === 0
        ? {
            code: codes.start,
            component: '启用',
          }
        : {
            code: codes.end,
            component: '停用',
            danger: true,
          },
      {
        code: codes.delete,
        component: '删除',
        danger: true,
        disabled: !isUndefined(record?.contractPackageNums) && record?.contractPackageNums !== 0,
      },
    ]
    if (type !== 'salaryRule') {
      /** 工资规则暂时不需要复制功能，会出同一个城市有工资规则在生效时，无法复制这个规则的问题，产品逻辑还需研究 */
      actions.unshift({
        code: codes.copy,
        component: '复制',
      })
    }
    return actions
  }

  const operateActions = (key) => {
    if (type === 'typeRule') {
      // 类型规则
      switch (key) {
        case codes.copy:
          dhrReport.trace(REPORT_EVENT_TYPE.TYPE_RULE_COPY, data)
          copyTypeRuleFn(id)
          break
        case codes.end:
          dhrReport.trace(REPORT_EVENT_TYPE.TYPE_RULE_DEACTIVATE, data)
          endTypeRuleFn(data, (actionName, pathParams: any) => {
            if (actionName === 'redirect') {
              navigate(`${baseRoute}/contract/basic-config/contract-package?codes=${pathParams}`)
            } else {
              getDetail?.()
            }
          })
          break
        case codes.start:
          dhrReport.trace(REPORT_EVENT_TYPE.TYPE_RULE_ENABLE, data)
          startTypeRuleFn(data, getDetail)
          break
        case codes.delete:
          dhrReport.trace(REPORT_EVENT_TYPE.TYPE_RULE_DELETE, data)
          deleteTypeRuleFn(id, () => navigate(-1))
          break
        case codes.edit:
          dhrReport.trace(REPORT_EVENT_TYPE.TYPE_RULE_EDIT, data)
          break
        default:
          break
      }
    }
    if (type === 'deadlineRule') {
      // 签订期限规则
      switch (key) {
        case codes.copy:
          dhrReport.trace(REPORT_EVENT_TYPE.DEADLINE_RULE_COPY, data)
          copyDeadlineRuleFn(id)
          break
        case codes.end:
          dhrReport.trace(REPORT_EVENT_TYPE.DEADLINE_RULE_DEACTIVATE, data)
          endDeadlineRuleFn(data, (actionName, pathParams: any) => {
            if (actionName === 'redirect') {
              navigate(`${baseRoute}/contract/basic-config/contract-package?codes=${pathParams}`)
            } else {
              getDetail?.()
            }
          })
          break
        case codes.start:
          dhrReport.trace(REPORT_EVENT_TYPE.DEADLINE_RULE_ENABLE, data)
          startDeadlineRuleFn(data, getDetail)
          break
        case codes.delete:
          dhrReport.trace(REPORT_EVENT_TYPE.DEADLINE_RULE_DELETE, data)
          deleteDeadlineRuleFn(id, () => navigate(-1))
          break
        case codes.edit:
          dhrReport.trace(REPORT_EVENT_TYPE.DEADLINE_RULE_EDIT, data)
          break
        default:
          break
      }
    }
    if (type === 'salaryRule') {
      // 签订工资规则
      switch (key) {
        case codes.copy:
          dhrReport.trace(REPORT_EVENT_TYPE.SALARY_RULE_COPY, data)
          copySalaryRuleFn(id)
          break
        case codes.end:
          dhrReport.trace(REPORT_EVENT_TYPE.SALARY_RULE_DEACTIVATE, data)
          endSalaryRuleFn(data, getDetail)
          break
        case codes.start:
          dhrReport.trace(REPORT_EVENT_TYPE.SALARY_RULE_ENABLE, data)
          startSalaryRuleFn(data, getDetail)
          break
        case codes.delete:
          dhrReport.trace(REPORT_EVENT_TYPE.SALARY_RULE_DELETE, data)
          deleteSalaryRuleFn(id, () => navigate(-1))
          break
        case codes.edit:
          dhrReport.trace(REPORT_EVENT_TYPE.SALARY_RULE_EDIT, data)
          break
        default:
          break
      }
    }
    // 编辑
    if (key === codes.edit) {
      navigate(`${baseRoute}/contract/basic-config/rule-config/edit?type=${type}&id=${id}`)
    }
  }
  return (
    <div className={styles.extra}>
      {(window.__POWERED_BY_QIANKUN__ || isLocalNetwork()) && (
        <span
          className={styles.back}
          onClick={() => {
            navigate(-1)
          }}
        >
          <RollbackOutlined className={styles.icon} />
          返回
        </span>
      )}
      <PermissionAction code={codes.edit} permissions={data?.permissions}>
        <Button
          onClick={() => {
            operateActions(codes.edit)
          }}
        >
          编辑
        </Button>
      </PermissionAction>
      <PermissionDropdown
        actions={getActions(data)}
        permissions={data?.permissions}
        onClick={({ key }) => operateActions(key)}
        showIcon={false}
      >
        <Button>更多</Button>
      </PermissionDropdown>
    </div>
  )
}

export default Extra
