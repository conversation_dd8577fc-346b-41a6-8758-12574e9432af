import React from 'react'
import { Descriptions } from 'antd'
import { SchemaForm, SchemaType, Item } from '@amazebird/antd-schema-form'
import { TreeSelect as Dict } from '@galaxy/dict'
import moment from 'moment'
import { Title } from '@/components/headerTitle'
import { RedirectToPackagePage } from '../../contractPackage/common'
import styles from './style.module.less'

const DeadlineRuleDetail = (props: any) => {
  const { data } = props
  const schema: SchemaType = {
    items: {
      type: 'array',
      component: 'ArrayTable',
      rules: [
        {
          required: true,
          message: '请输入规则设置',
        },
      ],
      props: {
        columns: [
          {
            key: 'seq',
            title: '序号',
            width: 80,
            cellType: 'order',
            render: (_, index) => index + 1,
          },
          {
            key: 'signTimeCode',
            title: '签订次数',
          },
          {
            key: 'signYearLimitCode',
            title: '合同期限',
          },
        ],
        operations: [],
        zebra: true,
      },
      item: {
        type: 'object',
        fields: {
          seq: {
            component: 'Text',
          },
          signTimeCode: {
            component: Dict,
            props: {
              code: 'SIGN_TIME',
              placeholder: '请选择签订次数',
              showSearch: true,
              allowClear: true,
            },
            disabled: true,
          },
          signYearLimitCode: {
            component: Dict,
            props: {
              code: 'CONTRACT_DEADLINE',
              placeholder: '请选择合同期限',
              showSearch: true,
              allowClear: true,
              style: {
                width: 300,
              },
            },
            disabled: true,
          },
        },
      },
    },
  }

  return (
    <div className={styles.detail}>
      <Title title="基础信息" />
      <Descriptions
        column={3}
        labelStyle={{
          color: '#8C8C8C',
          width: 100,
          display: 'flex',
          justifyContent: 'end',
        }}
      >
        <Descriptions.Item label="规则名称">{data.name || '--'}</Descriptions.Item>
        <Descriptions.Item label="规则编号">{data.ruleNo || '--'}</Descriptions.Item>
        <Descriptions.Item label="生效日期">
          {moment(data.effectiveDate).format('YYYY-MM-DD')}
        </Descriptions.Item>
        <Descriptions.Item label="停用日期">
          {data.disableDate ? moment(data.disableDate).format('YYYY-MM-DD') : '--'}
        </Descriptions.Item>
        <Descriptions.Item label="状态">{data.status ? '启用中' : '已停用'}</Descriptions.Item>
        <Descriptions.Item label="创建人">
          {`${data.userNameCreate}（${data.userIdCreate}）` || '--'}
        </Descriptions.Item>
        <Descriptions.Item label="创建时间">
          {moment(data.timeCreate).format('YYYY-MM-DD HH:mm:ss')}
        </Descriptions.Item>
      </Descriptions>
      <Title
        title="规则设置"
        style={{
          marginTop: '24px',
        }}
      />
      <SchemaForm
        schema={schema}
        initialValues={{
          items: data.items,
        }}
      >
        <Item field="items" />
      </SchemaForm>
    </div>
  )
}

export default DeadlineRuleDetail
