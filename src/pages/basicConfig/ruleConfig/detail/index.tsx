import React, { useEffect, useState } from 'react'
import { useStore } from '@/stores'
import { useLocation } from 'react-router-dom'
import queryString from 'query-string'
import { getTypeRuleDetail } from '@/api/basicConfig/type-rule'
import { getSalaryRuleDetail } from '@/api/basicConfig/salary-rule'
import { getDeadlineRuleDetail } from '@/api/basicConfig/deadline-rule'
import Extra from './Extra'
import TypeRuleDetail from './TypeRuleDetail'
import SalaryRuleDetail from './SalaryRuleDetail'
import DeadlineRuleDetail from './DeadlineRuleDetail'

import styles from './style.module.less'

const RuleDetail = () => {
  const store = useStore((state) => state)
  const { setExtra } = store

  const location = useLocation()
  const [data, setData] = useState()
  const { type, id } = queryString.parse(location.search)

  // 获取详情数据
  const getDetail = async () => {
    if (type === 'typeRule') {
      // 类型规则
      const { data: result } = await getTypeRuleDetail(id)
      setData(result.data)
    }
    if (type === 'salaryRule') {
      // 工资签订规则
      const { data: result } = await getSalaryRuleDetail(id)
      setData(result.data)
    }
    if (type === 'deadlineRule') {
      // 合同期限规则
      const { data: result } = await getDeadlineRuleDetail(id)
      setData(result.data)
    }
  }

  useEffect(() => {
    setExtra(<Extra type={type} id={id} data={data} getDetail={getDetail} />)
    return () => {
      setExtra(null)
    }
  }, [data])

  useEffect(() => {
    getDetail()
  }, [])
  return (
    <div className={styles.detailContainer}>
      {type === 'typeRule' && data && <TypeRuleDetail data={data} />}
      {type === 'salaryRule' && data && <SalaryRuleDetail data={data} />}
      {type === 'deadlineRule' && data && <DeadlineRuleDetail data={data} />}
    </div>
  )
}

export default RuleDetail
