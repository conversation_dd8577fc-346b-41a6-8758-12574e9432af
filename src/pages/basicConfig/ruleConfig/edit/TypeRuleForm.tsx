import React from 'react'
import { SchemaForm, Item, FormItemGrid, Observer } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/antd-schema-form'
import { TreeSelect as Dict } from '@galaxy/dict'
import moment from 'moment'
import { cloneDeep } from 'lodash'
import { Title } from '@/components/headerTitle'
import { getNameExists } from '@/api/basicConfig/type-rule'

import styles from './style.module.less'

interface IProps {
  form: any
  id: any
}

const TypeRuleForm = (props: IProps) => {
  const { form, id } = props
  // 名称校验
  // eslint-disable-next-line consistent-return
  const nameValidator = async (_, value) => {
    if (value) {
      const params = {
        name: value,
        id,
      }
      return getNameExists(params).then(({ data: result }) => {
        if (result.data) {
          return Promise.reject(new Error('规则名称重复，请重新命名'))
        }
        return Promise.resolve()
      })
    }
  }

  const schema: SchemaType = {
    name: {
      label: '规则名称',
      component: 'Input',
      max: 50,
      required: true,
      rules: [
        {
          validator: nameValidator,
        },
      ],
    },
    effectiveDate: {
      label: '生效日期',
      component: 'DatePicker',
      rules: [
        {
          required: true,
          message: '请选择生效日期',
        },
      ],
      placeholder: '请选择生效日期',
      observer: Observer({
        watch: 'disableDate',
        action: (value) => {
          const fieldProps: any = {
            style: {
              width: '100%',
            },
            disabledDate: (current) => current && current < moment().startOf('day'),
          }
          if (value) {
            fieldProps.disabledDate = (current) =>
              (current && current < moment().startOf('day')) ||
              (current && current >= cloneDeep(value).startOf('day'))
          }
          return {
            props: fieldProps,
          }
        },
      }),
    },
    disableDate: {
      label: '停用日期',
      component: 'DatePicker',
      placeholder: '请选择停用日期',
      observer: Observer({
        watch: 'effectiveDate',
        action: (value) => {
          const fieldProps: any = {
            style: {
              width: '100%',
            },
            disabledDate: (current) => current && current < moment().startOf('day'),
          }
          if (value) {
            fieldProps.disabledDate = (current) =>
              (current && current <= cloneDeep(value).endOf('day')) ||
              (current && current < moment().startOf('day'))
          }
          return {
            props: fieldProps,
          }
        },
      }),
    },
    classification: {
      label: '分类',
      component: 'Checkbox',
      options: [
        { label: '朴朴员工规则', value: 0 },
        { label: '自营第三方规则', value: 1 },
      ],
      required: true,
    },
    items: {
      type: 'array',
      component: 'ArrayTable',
      rules: [
        {
          required: true,
          message: '请输入规则设置',
        },
      ],
      props: {
        columns: [
          {
            key: 'seq',
            title: '序号',
            width: 80,
            cellType: 'order',
            render: (_, index) => index + 1,
          },
          {
            key: 'signTimeCode',
            required: true,
            title: '签订次数',
          },
          {
            key: 'contractTypeCode',
            required: true,
            title: '合同/协议类型',
          },
          {
            key: 'operation',
            title: '操作',
            width: 100,
            cellType: 'operation',
          },
        ],
        operations: [
          // 需要自定义整个操作功能
          // 采用默认形式
          'delete',
          'add',
        ],
        zebra: false,
      },
      item: {
        type: 'object',
        fields: {
          seq: {
            component: 'Text',
          },
          signTimeCode: {
            component: 'Select',
            props: {
              placeholder: '请选择签订次数',
              style: {
                width: 300,
              },
            },
            rules: [
              {
                required: true,
                message: '请选择签订次数',
              },
            ],
            options: Observer({
              watch: ['items[*].signTimeCode'],
              action: async ([value = []]) => {
                const result: any = await Dict.service.getDictionaryItem('SIGN_TIME', 'CONTRACT')
                if (result) {
                  const options = result.SIGN_TIME.map((i) => ({
                    value: i.code,
                    label: i.name,
                    disabled: value.includes(i.code),
                  }))
                  return options
                }
                return []
              },
            }),
          },
          contractTypeCode: {
            component: Dict,
            props: {
              code: 'CONTRACT_TYPE',
              placeholder: '请选择合同/协议类型',
              showSearch: true,
              allowClear: true,
              style: {
                width: '300px',
              },
            },
            rules: [
              {
                required: true,
                message: '请选择合同/协议类型',
              },
            ],
          },
        },
      },
    },
    version: {
      component: 'Text',
    },
  }

  return (
    <SchemaForm schema={schema} form={form}>
      <div className={styles.ruleConfig}>
        <Title title="基础信息" />
        <FormItemGrid colCount={2}>
          <Item field="name" />
          <Item field="effectiveDate" />
          <Item field="disableDate" />
          <Item field="classification" />
        </FormItemGrid>
        <Title
          title="规则设置"
          style={{
            marginTop: '24px',
          }}
        />
        <Item field="items" />
        <Item field="version" hidden />
      </div>
    </SchemaForm>
  )
}

export default TypeRuleForm
