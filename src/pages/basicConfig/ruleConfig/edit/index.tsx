import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import queryString from 'query-string'
import { Button, message } from 'antd'
import moment from 'moment'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { createTypeRule, getTypeRuleDetail, editTypeRule } from '@/api/basicConfig/type-rule'
import {
  createSalaryRule,
  getSalaryRuleDetail,
  editSalaryRule,
} from '@/api/basicConfig/salary-rule'
import {
  createDeadlineRule,
  getDeadlineRuleDetail,
  editDeadlineRule,
} from '@/api/basicConfig/deadline-rule'
import TypeRuleForm from './TypeRuleForm'
import SalaryRuleForm from './SalaryRuleForm'
import DeadlineRuleForm from './DeadlineRuleForm'

import styles from './style.module.less'

const EditRule = () => {
  const [loading, setLoading] = useState(false)
  const location = useLocation()
  const navigate = useNavigate()
  const form = SchemaForm.createForm()
  const { type, id } = queryString.parse(location.search)
  // 是否为编辑
  const isEdit = !!id

  const successFn = () => {
    message.success('保存成功')
    navigate(-1)
  }

  // 保存
  const submit = async () => {
    try {
      const values = await form?.validateFields()
      if (type === 'typeRule') {
        // 规则类型
        const { disableDate, effectiveDate, items, classification, ...rest } = values
        const params = {
          disableDate: disableDate ? disableDate.endOf('day').valueOf() : 0,
          effectiveDate: effectiveDate.startOf('day').valueOf(),
          items: items.map((i, index) => ({
            ...i,
            seq: index + 1,
          })),
          isSupportInner: false,
          isSupportOuter: false,
          ...rest,
        }
        if (classification?.indexOf(1) !== -1) {
          params.isSupportOuter = true
        }
        if (classification?.indexOf(0) !== -1) {
          params.isSupportInner = true
        }
        setLoading(true)
        if (isEdit) {
          // 编辑
          editTypeRule(id, params)
            .then(() => {
              successFn()
            })
            .finally(() => {
              setLoading(false)
            })
        } else {
          // 新建
          createTypeRule(params)
            .then(() => {
              successFn()
            })
            .finally(() => {
              setLoading(false)
            })
        }
      }
      if (type === 'salaryRule') {
        // 工资规则
        const { disableDate, effectiveDate, salary, ...rest } = values
        const params = {
          disableDate: disableDate ? disableDate.endOf('day').valueOf() : 0,
          effectiveDate: effectiveDate.startOf('day').valueOf(),
          salary: Number(salary).toFixed(2),
          ...rest,
        }
        setLoading(true)
        if (isEdit) {
          // 编辑
          editSalaryRule(id, params)
            .then(() => {
              successFn()
            })
            .finally(() => {
              setLoading(false)
            })
        } else {
          // 新建
          createSalaryRule(params)
            .then(() => {
              successFn()
            })
            .finally(() => {
              setLoading(false)
            })
        }
      }
      if (type === 'deadlineRule') {
        // 合同期限规则
        const { disableDate, effectiveDate, items, classification, ...rest } = values
        const params = {
          disableDate: disableDate ? disableDate.endOf('day').valueOf() : 0,
          effectiveDate: effectiveDate.startOf('day').valueOf(),
          items: items.map((i, index) => ({
            ...i,
            seq: index + 1,
          })),
          isSupportInner: false,
          isSupportOuter: false,
          ...rest,
        }
        if (classification?.indexOf(1) !== -1) {
          params.isSupportOuter = true
        }
        if (classification?.indexOf(0) !== -1) {
          params.isSupportInner = true
        }
        setLoading(true)
        if (isEdit) {
          // 编辑
          editDeadlineRule(id, params)
            .then(() => {
              successFn()
            })
            .finally(() => {
              setLoading(false)
            })
        } else {
          // 新建
          createDeadlineRule(params)
            .then(() => {
              successFn()
            })
            .finally(() => {
              setLoading(false)
            })
        }
      }
    } catch (error) {
      console.error(error)
    }
  }

  // 编辑时获取默认数据
  const getDefaultValues = async () => {
    if (isEdit) {
      if (type === 'typeRule') {
        // 类型规则
        const { data: result } = await getTypeRuleDetail(id)
        const { name, effectiveDate, disableDate, items, version, isSupportInner, isSupportOuter } =
          result.data
        const classification: number[] = []
        if (isSupportInner) {
          classification.push(0)
        }
        if (isSupportOuter) {
          classification.push(1)
        }
        const defaultValues = {
          effectiveDate: moment(effectiveDate),
          disableDate: disableDate ? moment(disableDate) : undefined,
          name,
          items,
          version,
          classification,
        }
        form?.setFieldsValue(defaultValues)
      }
      if (type === 'salaryRule') {
        // 工资签订规则
        const { data: result } = await getSalaryRuleDetail(id)
        const { effectiveDate, disableDate, name, workCityCode, salary, version } = result.data
        const defaultValues = {
          effectiveDate: moment(effectiveDate),
          disableDate: disableDate ? moment(disableDate) : undefined,
          name,
          workCityCode,
          salary,
          version,
        }
        form?.setFieldsValue(defaultValues)
      }
      if (type === 'deadlineRule') {
        // 合同期限规则
        const { data: result } = await getDeadlineRuleDetail(id)
        const { name, effectiveDate, disableDate, items, version, isSupportInner, isSupportOuter } =
          result.data
        const classification: number[] = []
        if (isSupportInner) {
          classification.push(0)
        }
        if (isSupportOuter) {
          classification.push(1)
        }
        const defaultValues = {
          effectiveDate: moment(effectiveDate),
          disableDate: disableDate ? moment(disableDate) : undefined,
          name,
          items,
          version,
          classification,
        }
        form?.setFieldsValue(defaultValues)
      }
    }
  }

  useEffect(() => {
    getDefaultValues()
  }, [])
  return (
    <div className={styles.editRuleContainer}>
      {type === 'typeRule' && <TypeRuleForm form={form} id={id} />}
      {type === 'salaryRule' && <SalaryRuleForm form={form} id={id} />}
      {type === 'deadlineRule' && <DeadlineRuleForm form={form} id={id} />}
      <div className={styles.footer}>
        <Button className={styles.cancel} onClick={() => navigate(-1)}>
          取消
        </Button>
        <Button className={styles.confirm} type="primary" onClick={submit} loading={loading}>
          保存
        </Button>
      </div>
    </div>
  )
}

export default EditRule
