import React from 'react'
import { SchemaForm, Item, FormItemGrid, Observer } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/antd-schema-form'
import { TreeSelect as Dict } from '@galaxy/dict'
import moment from 'moment'
import { cloneDeep } from 'lodash'
import { Title } from '@/components/headerTitle'
import config from '@/config'
import { getNameExists } from '@/api/basicConfig/salary-rule'

import styles from './style.module.less'

interface IProps {
  form: any
  id: any
}

const { tenantId } = config

const SalaryRuleForm = (props: IProps) => {
  const { form, id } = props
  // 名称校验
  // eslint-disable-next-line consistent-return
  const nameValidator = async (_, value) => {
    if (value) {
      const params = {
        name: value,
        id,
      }
      return getNameExists(params).then(({ data: result }) => {
        if (result.data) {
          return Promise.reject(new Error('规则名称重复，请重新命名'))
        }
        return Promise.resolve()
      })
    }
  }
  const schema: SchemaType = {
    name: {
      label: '规则名称',
      component: 'Input',
      required: true,
      max: 50,
      rules: [
        {
          validator: nameValidator,
        },
      ],
    },
    effectiveDate: {
      label: '生效日期',
      component: 'DatePicker',
      rules: [
        {
          required: true,
          message: '请选择生效日期',
        },
      ],
      placeholder: '请选择生效日期',
      observer: Observer({
        watch: 'disableDate',
        action: (value) => {
          const fieldProps: any = {
            style: {
              width: '100%',
            },
            disabledDate: (current) => current && current < moment().startOf('day'),
          }
          if (value) {
            fieldProps.disabledDate = (current) =>
              (current && current < moment().startOf('day')) ||
              (current && current >= cloneDeep(value).startOf('day'))
          }
          return {
            props: fieldProps,
          }
        },
      }),
    },
    disableDate: {
      label: '停用日期',
      component: 'DatePicker',
      placeholder: '请选择停用日期',
      observer: Observer({
        watch: 'effectiveDate',
        action: (value) => {
          const fieldProps: any = {
            style: {
              width: '100%',
            },
            disabledDate: (current) => current && current < moment().startOf('day'),
          }
          if (value) {
            fieldProps.disabledDate = (current) =>
              (current && current <= cloneDeep(value).endOf('day')) ||
              (current && current < moment().startOf('day'))
          }
          return {
            props: fieldProps,
          }
        },
      }),
    },
    workCityCode: {
      label: '工作城市',
      component: Dict,
      props: {
        code: 'BM_73',
        scope: ['EHR'],
        placeholder: '请选择工作城市',
        showSearch: true,
        allowClear: true,
        tenantId,
        getPopupContainer: (trigger) => trigger.parentNode,
      },
      rules: [
        {
          required: true,
          message: '请选择工作城市',
        },
      ],
    },
    salary: {
      label: '基本薪资',
      component: 'InputNumber',
      required: true,
      props: {
        addonAfter: '元',
        precision: 2,
        max: 99999.99,
        min: 0,
        style: {
          width: '100%',
        },
      },
    },
    version: {
      component: 'Text',
    },
  }
  return (
    <SchemaForm schema={schema} form={form}>
      <div className={styles.ruleConfig}>
        <Title title="基础信息" />
        <FormItemGrid colCount={2}>
          <Item field="name" />
          <Item field="effectiveDate" />
          <Item field="disableDate" />
        </FormItemGrid>
        <Title
          title="规则设置"
          style={{
            marginTop: '24px',
          }}
        />
        <FormItemGrid colCount={2}>
          <Item field="workCityCode" />
          <Item field="salary" />
          <Item field="version" hidden />
        </FormItemGrid>
      </div>
    </SchemaForm>
  )
}

export default SalaryRuleForm
