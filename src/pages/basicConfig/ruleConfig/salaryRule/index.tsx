import React, { useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { SchemaTable, createAction, Delete } from '@amazebird/antd-schema-table'
import type { SchemaColumnType } from '@amazebird/antd-schema-table'
import { Button, message } from 'antd'
import config from '@/config'
import { getSalaryRules, deleteSalaryRule } from '@/api/basicConfig/salary-rule'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import PermissionAction from '@/components/permissionAction'
import { stringTransform } from '@/utils/utils'
import { RULE_CONFIG } from '@/constants/rbac-code'
import getSalaryRulesColumns from './ColumnsConfig'
import { operateSalaryRule } from '../actions/salaryRuleActions'

type ColumnsItem = SchemaColumnType[number]
const { baseRoute } = config

const getActions = (record) => [
  /** 工资规则暂时不需要复制功能，会出同一个城市有工资规则在生效时，无法复制这个规则的问题，产品逻辑还需研究 */
  // { code: 'copy', component: '复制' },
  record.status === 0
    ? {
        code: RULE_CONFIG.SALARY_RULE.LIST.ENABLED,
        component: '启用',
      }
    : {
        code: RULE_CONFIG.SALARY_RULE.LIST.DISABLED,
        component: '停用',
      },
]

const TypeConfig = () => {
  const action = createAction()
  const navigate = useNavigate()
  const operateActions = async (key, record) => {
    operateSalaryRule(key, record, () => action.refresh())
  }

  const onDelete = (record) => {
    deleteSalaryRule(record.id).then(() => {
      message.success('删除成功')
      action.refresh()
    })
  }

  const operateColumns: ColumnsItem = {
    title: '操作',
    key: 'operate',
    fixed: 'right',
    width: 160,
    cell: ({ record }) => (
      <span>
        <PermissionAction code={RULE_CONFIG.SALARY_RULE.LIST.VIEW} permissions={record.permissions}>
          <Button
            type="link"
            size="small"
            style={{
              paddingLeft: 0,
            }}
            onClick={() => {
              dhrReport.trace(REPORT_EVENT_TYPE.SALARY_RULE_VIEW, record)
              navigate(
                `${baseRoute}/contract/basic-config/rule-config/detail?type=salaryRule&id=${record.id}`,
              )
            }}
          >
            查看
          </Button>
        </PermissionAction>
        <PermissionAction code={RULE_CONFIG.SALARY_RULE.LIST.EDIT} permissions={record.permissions}>
          <Button
            type="link"
            size="small"
            onClick={() => {
              dhrReport.trace(REPORT_EVENT_TYPE.SALARY_RULE_EDIT, record)
              navigate(
                `${baseRoute}/contract/basic-config/rule-config/edit?type=salaryRule&id=${record.id}`,
              )
            }}
          >
            编辑
          </Button>
        </PermissionAction>
        {getActions(record).map((c) => (
          <PermissionAction key={c.code} code={c.code} permissions={record.permissions}>
            <Button type="link" size="small" onClick={() => operateActions(c.code, record)}>
              {c.component}
            </Button>
          </PermissionAction>
        ))}
        <PermissionAction
          code={RULE_CONFIG.SALARY_RULE.LIST.DELETE}
          permissions={record.permissions}
        >
          <Delete
            onDelete={onDelete}
            record={record}
            content="删除后不可恢复，确定要删除规则吗？"
            buttonProps={{
              onClick: () => dhrReport.trace(REPORT_EVENT_TYPE.SALARY_RULE_DELETE, record),
            }}
          />
        </PermissionAction>
      </span>
    ),
  }

  const columns: SchemaColumnType = useMemo(
    () => getSalaryRulesColumns().columns.concat(operateColumns),
    [],
  )

  const requestApi = ({ filter, pagination, sorter }) => {
    const { current, pageSize } = pagination
    const { name, ruleNo, status, effectiveDate, disableDate, ...rest } = filter
    const params = {
      page: current,
      size: pageSize,
      name: stringTransform(name),
      ruleNo: stringTransform(ruleNo),
      status: status === 'all' ? undefined : status,
      effectiveStartTime: effectiveDate?.[0]?.startOf('day').valueOf(),
      effectiveEndTime: effectiveDate?.[1]?.endOf('day').valueOf(),
      disableStartTime: disableDate?.[0]?.startOf('day').valueOf(),
      disableEndTime: disableDate?.[1]?.endOf('day').valueOf(),
      sortField: Object.keys(sorter)?.[0],
      sortType: sorter.salary?.slice(0, -3),
      ...rest,
    }
    return getSalaryRules(params)
      .then(({ data: resData }) => {
        if (resData.data) {
          return {
            success: true,
            data: resData.data,
            total: resData.count,
          }
        }
        return {
          rows: [],
          total: 0,
        }
      })
      .catch(() => ({
        rows: [],
        total: 0,
      }))
  }

  return (
    <div
      style={{
        margin: '0px 24px',
        background: 'white',
      }}
    >
      <SchemaTable
        action={action}
        columns={columns}
        searchColumns={getSalaryRulesColumns().searchColumns}
        request={requestApi}
        searchProps={{
          initialValues: {
            status: 1,
          },
        }}
      />
    </div>
  )
}

export default TypeConfig
