import { Modal, message } from 'antd'
import modalWrapperHoc from '@/components/modalWrapperHoc'
import {
  copyTypeRule,
  disabledTypeRule,
  enabledTypeRule,
  deleteTypeRule,
} from '@/api/basicConfig/type-rule'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import { RULE_CONFIG } from '@/constants/rbac-code'
import CopyModal from '../components/CopyModal'
import EndModal from '../components/EndModal'

// 复制类型规则
export const copyTypeRuleFn = (id: number, successCallback?: () => void) => {
  modalWrapperHoc(CopyModal)({
    type: 'typeRule',
    onOk: (values: any) => {
      const { name, effectiveDate } = values
      const params = {
        name,
        effectiveDate: effectiveDate.valueOf(),
      }
      return new Promise((resolve, reject) => {
        copyTypeRule(id, params)
          .then(() => {
            successCallback?.()
            message.success('复制成功')
            resolve(true)
          })
          .catch(() => {
            reject()
          })
      })
    },
  })
}

// 停用类型规则
export const endTypeRuleFn = async (
  record: any,
  successCallback?: (actionName?: string, data?: any) => void,
) => {
  // 需要先调用详情接口判断是否有关联合同包
  if (record.contractPackageNums) {
    // 有合同包使用规则
    modalWrapperHoc(EndModal)({
      record,
      type: 'type',
      onOk: () => {
        successCallback?.('redirect', record.contractPackages.map((item) => item.number).join(','))
      },
    })
  } else {
    // 无合同包使用规则
    Modal.confirm({
      title: '停用',
      content: '停用后将不能被引用，确定要停用规则吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        const params = {
          disabledId: record.id,
          version: record.version,
        }
        return new Promise((resolve, reject) => {
          disabledTypeRule(params)
            .then(() => {
              successCallback?.()
              message.success('停用成功')
              resolve(true)
            })
            .catch(() => {
              reject()
            })
        })
      },
    })
  }
}

// 启用类型规则
export const startTypeRuleFn = (
  record: { name: string; id: number; version: number; [field: string]: any },
  successCallback?: () => void,
) => {
  Modal.confirm({
    title: '启用',
    content: `确定要启用【${record.name}】吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: () =>
      new Promise((resolve, reject) => {
        enabledTypeRule(record.id, record.version)
          .then(() => {
            successCallback?.()
            message.success('启用成功')
            resolve(true)
          })
          .catch(() => {
            reject()
          })
      }),
  })
}

// 删除类型规则
export const deleteTypeRuleFn = (id: number, successCallback?: () => void) => {
  Modal.confirm({
    title: '删除',
    content: '删除后不可恢复，确定要删除规则吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () =>
      new Promise((resolve, reject) => {
        deleteTypeRule(id)
          .then(() => {
            successCallback?.()
            message.success('删除成功')
            resolve(true)
          })
          .catch(() => {
            reject()
          })
      }),
  })
}

// 类型规则的操作项
export const operateTypeRule = (
  key: string,
  record: { name: string; id: number; version: number; [field: string]: any },
  successCallback?: () => void,
) => {
  // 复制
  if (key === RULE_CONFIG.TYPE_RULE.LIST.COPY) {
    dhrReport.trace(REPORT_EVENT_TYPE.TYPE_RULE_COPY, record)
    copyTypeRuleFn(record.id, successCallback)
  }
  // 停用
  if (key === RULE_CONFIG.TYPE_RULE.LIST.DISABLED) {
    dhrReport.trace(REPORT_EVENT_TYPE.TYPE_RULE_DEACTIVATE, record)
    endTypeRuleFn(record, successCallback)
  }
  // 启用
  if (key === RULE_CONFIG.TYPE_RULE.LIST.ENABLED) {
    dhrReport.trace(REPORT_EVENT_TYPE.TYPE_RULE_ENABLE, record)
    startTypeRuleFn(record, successCallback)
  }
  if (key === RULE_CONFIG.TYPE_RULE.LIST.DELETE) {
    dhrReport.trace(REPORT_EVENT_TYPE.TYPE_RULE_DELETE, record)
    deleteTypeRuleFn(record.id, successCallback)
  }
}
