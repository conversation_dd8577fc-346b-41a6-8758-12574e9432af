import { Modal, message } from 'antd'
import modalWrapperHoc from '@/components/modalWrapperHoc'
import {
  copyDeadlineRule,
  disabledDeadlineRule,
  enabledDeadlineRule,
  deleteDeadlineRule,
} from '@/api/basicConfig/deadline-rule'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import { RULE_CONFIG } from '@/constants/rbac-code'
import CopyModal from '../components/CopyModal'
import EndModal from '../components/EndModal'

// 复制类型规则
export const copyDeadlineRuleFn = (id: number, successCallback?: () => void) => {
  modalWrapperHoc(CopyModal)({
    type: 'deadlineRule',
    onOk: (values: any) => {
      const { name, effectiveDate } = values
      const params = {
        name,
        effectiveDate: effectiveDate.valueOf(),
      }
      return new Promise((resolve, reject) => {
        copyDeadlineRule(id, params)
          .then(() => {
            successCallback?.()
            message.success('复制成功')
            resolve(true)
          })
          .catch(() => {
            reject()
          })
      })
    },
  })
}

// 停用类型规则
export const endDeadlineRuleFn = async (
  record: any,
  successCallback?: (actionName?: string, data?: any) => void,
) => {
  if (record.contractPackageNums) {
    // 有合同包使用规则
    modalWrapperHoc(EndModal)({
      record,
      type: 'type',
      onOk: () => {
        successCallback?.('redirect', record.contractPackages.map((item) => item.number).join(','))
      },
    })
  } else {
    // 无合同包使用规则
    Modal.confirm({
      title: '停用',
      content: '停用后将不能被引用，确定要停用规则吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        const params = {
          disabledId: record.id,
          version: record.version,
        }
        return new Promise((resolve, reject) => {
          disabledDeadlineRule(params)
            .then(() => {
              successCallback?.()
              message.success('停用成功')
              resolve(true)
            })
            .catch(() => {
              reject()
            })
        })
      },
    })
  }
}

// 启用类型规则
export const startDeadlineRuleFn = (
  record: { name: string; id: number; version: number; [field: string]: any },
  successCallback?: () => void,
) => {
  Modal.confirm({
    title: '启用',
    content: `确定要启用【${record.name}】吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: () =>
      new Promise((resolve, reject) => {
        enabledDeadlineRule(record.id, record.version)
          .then(() => {
            successCallback?.()
            message.success('启用成功')
            resolve(true)
          })
          .catch(() => {
            reject()
          })
      }),
  })
}

// 删除类型规则
export const deleteDeadlineRuleFn = (id: number, successCallback?: () => void) => {
  Modal.confirm({
    title: '删除',
    content: '删除后不可恢复，确定要删除规则吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () =>
      new Promise((resolve, reject) => {
        deleteDeadlineRule(id)
          .then(() => {
            successCallback?.()
            message.success('删除成功')
            resolve(true)
          })
          .catch(() => {
            reject()
          })
      }),
  })
}

// 类型规则的操作项
export const operateDeadlineRule = (
  key: string,
  record: {
    name: string
    id: number
    version: number
    [field: string]: any
  },
  successCallback?: (actionName?: string, data?: any) => void,
) => {
  // 复制
  if (key === RULE_CONFIG.DEADLINE_RULE.LIST.COPY) {
    dhrReport.trace(REPORT_EVENT_TYPE.DEADLINE_RULE_COPY, record)
    copyDeadlineRuleFn(record.id, successCallback)
  }
  // 停用
  if (key === RULE_CONFIG.DEADLINE_RULE.LIST.DISABLED) {
    dhrReport.trace(REPORT_EVENT_TYPE.DEADLINE_RULE_DEACTIVATE, record)
    endDeadlineRuleFn(record, successCallback)
  }
  // 启用
  if (key === RULE_CONFIG.DEADLINE_RULE.LIST.ENABLED) {
    dhrReport.trace(REPORT_EVENT_TYPE.DEADLINE_RULE_ENABLE, record)
    startDeadlineRuleFn(record, successCallback)
  }
  if (key === RULE_CONFIG.DEADLINE_RULE.LIST.DELETE) {
    dhrReport.trace(REPORT_EVENT_TYPE.DEADLINE_RULE_DELETE, record)
    deleteDeadlineRuleFn(record.id, successCallback)
  }
}
