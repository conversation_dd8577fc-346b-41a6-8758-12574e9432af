import { Modal, message } from 'antd'
import modalWrapperHoc from '@/components/modalWrapperHoc'
import {
  copySalaryRule,
  enableSalaryRule,
  disableSalaryRule,
  deleteSalaryRule,
} from '@/api/basicConfig/salary-rule'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import { RULE_CONFIG } from '@/constants/rbac-code'
import CopyModal from '../components/CopyModal'

// 复制工资规则
export const copySalaryRuleFn = (id: number, successCallback?: () => void) => {
  modalWrapperHoc(CopyModal)({
    type: 'salaryRule',
    onOk: (values: any) => {
      const { name, effectiveDate } = values
      const params = {
        name,
        effectiveDate: effectiveDate.valueOf(),
      }
      return new Promise((resolve, reject) => {
        copySalaryRule(id, params)
          .then(() => {
            successCallback?.()
            message.success('复制成功')
            resolve(true)
          })
          .catch(() => {
            reject()
          })
      })
    },
  })
}

// 停用工资规则
export const endSalaryRuleFn = (record, successCallback?: () => void) => {
  Modal.confirm({
    title: '停用',
    content: '停用后将不能被引用，确定要停用规则吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () =>
      new Promise((resolve, reject) => {
        disableSalaryRule(record.id, record.version)
          .then(() => {
            successCallback?.()
            message.success('停用成功')
            resolve(true)
          })
          .catch(() => {
            reject()
          })
      }),
  })
}

// 启用工资规则
export const startSalaryRuleFn = (record, successCallback?: () => void) => {
  Modal.confirm({
    title: '启用',
    content: `确定要启用【${record.name}】吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: () =>
      new Promise((resolve, reject) => {
        enableSalaryRule(record.id, record.version)
          .then(() => {
            successCallback?.()
            message.success('启用成功')
            resolve(true)
          })
          .catch(() => {
            reject()
          })
      }),
  })
}

// 删除类型规则
export const deleteSalaryRuleFn = (id: number, successCallback?: () => void) => {
  Modal.confirm({
    title: '删除',
    content: '删除后不可恢复，确定要删除规则吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () =>
      new Promise((resolve, reject) => {
        deleteSalaryRule(id)
          .then(() => {
            successCallback?.()
            message.success('删除成功')
            resolve(true)
          })
          .catch(() => {
            reject()
          })
      }),
  })
}

// 类型规则的操作项
export const operateSalaryRule = (
  key: string,
  record: { name: string; id: number; version: number; [field: string]: any },
  successCallback?: () => void,
) => {
  // 复制
  if (key === 'copy') {
    dhrReport.trace(REPORT_EVENT_TYPE.SALARY_RULE_COPY, record)
    copySalaryRuleFn(record.id, successCallback)
  }
  // 停用
  if (key === RULE_CONFIG.SALARY_RULE.LIST.DISABLED) {
    dhrReport.trace(REPORT_EVENT_TYPE.SALARY_RULE_DEACTIVATE, record)
    endSalaryRuleFn(record, successCallback)
  }
  // 启用
  if (key === RULE_CONFIG.SALARY_RULE.LIST.ENABLED) {
    dhrReport.trace(REPORT_EVENT_TYPE.SALARY_RULE_ENABLE, record)
    startSalaryRuleFn(record, successCallback)
  }
  // 删除
  if (key === RULE_CONFIG.SALARY_RULE.LIST.DELETE) {
    dhrReport.trace(REPORT_EVENT_TYPE.SALARY_RULE_DELETE, record)
  }
}
