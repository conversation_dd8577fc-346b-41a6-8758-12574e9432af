import React from 'react'
import { Modal } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'

const EndModal = (props: any) => {
  const { visible, close, record, onOk, ...restProp } = props

  return (
    <Modal
      title={
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <ExclamationCircleOutlined
            style={{
              color: '#ffa229',
              marginRight: 16,
              fontSize: '22px',
            }}
          />
          停用
        </div>
      }
      visible={visible}
      onCancel={close}
      width={650}
      cancelText="关闭"
      okText="前往操作"
      {...restProp}
      onOk={() => {
        close?.()
        onOk?.()
      }}
    >
      <div
        style={{
          marginBottom: 8,
        }}
      >
        {`当前有${record.contractPackageNums}个合同包正在使用该规则，不能直接停用。在合同包将该规则移除后，可正常停用！`}
      </div>
      <div>{`关联的合同包：${record.contractPackages.map((i) => i.name).join('、')}`}</div>
    </Modal>
  )
}

export default EndModal
