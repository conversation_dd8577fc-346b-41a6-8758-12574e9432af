import React, { useState } from 'react'
import { Modal } from 'antd'
import moment from 'moment'
import { SchemaForm, Item, SchemaType } from '@amazebird/antd-schema-form'
import { getNameExists as typeNameExists } from '@/api/basicConfig/type-rule'
import { getNameExists as deadlineNameExists } from '@/api/basicConfig/deadline-rule'
import { getNameExists as salaryNameExists } from '@/api/basicConfig/salary-rule'

const apiList = [
  {
    type: 'typeRule',
    api: typeNameExists,
  },
  {
    type: 'deadlineRule',
    api: deadlineNameExists,
  },
  {
    type: 'salaryRule',
    api: salaryNameExists,
  },
]

const CopyModal = (props: any) => {
  const { visible, close, onOk: okClick, type } = props
  const form = SchemaForm.createForm()
  const [loading, setLoading] = useState(false)

  // 名称校验
  // eslint-disable-next-line consistent-return
  const nameValidator = async (_, value) => {
    if (value) {
      const params = {
        name: value,
      }
      return apiList
        .find((i) => i.type === type)
        ?.api(params)
        .then(({ data: result }) => {
          if (result.data) {
            return Promise.reject(new Error('规则名称重复，请重新命名'))
          }
          return Promise.resolve()
        })
    }
  }

  const schema: SchemaType = {
    name: {
      label: '规则名称',
      component: 'Input',
      required: true,
      max: 50,
      rules: [
        {
          validator: nameValidator,
        },
      ],
    },
    effectiveDate: {
      label: '生效日期',
      component: 'DatePicker',
      rules: [
        {
          required: true,
          message: '请选择生效日期',
        },
      ],
      props: {
        style: {
          width: '100%',
        },
        disabledDate: (current) => current && current < moment().startOf('day'),
      },
    },
  }

  const onOk = async () => {
    try {
      const values = await form?.validateFields()
      const { effectiveDate, ...rest } = values
      setLoading(true)
      okClick({
        effectiveDate: effectiveDate.startOf('day'),
        ...rest,
      })
        .then(() => {
          close()
        })
        .finally(() => {
          setLoading(false)
        })
    } catch (error) {
      console.error(error)
    }
  }

  return (
    <Modal
      title="复制"
      visible={visible}
      onCancel={close}
      onOk={onOk}
      width={650}
      okButtonProps={{
        loading,
      }}
    >
      <SchemaForm form={form} schema={schema}>
        <Item field="name" />
        <Item field="effectiveDate" />
      </SchemaForm>
    </Modal>
  )
}

export default CopyModal
