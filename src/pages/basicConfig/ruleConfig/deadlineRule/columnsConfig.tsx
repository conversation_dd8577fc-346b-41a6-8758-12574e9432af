import React from 'react'
import type { SchemaColumnType } from '@amazebird/antd-schema-table'
import { RedirectToPackagePage } from '@/pages/basicConfig/contractPackage/common'
import { STATUS } from '@/constants'

const getDeadlineRulesColumns = () => {
  const columns: SchemaColumnType = [
    {
      title: '规则名称',
      dataIndex: 'name',
      cell: {
        type: 'Text',
        props: {
          ellipsis: true,
        },
      },
      fixed: 'left',
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'status',
      cell: 'Status',
      options: STATUS,
    },
    {
      title: '规则编号',
      dataIndex: 'ruleNo',
      cell: 'Text',
    },
    {
      title: '生效日期',
      dataIndex: 'effectiveDate',
      cell: 'Date',
    },
    {
      title: '停用日期',
      dataIndex: 'disableDate',
      cell: 'Date',
    },
    {
      title: '创建时间',
      dataIndex: 'timeCreate',
      cell: 'DateTime',
    },
    {
      title: '创建人',
      dataIndex: 'userIdCreate',
      cell: ({ text, record }) => `${record.userNameCreate}（${text}）`,
      width: 150,
    },
  ]

  const searchColumns = [
    {
      title: '规则名称',
      dataIndex: 'name',
      component: 'Input',
      props: {
        allowClear: true,
      },
    },
    {
      title: '规则编号',
      dataIndex: 'ruleNo',
      component: 'Input',
      props: {
        allowClear: true,
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      component: 'Select',
      options: [
        {
          value: 'all',
          label: '全部',
        },
        ...STATUS,
      ],
    },
    {
      title: '生效日期',
      dataIndex: 'effectiveDate',
      component: 'RangePicker',
    },
    {
      title: '停用日期',
      dataIndex: 'disableDate',
      component: 'RangePicker',
    },
  ]

  return {
    columns,
    searchColumns,
  }
}

export default getDeadlineRulesColumns
