import React, { useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { SchemaTable, createAction, Delete } from '@amazebird/antd-schema-table'
import type { SchemaColumnType } from '@amazebird/antd-schema-table'
import { Button } from 'antd'
import config from '@/config'
import { getDeadlineRule, deleteDeadlineRule } from '@/api/basicConfig/deadline-rule'
import PermissionAction from '@/components/permissionAction'
import { RULE_CONFIG } from '@/constants/rbac-code'
import { stringTransform } from '@/utils/utils'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import { operateDeadlineRule } from '../actions/deadlineRuleActions'
import getDeadlineRulesColumns from './columnsConfig'

type ColumnsItem = SchemaColumnType[number]

const { baseRoute } = config

const getActions = (record) => [
  {
    code: RULE_CONFIG.DEADLINE_RULE.LIST.COPY,
    component: '复制',
  },
  record.status === 0
    ? {
        code: RULE_CONFIG.DEADLINE_RULE.LIST.ENABLED,
        component: '启用',
      }
    : {
        code: RULE_CONFIG.DEADLINE_RULE.LIST.DISABLED,
        component: '停用',
      },
]

const DeadlineRule = () => {
  const navigate = useNavigate()
  const action = createAction()
  const operateActions = (key, record) => {
    operateDeadlineRule(key, record, (actionName?: string, data?: any) => {
      if (actionName === 'redirect') {
        navigate(`${baseRoute}/contract/basic-config/contract-package?codes=${data}`)
      } else {
        action.refresh()
      }
    })
  }

  const operateColumns: ColumnsItem = {
    title: '操作',
    key: 'operate',
    fixed: 'right',
    width: 190,
    cell: ({ record }) => (
      <span>
        <PermissionAction
          code={RULE_CONFIG.DEADLINE_RULE.LIST.VIEW}
          permissions={record.permissions}
        >
          <Button
            type="link"
            size="small"
            style={{
              paddingLeft: 0,
            }}
            onClick={() => {
              dhrReport.trace(REPORT_EVENT_TYPE.DEADLINE_RULE_VIEW, record)
              navigate(
                `${baseRoute}/contract/basic-config/rule-config/detail?type=deadlineRule&id=${record.id}`,
              )
            }}
          >
            查看
          </Button>
        </PermissionAction>
        <PermissionAction
          code={RULE_CONFIG.DEADLINE_RULE.LIST.EDIT}
          permissions={record.permissions}
        >
          <Button
            type="link"
            size="small"
            onClick={() => {
              dhrReport.trace(REPORT_EVENT_TYPE.DEADLINE_RULE_EDIT, record)
              navigate(
                `${baseRoute}/contract/basic-config/rule-config/edit?type=deadlineRule&id=${record.id}`,
              )
            }}
          >
            编辑
          </Button>
        </PermissionAction>
        {getActions(record).map((c) => (
          <PermissionAction key={c.code} code={c.code} permissions={record.permissions}>
            <Button type="link" size="small" onClick={() => operateActions(c.code, record)}>
              {c.component}
            </Button>
          </PermissionAction>
        ))}
        <PermissionAction
          code={RULE_CONFIG.DEADLINE_RULE.LIST.DELETE}
          permissions={record.permissions}
        >
          <Delete
            onDelete={() => deleteDeadlineRule(record.id)}
            record={record}
            content="删除后不可恢复，确定要删除规则吗？"
            buttonProps={{
              onClick: () => dhrReport.trace(REPORT_EVENT_TYPE.DEADLINE_RULE_DELETE, record),
            }}
          />
        </PermissionAction>
      </span>
    ),
  }

  const columns: SchemaColumnType = useMemo(
    () => getDeadlineRulesColumns().columns.concat(operateColumns),
    [],
  )

  const requestApi = ({ filter, pagination }) => {
    const { current, pageSize } = pagination
    const { name, ruleNo, status, effectiveDate, disableDate, classification } = filter
    const params: any = {
      page: current,
      size: pageSize,
      name: stringTransform(name),
      ruleNo: stringTransform(ruleNo),
      status: status === 'all' ? undefined : status,
      effectiveStartTime: effectiveDate?.[0]?.startOf('day').valueOf(),
      effectiveEndTime: effectiveDate?.[1]?.endOf('day').valueOf(),
      disableStartTime: disableDate?.[0]?.startOf('day').valueOf(),
      disableEndTime: disableDate?.[1]?.endOf('day').valueOf(),
    }
    if (classification) {
      if (classification.indexOf(0) !== -1) {
        params.isSupportInner = true
      }
      if (classification.indexOf(1) !== -1) {
        params.isSupportOuter = true
      }
    }
    return getDeadlineRule(params)
      .then(({ data: resData }) => {
        if (resData.data) {
          return {
            success: true,
            data: resData.data,
            total: resData.count,
          }
        }
        return {
          rows: [],
          total: 0,
        }
      })
      .catch(() => ({
        rows: [],
        total: 0,
      }))
  }

  return (
    <div
      style={{
        margin: '0px 24px',
        background: 'white',
      }}
    >
      <SchemaTable
        columns={columns}
        request={requestApi}
        action={action}
        searchColumns={getDeadlineRulesColumns().searchColumns}
        searchProps={{
          initialValues: {
            status: 1,
          },
        }}
      />
    </div>
  )
}

export default DeadlineRule
