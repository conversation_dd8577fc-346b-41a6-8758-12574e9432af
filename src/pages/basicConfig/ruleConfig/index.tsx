import React, { useState, useEffect } from 'react'
import { useStore } from '@/stores'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'antd'
import { getPermission } from '@galaxy/rbac'
import config from '@/config'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import PermissionAction from '@/components/permissionAction'
import { RULE_CONFIG } from '@/constants/rbac-code'
import { transformStringType } from '@/utils/utils'
import useCreateStore from '@/stores/useCreateStore'
import ParamsRule from './paramsRule'
import TypeRule from './typeRule'
import DeadlineRule from './deadlineRule'
import SalaryRule from './salaryRule'

import styles from './styles.module.less'

const { baseRoute } = config

const RuleConfig = () => {
  const store = useStore((state) => state)
  const { setTitle, setExtra } = store

  const navigate = useNavigate()
  const [search, setSearch] = useSearchParams()
  const [activeKey, setActiveKey] = useState<any>(search.get('activeKey') || 'paramsRule')
  const permissions = useCreateStore((state: any) => state.permissions)

  const items = [
    {
      key: 'paramsRule',
      label: '参数规则',
      code: RULE_CONFIG.TABS.PARAMS_RULE,
      children: <ParamsRule />,
    },
    /** V1.5.0 屏蔽配置 */
    // {
    //   key: 'typeRule',
    //   label: '合同类型规则',
    //   eventCode: REPORT_EVENT_TYPE.TYPE_RULE_CREATE,
    //   code: RULE_CONFIG.TABS.TYPE_RULE,
    //   children: <TypeRule />,
    // },
    // {
    //   key: 'deadlineRule',
    //   label: '合同期限规则',
    //   eventCode: REPORT_EVENT_TYPE.DEADLINE_RULE_CREATE,
    //   code: RULE_CONFIG.TABS.DEADLINE_RULE,
    //   children: <DeadlineRule />,
    // },
    {
      key: 'salaryRule',
      label: '签订工资规则',
      eventCode: REPORT_EVENT_TYPE.SALARY_RULE_CREATE,
      code: RULE_CONFIG.TABS.SALARY_RULE,
      children: <SalaryRule />,
    },
  ]

  const onChange = (key) => {
    setActiveKey(key)
    setSearch((prev) => {
      prev.set('activeKey', key)
      return prev
    })
  }

  useEffect(() => {
    const tab = items.find((i) => i.key === activeKey)
    setTitle(<span>{tab?.label}</span>)

    if (activeKey !== 'paramsRule') {
      const code = RULE_CONFIG[transformStringType(activeKey)].CREATE
      setExtra(
        <PermissionAction code={code} permissions={permissions}>
          <Button
            type="primary"
            onClick={() => {
              if (tab?.eventCode) {
                dhrReport.trace(tab?.eventCode, {
                  tabKey: activeKey,
                  tabName: tab?.label,
                })
              }
              navigate(`${baseRoute}/contract/basic-config/rule-config/create?type=${activeKey}`)
            }}
          >
            新增规则
          </Button>
        </PermissionAction>,
      )
    }
    return () => {
      setTitle(null)
      if (activeKey !== 'paramsRule') {
        setExtra(null)
      }
    }
  }, [activeKey, permissions])

  return (
    <div className={styles.rulesConfig}>
      <Tabs
        defaultActiveKey="paramsRule"
        items={items.filter((i) => getPermission(i.code))}
        onChange={onChange}
        activeKey={activeKey}
      />
    </div>
  )
}

export default RuleConfig
