import React from 'react'
import { Button } from 'antd'
import { RollbackOutlined } from '@ant-design/icons'
import PermissionAction from '@/components/permissionAction'
import '@amazebird/antd-field'
import config from '@/config'
import { useNavigate } from 'react-router-dom'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import { CONTRACT_PACKAGE } from '@/constants/rbac-code'
// 样式
import styles from '@/pages/basicConfig/ruleConfig/detail/style.module.less'
// 组件
import { operateActions, operates } from '../common'
import { MoreAction } from '../columnsConfig'

const { baseRoute } = config

function Extra({
  values,
  callback,
}: {
  values: { id: number; name: string; status: number; [field: string]: any }
  callback?: (name: string) => void
}) {
  const navigate = useNavigate()
  return (
    <div className={styles.extra}>
      {window.__POWERED_BY_QIANKUN__ && (
        <span
          className={styles.back}
          onClick={() => {
            navigate(-1)
          }}
        >
          <RollbackOutlined className={styles.icon} />
          返回
        </span>
      )}
      <PermissionAction code={CONTRACT_PACKAGE.LIST.EDIT} permissions={values.permissions}>
        <Button
          onClick={() => {
            dhrReport.trace(REPORT_EVENT_TYPE.PACKAGE_EDIT, values)
            navigate(`${baseRoute}/contract/basic-config/contract-package/edit?id=${values.id}`)
          }}
        >
          编辑
        </Button>
      </PermissionAction>
      <MoreAction
        actions={operates(values.status, true)}
        showIcon={false}
        onClick={({ key }) =>
          operateActions(key, values, (actionName) => {
            if (actionName === CONTRACT_PACKAGE.LIST.DELETE) {
              // 删除成功后返回列表
              navigate(-1)
            }
            callback?.(actionName)
          })
        }
        permissions={values.permissions}
        isDetailPage
      />
    </div>
  )
}

export default Extra
