.packageContainer {
  background-color: white;
  padding: 24px;
  margin: 24px 24px 48px 24px;

  .toValidator {
    display: block;
    font-size: 12px;
    cursor: pointer;
    color: #1abb9c;
    display: flex;
    align-items: center;

    .icon {
      color: rgba(0, 0, 0, 0.85);
      margin-left: 5px;
    }
  }

  .empty {
    margin: 40px;
  }

  :global {
    .schema-form-text-area-detail {
      word-break: break-all;
    }
  }
}

.selectWrapper {
  position: relative;
  margin-bottom: 10px;

  :global {
    .@{ant-prefix}-form-item {
      margin-bottom: 0px;
    }

    .@{ant-prefix}-row {
      .@{ant-prefix}-form-item-control {
        max-width: unset;
      }
    }
  }

  .action {
    position: absolute;
    right: -50px;
    top: 4px;
    width: 40px;

    .addIcon {
      margin-right: 5px;
    }
  }
}

.viewTextWrapper {
  display: flex;
  align-items: baseline;

  .viewText {
    padding: 0;
    white-space: pre-wrap;
    text-align: left;
    color: #1abb9c;
    cursor: pointer;
  }
}
