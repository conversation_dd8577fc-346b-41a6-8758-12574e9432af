.templateModal {

  .tabBox {
    :global {
      .@{ant-prefix}-tabs-tab {
        padding-top: 0;
      }

      .@{ant-prefix}-tabs-tab-btn:focus {
        color: unset;
      }

      .@{ant-prefix}-tabs-tab.@{ant-prefix}-tabs-tab-active {
        color: #1abb9c;
      }
    }

  }

  .checkGroupBox {
    max-height: 320px;
    display: flex;
    flex-direction: column;
    overflow: auto;
    margin-top: 10px;

    :global {
      .@{ant-prefix}-checkbox-group-item {
        margin: 10px 0 5px 12px;
      }
    }
  }

  .rightBox {
    display: flex;
    width: 100%;
    height: 100%;

    .symbol {
      min-height: 100%;
      display: inline-flex;
      justify-content: center;
      align-items: center;
    }

    :global {
      .@{ant-prefix}-card-body {
        padding: 0;
      }
    }

  }

}