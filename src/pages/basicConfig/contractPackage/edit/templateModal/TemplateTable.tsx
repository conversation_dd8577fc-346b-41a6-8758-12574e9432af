import React, { useState, useEffect, useRef } from 'react'
import { SchemaTable } from '@amazebird/antd-schema-table'
import { useMemoizedFn } from 'ahooks'
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc'
import { Button } from 'antd'
import { MenuOutlined, StarFilled } from '@ant-design/icons'

interface IProps {
  selectList: any[]
  setSelectList: any
  sealLocateModeDict: { value: string; label: string }[]
  maxHeight?: number
  callback?: (actionName: 'remove' | 'setMain', params?: any) => void
}

export const getTemplateName = (isMain: boolean, name: string) => {
  return isMain ? (
    <span style={{ whiteSpace: 'nowrap' }}>
      <StarFilled style={{ color: '#ffa229' }} />
      {`（主合同）${name}`}
    </span>
  ) : (
    name || '--'
  )
}

export function arrayMoveMutable<ValueType>(
  array: ValueType[],
  fromIndex: number,
  toIndex: number,
) {
  const startIndex = fromIndex < 0 ? array.length + fromIndex : fromIndex

  if (startIndex >= 0 && startIndex < array.length) {
    const endIndex = toIndex < 0 ? array.length + toIndex : toIndex
    const [item] = array.splice(fromIndex, 1)
    array.splice(endIndex, 0, item)
  }
}

export function arrayMoveImmutable<T>(array: T[], fromIndex: number, toIndex: number) {
  const newArray = [...array]
  arrayMoveMutable(newArray, fromIndex, toIndex)
  return newArray
}

const DragHandle = SortableHandle(() => <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />)

function TemplateTable({
  selectList,
  setSelectList,
  sealLocateModeDict,
  maxHeight,
  callback,
}: IProps) {
  const [dict, setDict] = useState<Record<string, string>>({})
  const tableRef = useRef<any>(null)
  const [dataSource, setDataSource] = useState(selectList)
  const SortableItem = SortableElement((props) => <tr {...props} />)
  const SortContainer = SortableContainer((props) => <tbody {...props} />)

  useEffect(() => {
    setDataSource(selectList)
  }, [selectList])

  const onSortEnd = useMemoizedFn(
    ({ oldIndex, newIndex }: { oldIndex: number; newIndex: number }) => {
      if (oldIndex !== newIndex) {
        const newData = arrayMoveImmutable([...dataSource], oldIndex, newIndex)
          .filter((el) => !!el)
          .map((item, i) => {
            if (i === 0) {
              return {
                ...item,
                isMain: true,
              }
            }
            return {
              ...item,
              isMain: false,
            }
          })
        setSelectList([...newData])
      }
    },
  )

  const DraggableContainer = (props: any) => (
    <SortContainer
      helperContainer={() => {
        return tableRef.current.querySelector('table tbody')
      }}
      useDragHandle
      disableAutoscroll
      helperClass="custom-row-dragging"
      onSortEnd={onSortEnd}
      {...props}
    />
  )

  const DraggableBodyRow = (props: any) => {
    const { className, style, ...restProps } = props
    const index = dataSource.findIndex((x) => {
      return x.index === restProps['data-row-key']
    })
    return <SortableItem index={index} {...restProps} />
  }

  const columns: any = [
    {
      title: '排序',
      width: 50,
      cell: () => <DragHandle />,
      fixed: 'left',
    },
    {
      dataIndex: 'name',
      title: '模板名称',
      fixed: 'left',
      width: 150,
      cell: {
        type: 'Text',
        props: {
          ellipsis: true,
        },
        render: ({ record }, dom) => {
          return record?.isMain ? (
            <div style={{ display: 'flex' }}>
              {getTemplateName(record?.isMain, '')}
              {dom}
            </div>
          ) : (
            dom
          )
        },
      },
    },
    {
      dataIndex: 'sealLocateModeCode',
      title: '印章定位方式',
      width: 100,
      cell: {
        render: ({ text }) => {
          return dict[text] || '--'
        },
      },
    },
    {
      dataIndex: 'enterpriseKeyWord',
      title: '企业关键字',
    },
    {
      dataIndex: 'personKeyWord',
      title: '员工关键字',
    },
    {
      title: '操作',
      fixed: 'right',
      width: 150,
      cell: {
        render: ({ record }) => (
          <>
            <Button
              type="link"
              size="small"
              danger
              onClick={() => {
                callback?.('remove', record)
              }}
            >
              移除
            </Button>
            <Button
              type="link"
              size="small"
              disabled={record?.isMain}
              onClick={() => {
                callback?.('setMain', record)
              }}
            >
              设为主合同
            </Button>
          </>
        ),
      },
    },
  ]

  useEffect(() => {
    if (sealLocateModeDict.length) {
      const dicts = sealLocateModeDict.reduce((cur, item) => {
        // eslint-disable-next-line no-param-reassign
        cur[item.value] = item.label
        return cur
      }, {})
      setDict(dicts)
    }
  }, [sealLocateModeDict])

  return (
    <SchemaTable
      ref={tableRef}
      columns={columns}
      rowKey="index"
      dataSource={dataSource}
      scroll={{ y: maxHeight }}
      components={{
        body: {
          wrapper: DraggableContainer,
          row: DraggableBodyRow,
        },
      }}
    />
  )
}

export default TemplateTable
