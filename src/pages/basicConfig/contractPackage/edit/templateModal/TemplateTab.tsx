import React, { useState, useEffect, useRef } from 'react'

import { Checkbox, Empty, Input, Spin, Tabs, TabsProps, Modal } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { dictLocateMode } from '@/api/dict'
import { getTemplateInfo, getTemplateInfoList } from '@/api/basicConfig/template'
import { TableItem } from '@/types/package'
import styles from './index.module.less'

type TabType = {
  selectList?: TableItem[]
  callback?: (actionName: '__dictLoaded' | string, search: string, params?: any) => void
  relatedValue?: any
  isShowSealKeyWord2?: boolean
  onTabsChange: () => void
} & Omit<TabsProps, 'items' | 'onChange'>

type CheckItemType = { id: number; name: string; code: string }[]

type TemplateTabType = Record<
  string,
  {
    isRequest: boolean
    searchValue?: string
    list: TableItem[]
  }
>

function TemplateTab({
  callback,
  selectList,
  onTabsChange,
  relatedValue,
  isShowSealKeyWord2,
  ...restProps
}: TabType) {
  const [loading, setLoading] = useState(false)
  const [activeKey, setActiveKey] = useState<string>('')
  const [items, setItems] = useState<TabsProps['items']>([])
  const [templateListMap, setTemplateListMap] = useState<TemplateTabType>({})
  const groupChecked = useRef<Record<string, number[]>>({})

  const TemplateTree = ({ map }: { map?: TemplateTabType }) => {
    const searchValue = map?.[activeKey]?.searchValue || ''
    const list = map?.[activeKey]?.list
    const options =
      list?.filter((item) => {
        return (item.label as any)?.includes(searchValue)
      }) || []
    return (
      <>
        <Input
          placeholder="搜索模板名称"
          defaultValue={searchValue}
          onCompositionEnd={(e: any) => {
            const result = { ...templateListMap }
            if (result[activeKey] !== undefined) {
              result[activeKey].searchValue = e.target.value
              setTemplateListMap(result)
            }
          }}
          onChange={(e) => {
            // 只有当不在输入法组合状态时才更新搜索
            if (!e.nativeEvent.isComposing) {
              const result = { ...templateListMap }
              if (result[activeKey] !== undefined) {
                result[activeKey].searchValue = e.target.value
                setTemplateListMap(result)
              }
            }
          }}
          allowClear
        />
        {list !== undefined && options.length !== 0 ? (
          <Checkbox.Group
            className={styles.checkGroupBox}
            value={groupChecked.current[activeKey]}
            key={activeKey}
            options={options}
            onChange={(val) => {
              callback?.(
                activeKey,
                searchValue,
                val?.length ? map?.[activeKey]?.list?.filter((item) => val?.includes(item.id)) : [],
              )
            }}
          />
        ) : (
          <Empty style={{ marginTop: '24px' }} />
        )}
      </>
    )
  }

  // 请求失败展示无数据，公共方法
  const showTabItems = ({ map }: { map?: TemplateTabType }) => {
    const newItems = items?.map((item) => {
      if (item.key === activeKey) {
        return {
          ...item,
          children: TemplateTree({ map }),
        }
      }
      return item
    })
    setItems(newItems)
  }

  useEffect(() => {
    if (Array.isArray(selectList) && Object.keys(templateListMap).length) {
      const map: Record<string, number[]> = {}
      selectList?.forEach((item) => {
        if (map[item.sealLocateModeCode] === undefined) {
          map[item.sealLocateModeCode] = [item.id]
        } else {
          map[item.sealLocateModeCode].push(item.id)
        }
      })
      groupChecked.current = map
      showTabItems({ map: templateListMap })
    }
  }, [selectList, templateListMap])

  useEffect(() => {
    // 获取印章定位方式字典选项
    setLoading(true)
    dictLocateMode()
      .then(({ LOCATE_MODE = [] }: { LOCATE_MODE?: CheckItemType }) => {
        if (Array.isArray(LOCATE_MODE) && LOCATE_MODE.length > 0) {
          const options = LOCATE_MODE.sort((prev, next) => next.id - prev.id)
          callback?.('__dictLoaded', '', options)
          let code = options[0].code
          // 是否为同一个分组
          if (Array.isArray(selectList)) {
            // 如果都不是第一个tab
            if (selectList?.every((item) => item.sealLocateModeCode !== code)) {
              const newCode = selectList?.[0]?.sealLocateModeCode
              // 并且在 options
              if (options.find((item) => item.code === newCode)) {
                code = newCode
              }
            }
          }
          setActiveKey(code)
          setItems(
            options.map((item) => {
              return {
                label: item.name,
                key: item.code,
                forceRender: true,
                children: <Spin />,
              }
            }),
          )
        }
      })
      .finally(() => {
        setLoading(false)
      })
  }, [])

  useEffect(() => {
    if (activeKey && items?.length) {
      // 判断是否请求过
      const isRequest = templateListMap[activeKey]?.isRequest
      if (isRequest) {
        return
      }
      const params: any = {
        sealLocateModeCode: activeKey,
        isShowSealKeyWord2,
      }
      const api = relatedValue ? getTemplateInfoList : getTemplateInfo
      if (relatedValue) {
        params.businessItemCode = relatedValue?.dictValue ? relatedValue?.dictValue : relatedValue
      }
      api(params).then(({ data: { data } }) => {
        const map = {
          ...templateListMap,
          [activeKey]: {
            isRequest: true,
            searchValue: '',
            list: data.map((item) => {
              return {
                ...item,
                label: item.name,
                value: item.id,
              }
            }),
          },
        }
        setTemplateListMap(map)
        showTabItems({ map })
      })
    }
  }, [activeKey, items])

  return loading ? (
    <Spin />
  ) : (
    <Tabs
      activeKey={activeKey}
      {...restProps}
      items={items}
      onChange={(tabKey) => {
        groupChecked.current

        if (!groupChecked.current[activeKey] || groupChecked.current[activeKey].length === 0) {
          onTabsChange()
          setActiveKey(tabKey)
          return
        }
        Modal.confirm({
          title: '切换定位方式会清空已选择的模板，确认继续吗？',
          icon: <ExclamationCircleOutlined />,
          okText: '确认',
          cancelText: '取消',
          onOk: () => {
            onTabsChange()
            setActiveKey(tabKey)
            groupChecked.current[activeKey] = []
          },
          onCancel: () => {
            groupChecked
          },
        })
      }}
      className={styles.tabBox}
    />
  )
}

export default TemplateTab
