import React, { useState, useRef } from 'react'

import { Col, Row, Modal, Button, Toolt<PERSON>, message, Alert } from 'antd'
import { RightOutlined, LeftOutlined } from '@ant-design/icons'
import { TableItem } from '@/types/package'
import { validTemplate } from '@/api/basicConfig/template'
import { cloneDeep } from 'lodash-es'

import styles from './index.module.less'
import TemplateTable from './TemplateTable'
import TemplateTab from './TemplateTab'

interface IProps {
  visible: boolean
  templateList: TableItem[]
  close: () => void
  onOk: (data: any) => Promise<any>
  relatedValue?: any
  isShowSealKeyWord2?: boolean
}

function TemplateModal(props: IProps) {
  const { templateList = [], close, onOk, relatedValue, isShowSealKeyWord2 } = props
  const [isExpand, setIsExpand] = useState(true)
  // const [templateMap, setTemplateMap] = useState<Record<string, any[]>>({})
  const templateSelected = useRef<TableItem[]>([])
  const [selectList, setSelectList] = useState<TableItem[]>(templateList)
  const [dictList, setDictList] = useState([])
  const [loading, setLoading] = useState(false)
  const [index, setIndex] = useState(0)

  templateSelected.current = selectList // 回调函数里拿不到 selectList的值

  const ComponentWrapper = (component) => {
    if (isExpand) {
      return <Col span={16}>{component}</Col>
    }
    return component
  }

  return (
    <Modal
      title="选择模板"
      onCancel={close}
      width={1200}
      {...props}
      onOk={() => {
        if (selectList.length && !selectList[0]?.isMain) {
          message.info('请先设置主合同')
          return
        }
        setLoading(true)
        const templateIds = selectList.map((item) => item.id)
        validTemplate(templateIds)
          .then(() => {
            close?.()
            onOk?.(selectList)
          })
          .finally(() => {
            setLoading(false)
          })
      }}
      okButtonProps={{
        loading,
        disabled: selectList.length === 0,
      }}
    >
      <Alert
        message="多选模板，只能选择印章类型一致，印章定位方式为关键字定位且关键字一致的模板。"
        showIcon
        type="info"
        closable
        style={{ marginBottom: '24px' }}
      />
      <Row justify="start" className={styles.templateModal}>
        <Col span={8} style={{ display: isExpand ? 'block' : 'none' }}>
          <TemplateTab
            selectList={selectList}
            onTabsChange={() => setSelectList([])}
            callback={(actionName, searchValue, list = []) => {
              if (actionName === '__dictLoaded') {
                // 字典加载成功
                setDictList(
                  list.map((item) => {
                    return {
                      label: item.name,
                      value: item.code,
                    }
                  }),
                )
              } else if (templateSelected.current.length === 0) {
                // 直接设置成主合同
                const newList: TableItem[] = cloneDeep(list)
                if (newList.length && !newList[0].isMain) {
                  newList[0].isMain = true
                }
                const indexArr = newList.map((item, i) => {
                  return {
                    ...item,
                    index: index + i,
                  }
                })
                setIndex(index + newList.length)
                setSelectList((prev) => {
                  const origin = prev.filter((item) => item.name.indexOf(searchValue) === -1)
                  const current = origin.concat(indexArr)
                  return current
                })
              } else {
                const originList = cloneDeep(templateSelected.current)
                const copyNewList: any[] = cloneDeep(
                  list.map((item, i) => {
                    return {
                      ...item,
                      index: index + i,
                    }
                  }),
                )

                setIndex(index + originList.length)
                setSelectList((prev) => {
                  let current: any[] = []
                  if (searchValue) {
                    const origin = prev.filter((item) => item.name.indexOf(searchValue) === -1)
                    const originListHasMain = origin.find((item) => item.isMain)
                    const newList = originListHasMain
                      ? copyNewList.map((item) => ({
                          ...item,
                          isMain: false,
                        }))
                      : copyNewList
                    current = origin.concat(newList)
                    const finnallyHasMain = current.find((item) => item.isMain)

                    if (!finnallyHasMain && current.length > 0) {
                      current[0].isMain = true
                    }
                  } else {
                    // eslint-disable-next-line no-lonely-if
                    if (copyNewList.length < prev.length) {
                      const hasMain = copyNewList.find((item) => item.isMain)
                      if (!hasMain && copyNewList.length > 0) {
                        copyNewList[0].isMain = true
                      }
                      current = copyNewList
                    } else {
                      const prevIds = prev.map((item) => item.id)
                      const newList = copyNewList
                        .filter((item) => !prevIds.includes(item.id))
                        .map((item) => ({
                          ...item,
                          isMain: false,
                        }))
                      current = prev.concat(newList)
                    }
                  }
                  return current
                })
              }
            }}
            relatedValue={relatedValue}
            isShowSealKeyWord2={isShowSealKeyWord2}
          />
        </Col>
        {ComponentWrapper(
          <div className={styles.rightBox}>
            <div className={styles.symbol}>
              <Button
                type="link"
                icon={
                  <Tooltip title={isExpand ? '点击后，左侧进行收起' : '点击后，展开定位方式'}>
                    {isExpand ? <LeftOutlined /> : <RightOutlined />}
                  </Tooltip>
                }
                size="large"
                onClick={() => {
                  setIsExpand(!isExpand)
                }}
              />
            </div>

            <div style={{ width: 'calc(100% - 40px)' }}>
              <div style={{ marginBottom: '9px' }}>
                已选 {selectList.length} 个模板&nbsp;&nbsp;
                {selectList.length > 0 && (
                  <Button
                    type="link"
                    size="small"
                    style={{ fontSize: '12px' }}
                    onClick={() => setSelectList([])}
                  >
                    全部清空
                  </Button>
                )}
              </div>
              <TemplateTable
                sealLocateModeDict={dictList}
                selectList={selectList}
                setSelectList={setSelectList}
                maxHeight={325}
                callback={(actionName, params) => {
                  if (actionName === 'remove') {
                    const restList = selectList.filter((item) => item.id !== params.id)
                    if (params?.isMain && restList.length > 0) {
                      // 移除的如果是主合同，把下一个变成主合同
                      restList[0].isMain = true
                    }
                    setSelectList(restList)
                  } else if (actionName === 'setMain') {
                    const first = { ...params, isMain: true }
                    const newList = [first]
                    selectList.forEach((item) => {
                      if (item.id !== params.id) {
                        newList.push({
                          ...item,
                          isMain: false,
                        })
                      }
                    })
                    setSelectList(newList)
                  }
                }}
              />
            </div>
          </div>,
        )}
      </Row>
    </Modal>
  )
}

export default TemplateModal
