import React, { useEffect, useMemo, useState } from 'react'
import { Select, Modal } from 'antd'
import { ExclamationCircleFilled } from '@ant-design/icons'
import { getDate, getDateTime } from '@amazebird/antd-schema-table/lib/utils'
import '@amazebird/antd-field'
import { Observer, SchemaType } from '@amazebird/antd-schema-form'
// 接口
import { TreeSelect as Dict } from '@galaxy/dict'

import { STATUS } from '@/constants'
import { getParamRule } from '@/api/basicConfig/business-param-rule'
import { getNameExists, getApplicationList } from '@/api/basicConfig/package'

// 组件
import ShortChain from '@/components/headerTitle/ShortChain'
import ContractTemplate from './ContractTemplate'

type UseSchemaReturnProps = {
  schema: SchemaType
  loaded: boolean
}

type UseSchema = (form: any, isView: boolean, id: string | null) => UseSchemaReturnProps

const { confirm } = Modal

// 主合同、附加协议
const MainOrAddition = (isView?: boolean): any => {
  return {
    contractTemplateIds: {
      label: '合同模板',
      required: [!isView, '请选择合同模板'],
      component: ContractTemplate,
      observer: Observer({
        watch: 'relatedBusinessItemCode',
        action: (actionValue) => {
          return {
            props: {
              isView,
              relatedValue: actionValue,
              isShowSealKeyWord2: true,
            },
          }
        },
      }),
      disabled: Observer({
        watch: 'relatedBusinessItemCode',
        action: (actionValue) => {
          if (actionValue?.dictValue) {
            return false
          }
          return true
        },
      }),
    },
  }
}

// 名称是否重复
const nameValidator = (id) => (_, value) => {
  if (!value) {
    return Promise.reject(new Error('请输入合同包名称'))
  }
  if (value) {
    return getNameExists({
      id: id || undefined,
      name: value,
    }).then(({ data: result }) => {
      if (result.data) {
        return Promise.reject(new Error('合同包名称重复，请重新命名'))
      }
      return Promise.resolve()
    })
  }
  return Promise.reject()
}

// 表单
export const useSchema: UseSchema = (form, isView, id) => {
  const [ruleMap, setRuleMap] = useState<any>({})
  const [applicationList, setApplicationList] = useState([])

  const loaded = useMemo(
    () => Object.keys(ruleMap).length > 0 && applicationList.length > 0,
    [ruleMap, applicationList],
  )

  const onOk = (value, newValue, onChange) => {
    form.setFieldsValue({
      contractTemplateIds: null,
    })
    onChange?.({
      ...value,
      dictValue: newValue,
    })
    if (newValue) {
      const option = ruleMap[newValue]
      if (option) {
        form.setFieldsValue({
          validDurationName: {
            type: option?.validDurationCode,
            day: option?.validDurationDay,
          },
        })
      }
    } else {
      // 清空
      form.setFieldsValue({
        validDurationName: {
          type: undefined,
          day: undefined,
        },
        // priority: undefined,
      })
    }
  }

  useEffect(() => {
    getApplicationList().then((res) => {
      const {
        data: { data },
      } = res
      const result = data.map((item) => ({
        label: item.applicationName,
        value: item.applicationCode,
      }))
      setApplicationList(result)
    })
  }, [])

  useEffect(() => {
    getParamRule().then(({ data: { data } }) => {
      if (Array.isArray(data)) {
        setRuleMap(
          data.reduce((cur, item) => {
            // eslint-disable-next-line no-param-reassign
            cur[item.businessNameCode] = {
              validDurationCode: item.validDurationCode,
              validDurationDay: item.validDurationDay,
              businessName: item.businessName,
            }
            return cur
          }, {}),
        )
      }
    })
  }, [])

  const schema = {
    name: {
      label: '合同包名称',
      component: 'Input',
      required: !isView,
      rules: [
        {
          type: 'string',
          max: 500,
          message: '最长不超过500个字符',
        },
        {
          validator: nameValidator(id),
        },
      ],
    },
    number: {
      label: '合同包编号',
      component: 'Input',
      visible: isView,
    },
    ...MainOrAddition(isView),
    classification: {
      label: '分类',
      component: 'Checkbox',
      options: [
        { label: '朴朴员工合同包', value: 0 },
        { label: '自营第三方合同包', value: 1 },
      ],
      required: [!isView, '请选择分类'],
    },
    relatedBusinessItemCode: {
      label: '关联业务项',
      component: ({ onChange, value }) => {
        const relatedBusinessItemCode = form.getFieldsValue(true)?.relatedBusinessItemCode
        return isView ? (
          `${relatedBusinessItemCode?.selectValue}-${relatedBusinessItemCode?.dictValue}`
        ) : (
          <div
            style={{
              display: 'flex',
            }}
          >
            <Select
              options={applicationList}
              style={{
                minWidth: '200px',
                marginRight: 8,
              }}
              placeholder="请选择应用"
              allowClear
              showSearch
              optionFilterProp="label"
              value={value?.selectValue}
              onChange={(selectValue) => {
                form.setFieldsValue({
                  contractTemplateIds: null,
                })
                onChange?.({
                  ...value,
                  selectValue,
                })
              }}
            />
            <Dict
              style={{
                minWidth: '200px',
              }}
              code="BUSINESS_ITEM"
              showSearch
              allowClear
              placeholder="请选择业务项"
              value={value?.dictValue || null}
              onChange={(dictValue) => {
                if (!value?.dictValue) {
                  onOk(value, dictValue, onChange)
                } else {
                  confirm({
                    title: '变更业务项后将会清空已设置的人员范围条件，确定要执行该操作吗？',
                    icon: <ExclamationCircleFilled />,
                    okText: '确定',
                    cancelText: '取消',
                    onOk: () => onOk(value, dictValue, onChange),
                    onCancel() {
                      form.setFieldsValue({
                        relatedBusinessItemCode: {
                          ...value,
                        },
                      })
                    },
                  })
                }
              }}
            />
          </div>
        )
      },
      required: [!isView, ''],
      rules: [
        {
          validator: (_, value) => {
            if (!value?.dictValue) {
              return Promise.reject(new Error('请选择业务项'))
            }
            return Promise.resolve()
          },
        },
      ],
    },
    validDurationName: {
      label: '短链有效时长',
      component: ShortChain,
      disabled: true,
      props: {
        isShowDay: true,
        isView,
        code: 'EFFECTIVE_DURATION',
      },
    },
    priority: {
      label: '优先级',
      component: 'InputNumber',
      required: !isView,
      props: {
        precision: 0,
        min: 1,
        max: 1000000,
        style: {
          width: '100%',
        },
      },
    },
    userNameCreate: {
      label: '创建人',
      component: 'Input',
      visible: isView,
    },
    timeCreate: {
      label: '创建时间',
      component: ({ value }) => getDateTime(value),
      visible: isView,
    },
    status: {
      label: '状态',
      component: 'Select',
      options: STATUS,
      visible: isView,
    },
    disableDate: {
      label: '停用日期',
      component: ({ value }) => getDate(value),
      visible: isView,
    },
    remark: {
      label: '合同包说明',
      component: 'Input.TextArea',
      max: 200,
      props: {
        rows: 5,
      },
    },
  }

  return { loaded, schema }
}
