import React from 'react'
import { Item, FormItemGrid } from '@amazebird/antd-schema-form'

interface IProps {
  isView: boolean
}

const TopForm = ({ isView }: IProps) => {
  const wrapperCol = {
    wrapperCol: isView ? { flex: 1 } : undefined,
  }
  // 合同包编号不显示，也会占位
  return isView ? (
    <>
      <FormItemGrid colCount={2}>
        <Item field="name" />
        <Item field="number" />
        <Item field="classification" />
        <Item field="relatedBusinessItemCode" />
        <Item
          field="contractTemplateIds"
          tooltip="合同批量签署（多选模板）只能选择印章类型一致、关键字定位且关键字一致的模板"
        />
        <Item field="validDurationName" />
      </FormItemGrid>
      <Item field="remark" wrapperCol={{ flex: '0 0 71.3%' }} labelCol={{ flex: '0 0 14.75%' }} />
    </>
  ) : (
    <>
      <FormItemGrid colCount={2}>
        <Item field="name" />
        <Item field="classification" />
        <Item field="relatedBusinessItemCode" />
        <Item
          field="contractTemplateIds"
          tooltip="合同批量签署（多选模板）只能选择印章类型一致、关键字定位且关键字一致的模板"
          {...wrapperCol}
        />
        <Item field="validDurationName" />
      </FormItemGrid>
      <Item field="remark" wrapperCol={{ flex: '0 0 71.3%' }} labelCol={{ flex: '0 0 14.75%' }} />
    </>
  )
}

export default TopForm
