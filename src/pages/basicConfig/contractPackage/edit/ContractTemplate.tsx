import React, { memo, useEffect, useState, useRef } from 'react'
import { <PERSON>ton, Select, SelectProps } from 'antd'

import config from '@/config'
import modalWrapperHoc from '@/components/modalWrapperHoc'
import { getContractTemplate } from '@/service/package'
import { TableItem } from '@/types/package'
import TemplateModal from './templateModal'
import { getTemplateName } from './templateModal/TemplateTable'

import styles from './style.module.less'

const { baseRoute } = config

type IProps = {
  isView?: boolean
  value: TableItem[]
  isPreview?: boolean
  disabled?: boolean
  relatedValue?: any
  isShowSealKeyWord2?: boolean
} & Omit<SelectProps, 'value'>

// 合同模板
function ContractTemplate(props: IProps) {
  const origin = useRef<TableItem[]>([])
  const [options, setOptions] = useState([])

  const {
    isView,
    value,
    isPreview = true,
    onChange,
    disabled,
    relatedValue,
    isShowSealKeyWord2,
  } = props

  const handleToPreview = (templateIds) => {
    window.open(`${baseRoute}/contract/template-preview?id=${templateIds}`, '_blank')
  }

  // 值
  if (isView) {
    const templateIds = Array.isArray(value) ? value.map((item) => item.id) : []
    return (
      <div className={styles.viewTextWrapper}>
        {value.length === 0 ? (
          '--'
        ) : (
          <div className={styles.viewText} onClick={() => handleToPreview(templateIds)}>
            {value?.map((template: any, index: number) => (
              <>
                {getTemplateName(index === 0, template.name)}
                {index + 1 === value.length ? '' : '、'}
              </>
            ))}
          </div>
        )}
      </div>
    )
  }

  useEffect(() => {
    getContractTemplate().then((data) => {
      setOptions(data)
    })
    origin.current = value
  }, [])

  return (
    <div
      style={{
        position: 'relative',
        width: '100%',
      }}
    >
      <Select
        {...props}
        value={value?.map((item, index) => {
          return {
            ...item,
            label: getTemplateName(index === 0, item.title as string), // 处理主合同的显示问题
          }
        })}
        onChange={(_value, _option) => {
          // 因为下拉的数据不是全的原因，做的一层处理
          if (origin.current?.length > 0) {
            const ids = _value.map((item) => item.value)
            // 由于 _value 获取不到原来全的数据，保存一份
            onChange?.(
              origin.current.filter((item) => ids.includes(item.id)),
              _option,
            )
          } else {
            onChange?.(_value, _option)
          }
        }}
        showSearch
        mode="multiple"
        labelInValue
        placeholder="请选择合同模板"
        dropdownMatchSelectWidth={310}
        dropdownStyle={{ display: 'none' }}
        options={options}
        style={{
          minWidth: '155px',
        }}
        onClick={() => {
          if (disabled) {
            return
          }
          modalWrapperHoc(TemplateModal)({
            templateList: value?.map((item, index) => {
              return {
                ...item,
                isMain: index === 0,
              }
            }),
            relatedValue,
            isShowSealKeyWord2,
            onOk: (list) => {
              if (Array.isArray(list)) {
                const newList = list.map((item) => {
                  return {
                    ...item,
                    title: item.label, // 用于回填时的数据
                    value: item.id, // 用于onChange判断
                  }
                })
                origin.current = newList
                onChange?.(newList, newList)
              }
            },
          })
        }}
        allowClear
      />
      {isPreview && (
        <Button
          type="link"
          style={{
            position: 'absolute',
            right: '-42px',
            top: 0,
            padding: 0,
          }}
          disabled={!value?.length}
          onClick={() => {
            window.open(
              `${baseRoute}/contract/template-preview?id=${value
                ?.map((item) => item.id)
                .join(',')}`,
            )
          }}
        >
          预览
        </Button>
      )}
    </div>
  )
}

export default memo(ContractTemplate)
