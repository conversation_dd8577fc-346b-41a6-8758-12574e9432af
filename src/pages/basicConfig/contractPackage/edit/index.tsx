import React, { useState, useEffect, useRef } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { message, Modal, Tooltip, Empty } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { SchemaForm, FORM_MODE } from '@amazebird/antd-schema-form'
import { getPermission } from '@galaxy/rbac'
import '@amazebird/antd-field'
import config from '@/config'
import { useStore } from '@/stores'
import { CONTRACT_PACKAGE } from '@/constants/rbac-code'

import { isEqual } from 'lodash-es'

// 接口
import {
  createContractPackage,
  editContractPackage,
  getContractPackage,
} from '@/api/basicConfig/package'

// 组件
import PageLoading from '@/components/pageLoading'
import BasicFooter from '@/components/pageFooter/BasicFooter'
import { Title as HeaderTitle } from '@/components/headerTitle'
import { usePersonScopeValue } from '@/hooks/usePersonScopeValue'
import TopForm from './TopForm'
import ExtraBtn from './ExtraBtn'
import PersonScope from '../personScope'
import { useSchema } from './columnsConfig'
// 样式
import styles from './style.module.less'

const { baseRoute } = config

function ContractConfig() {
  const store = useStore((state) => state)
  const { setExtra, setTitle } = store

  const form = SchemaForm.createForm()
  const navigate = useNavigate()
  const [search] = useSearchParams()
  const id = search.get('id')
  const isEdit = id !== null
  const isView = search.get('isView') !== null
  const originRef = useRef({
    contractTemplateIds: [],
  })
  const [loading, setLoading] = useState(false)
  const [businessCode, setBusinessCode] = useState('')
  const [formLoading, setFormLoading] = useState(true) // 默认true，不然字典会重复请求
  const { schema, loaded } = useSchema(form, isView, id)
  const { setValue: setPersonScopeValue } = usePersonScopeValue()
  const personScopeRef = useRef<any>(null)

  /**
   * 数据类型转换
   */
  const handleArrayToString = (rules) => {
    for (let i = 0; i < rules?.length; i++) {
      if (rules[i].rules) {
        handleArrayToString(rules[i].rules)
      } else {
        const isArrayValue = Array.isArray(rules[i].rightValue)
        const isArrayLabelValue = Array.isArray(rules[i].rightValueLabel)
        // eslint-disable-next-line
        rules[i] = {
          ...rules[i],
          rightValue: isArrayValue ? rules[i].rightValue?.join(',') : rules[i].rightValue,
          rightValueLabel: isArrayLabelValue
            ? rules[i].rightValueLabel?.join(',')
            : rules[i].rightValueLabel,
        }
      }
    }
  }

  const handleStringToArray = (rules) => {
    for (let i = 0; i < rules?.length; i++) {
      if (rules[i].rules) {
        handleStringToArray(rules[i].rules)
      } else {
        const isInterface = rules[i].controlType === 'interface'
        const isDepTree = rules[i].controlType === 'departmentTree'

        const rightValueArr = rules[i].rightValue?.split(',')
        // eslint-disable-next-line
        rules[i] = {
          ...rules[i],
          rightValue:
            isDepTree || isInterface
              ? rightValueArr.map((item) => {
                  if (Number.isNaN(Number(item))) {
                    return item
                  }
                  return Number(item)
                })
              : rightValueArr,
          rightValueLabel: rules[i].rightValueLabel?.split(','),
        }
      }
    }
  }

  // 保存按钮
  const handleConfirm = async () => {
    setLoading(true)
    try {
      const [values, data] = await Promise.all([
        personScopeRef?.current?.getValue(),
        form?.validateFields(),
      ])
      const userRangeRuleConfig = values?.rules
      handleArrayToString(userRangeRuleConfig.rules)
      const contractTemplateIds = data.contractTemplateIds.map((v) => v.id)
      const params: any = {
        ...data,
        contractTemplateIds,
        isSupportInner: data.classification.indexOf(0) !== -1,
        isSupportOuter: data.classification.indexOf(1) !== -1,
        name: data.name,
        relatedApplicationCode: data.relatedBusinessItemCode.selectValue,
        relatedBusinessItemCode: data.relatedBusinessItemCode.dictValue,
        relatedContractDeadlineRuleId: data.relatedContractDeadlineRuleId,
        relatedContractTypeRuleId: data.relatedContractTypeRuleId,
        remark: data.remark,
        userRangeName: values?.name,
        userRangeRuleConfig: JSON.stringify(userRangeRuleConfig || {}),
      }
      if (isEdit) {
        params.id = id
      }
      // 主合同，附加协议
      const apiRequest = isEdit ? editContractPackage(id, params) : createContractPackage(params)
      await apiRequest
      message.success('保存成功')
      navigate(`${baseRoute}/contract/basic-config/contract-package`)
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('error:', error)
    } finally {
      setLoading(false)
    }
  }

  // 返回列表
  const handleCancel = () => {
    if (isEqual(form?.getFieldsValue(true), originRef.current)) {
      navigate(-1)
      return
    }
    Modal.confirm({
      title: '提示',
      content: '数据已发生变化，是否继续返回列表？',
      cancelText: '取消',
      okText: '确定',
      onOk() {
        navigate(-1)
      },
    })
  }

  // 刷新数据
  const refreshForm = (callback?: (values?: any) => void) => {
    setFormLoading(true) // 会触发接口重复
    getContractPackage(id as any)
      .then(({ data: { data } }) => {
        const values = {
          ...data,
        }
        // 短链有效时长
        values.validDurationName = {
          type: values.validDurationName,
          day: values.validDurationDays,
        }
        if (isView) {
          values.userNameCreate = `${values.userNameCreate || '--'}（${
            values.userIdCreate || '--'
          }）`
          values.remark = values.remark || '--'
        }
        // 查看时展示的是中文
        values.contractTemplateIds =
          data.templateRela?.map((item, index) => {
            return {
              ...item,
              isMain: index === 0,
              label: item.name,
              value: item.id,
              title: item.name,
            }
          }) || []
        const arr: number[] = []
        if (data.isSupportInner) {
          arr.push(0)
        }
        if (data.isSupportOuter) {
          arr.push(1)
        }
        values.classification = arr
        if (isView) {
          values.originRelatedBusinessItemCode = values.relatedBusinessItemCode
        }
        values.relatedBusinessItemCode = {
          selectValue: isView ? values.relatedApplicationName : values.relatedApplicationCode,
          dictValue: isView ? values.relatedBusinessItemName : values.relatedBusinessItemCode,
        }
        setBusinessCode(data.relatedBusinessItemCode || '')
        form?.setFieldsValue(values)
        originRef.current = values
        callback?.(values)

        if (values.userRangeRuleConfig) {
          const userRangeRuleConfig = JSON.parse(values.userRangeRuleConfig)
          handleStringToArray(userRangeRuleConfig.rules)
          const obj = {
            name: values.userRangeName,
            rules: userRangeRuleConfig,
          }
          setPersonScopeValue(obj)
        }
      })
      .finally(() => {
        setFormLoading(false)
      })
  }

  // 启用禁用完，需要刷新一下按钮
  const refreshExtra = () => {
    refreshForm((values) => {
      if (!isView) {
        return
      }
      setExtra(
        <ExtraBtn
          values={values}
          callback={(name) => {
            if ([CONTRACT_PACKAGE.LIST.ENABLED, CONTRACT_PACKAGE.LIST.DISABLED].includes(name)) {
              // 刷新
              refreshExtra()
            }
          }}
        />,
      )
    })
  }

  // 前往人员校验
  const handleCheckPerson = async () => {
    const values = await personScopeRef.current.getValue()
    setPersonScopeValue(values)

    businessCode
      ? window.open(
          `${baseRoute}/contract/basic-config/contract-package/verification-personnel?code=${businessCode}`,
        )
      : window.open(`${baseRoute}/contract/basic-config/contract-package/verification-personnel`)
  }

  // 业务项 code 改变
  const onBussinessCodeChange = (value) => {
    const { relatedBusinessItemCode } = value
    if (!relatedBusinessItemCode) {
      return
    }

    const { dictValue } = relatedBusinessItemCode
    // 两个条件都满足当业务项不改变时
    if (dictValue === businessCode || (!dictValue && !businessCode)) {
      return
    }
    personScopeRef.current?.clearValue()
    setBusinessCode(dictValue)
  }

  useEffect(() => {
    let title = <span>新增合同包</span>
    if (isEdit) {
      title = isView ? <span>合同包详情</span> : <span>编辑合同包</span>
      refreshExtra()
    } else {
      form?.setFieldsValue(originRef.current)
      setFormLoading(false)
    }
    setTitle(title)

    return () => {
      setTitle(null)
      setExtra(null)
    }
  }, [])

  return formLoading || !loaded ? (
    <PageLoading />
  ) : (
    <div className={styles.packageContainer}>
      <SchemaForm
        form={form}
        schema={schema}
        mode={isView ? FORM_MODE.DETAIL : FORM_MODE.ADD}
        labelCol={{ flex: '0 0 30%' }}
        onValuesChange={onBussinessCodeChange}
      >
        <HeaderTitle title="基础信息" />
        <TopForm isView={isView} />
        <HeaderTitle
          title="设置人员范围"
          style={{ marginTop: '70px', position: 'relative' }}
          extra={
            getPermission(CONTRACT_PACKAGE.LIST.VERIFICATION_PERSONNEL) &&
            !isView &&
            businessCode ? (
              <div className={styles.toValidator} onClick={handleCheckPerson}>
                <span style={{ fontSize: '14px' }}>校验人员</span>
                <Tooltip title="可点击模拟是否能根据设置条件，找到对应人员。">
                  <QuestionCircleOutlined className={styles.icon} />
                </Tooltip>
              </div>
            ) : null
          }
        />
        {businessCode ? (
          <PersonScope personScopeRef={personScopeRef} code={businessCode} readOnly={isView} />
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            className={styles.empty}
            description="暂无数据，请先选择关联业务项"
          />
        )}
        <BasicFooter
          loading={loading}
          useSchemaSubmit={false}
          showConfirm={!isView}
          showCancel={isView ? !window.__POWERED_BY_QIANKUN__ : true}
          cancelText={isView ? '返回' : undefined}
          handleConfirm={handleConfirm}
          handleCancel={handleCancel}
        />
      </SchemaForm>
    </div>
  )
}

export default ContractConfig
