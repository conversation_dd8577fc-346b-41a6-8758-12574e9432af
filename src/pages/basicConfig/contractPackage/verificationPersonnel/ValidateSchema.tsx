import React from 'react'
import { Divider } from 'antd'
import { SchemaForm, Item } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/antd-schema-form'

import PageLoading from '@/components/pageLoading'

import styled from './style.module.less'

interface IProps {
  form: any
  schema: Record<string, SchemaType[keyof SchemaType] & { divider: boolean }>
}

const ValidateSchema = ({ schema, form }: IProps) => {
  return (
    <div style={{ width: '100%' }}>
      {Object.keys(schema).length >= 1 ? (
        <SchemaForm form={form} schema={schema} className={styled.schemaWrapper}>
          {Object.keys(schema).map((item, index) => {
            return (
              <>
                <Item field={item} key={item} style={{ width: '100%' }} />
                {schema[item]?.divider && index !== Object.keys(schema).length - 1 && <Divider />}
              </>
            )
          })}
        </SchemaForm>
      ) : (
        <PageLoading />
      )}
    </div>
  )
}

export default ValidateSchema
