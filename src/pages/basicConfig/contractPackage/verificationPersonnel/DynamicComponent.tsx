import React from 'react'
import PageSelect from '@/components/PageSelect'
import { maxTagPlaceholder } from '@/utils/utils'
import DhrDict from '@/components/RuleControl/DhrDict'
import OrgsFilterSelect from '@/components/business/OrgsFilterSelect'
import { getPositionApi } from '@/api/basic-server'

// 支持规则控件化
// 规则选择器属性（部门、实体岗位、基础岗位、字典项、审批角色）支持控件化选择
enum ControlType {
  interface, // 接口
  ehrDictionary, // 字典
  departmentTree, // 部门树
  contractDictionary, // 合同字典
}

type ControlTypeKey = keyof typeof ControlType

const DynamicComponent = ({
  controlType,
  code,
  onChange,
  value,
  ...rest
}: {
  controlType?: ControlTypeKey | string
  code?: string | undefined
  onChange?: any
  value?: any
  [field: string]: any
}) => {
  switch (controlType) {
    case 'interface':
      return (
        <PageSelect
          {...rest}
          apiFunction={getPositionApi}
          fieldNames={{
            value: 'id',
          }}
          maxLength={100}
          onChange={(v) => {
            onChange?.(v)
          }}
          value={value}
          mode="multiple"
          maxTagCount="responsive"
          maxTagPlaceholder={maxTagPlaceholder()}
        />
      )
    case 'ehrDictionary':
      return (
        <DhrDict
          // 该类型组件一定会有 code
          code={code as string}
          scope="EHR"
          {...rest}
          onChange={(v) => {
            onChange?.(v)
          }}
          value={value}
          maxTagCount="responsive"
          maxTagPlaceholder={maxTagPlaceholder()}
        />
      )
    case 'contractDictionary':
      return (
        <DhrDict
          code={code as string}
          scope="CONTRACT"
          {...rest}
          onChange={(v) => {
            onChange?.(v)
          }}
          value={value}
          maxTagCount="responsive"
          maxTagPlaceholder={maxTagPlaceholder()}
        />
      )
    case 'departmentTree':
      return (
        <OrgsFilterSelect
          autoClearSearchValue={false}
          showSearch
          multiple
          {...rest}
          value={value}
          onChange={(v) => {
            onChange?.(v)
          }}
          maxTagCount="responsive"
          maxTagPlaceholder={maxTagPlaceholder()}
        />
      )
    default: {
      // 可能是xxxDictionary，split之后传递给scope,如果匹配不到则返回无匹配组件
      const scope = controlType?.split('Dictionary')[0].toUpperCase()
      if (scope) {
        return (
          <DhrDict
            code={code as string}
            scope={scope}
            {...rest}
            onChange={(v) => {
              onChange?.(v)
            }}
            value={value}
            maxTagCount="responsive"
            maxTagPlaceholder={maxTagPlaceholder()}
          />
        )
      }
      return <>无匹配组件可选择</>
    }
  }
}

export default DynamicComponent
