import React from 'react'
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons'
import styles from './style.module.less'

interface PassProps {
  name: string
  num: string
}

export interface ResultProps {
  fail: PassProps[]
  success: PassProps[]
}

interface IResultProps {
  result: ResultProps
}

const Result = ({ result }: IResultProps) => {
  const { fail = [], success = [] } = result

  return (
    <div className={styles.footer}>
      <div className={styles.resultText}>校验结果</div>
      <div>
        <div className={styles.resultList}>
          <CheckCircleOutlined className={styles.check} />
          <span className={styles.detail}>符合人员</span>
          <span>
            （{success.length}个）
            {success.length > 0 &&
              `：${success.map((item: any) => `${item.name}（${item.num}）`).join('、')}`}
          </span>
        </div>
        <div className={styles.resultList}>
          <CloseCircleOutlined className={styles.close} />
          <span className={styles.detail}>不符合人员</span>
          <span>
            （{fail.length}个）
            {fail.length > 0 &&
              `：${fail.map((item: any) => `${item.name}（${item.num}）`).join('、')}`}
          </span>
        </div>
      </div>
    </div>
  )
}
export default Result
