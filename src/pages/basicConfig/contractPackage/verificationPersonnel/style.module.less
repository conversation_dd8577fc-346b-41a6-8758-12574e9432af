.external {
  margin: 24px;
  border: 1px solid #e4e4e4;
  flex: 1;
  padding-bottom: 50px;

  .schemaWrapper {
    padding: 20px;
    width: 100%;
  }
}

.leftFont {
  font-weight: bold;
  margin: 12px 0 12px 16px;
}

.leftContent {
  margin-left: 40px;
  margin-top: 60px;

  :global {
    .brick-rule-builder-rule--readOnly {
      line-height: 3;
    }
  }
}

.center {
  margin-top: 24px;
  position: relative;
  width: 20px;
  display: flex;
  align-items: center;

  .icon {
    position: absolute;
    font-size: 20px;
    color: #1ABB9C;
  }
}

.btn {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;
}

.line {
  border-top: 1px solid #e4e4e4;
}


.footer {
  padding-bottom: 50px;

  .resultText {
    margin: 16px 0 0 16px;
    font-weight: bold;
  }

  .resultList {
    margin: 16px 0 0 16px;

    .check {
      color: #1ABB9C;
    }

    .close {
      color: red;
    }

    .detail {
      margin-left: 10px;
    }
  }
}

.boundary {
  margin-bottom: 100px;
}