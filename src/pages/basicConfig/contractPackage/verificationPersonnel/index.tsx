import React, { useEffect, useRef, useState } from 'react'
import { useLocation } from 'react-router-dom'
import { <PERSON><PERSON>, But<PERSON> } from 'antd'
import { ArrowRightOutlined } from '@ant-design/icons'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { RulesDataGroup } from '@pupu/brick/lib/components/RuleBuilder/types'
import { cloneDeep } from 'lodash-es'
import queryString from 'query-string'

import { maxTagPlaceholder } from '@/utils/utils'
import { userRangeCheck } from '@/api/basicConfig/package'
import { usePersonScopeValue } from '@/hooks/usePersonScopeValue'
import UserFilterSelect from '@/components/business/UserFilterSelect'
import ValidateSchema from './ValidateSchema'
import Result, { ResultProps } from './Result'
import DynamicComponent from './DynamicComponent'
import ConditionSelect from '../personScope/ConditionSelect'

import styled from './style.module.less'

interface QueryProps {
  code: string // 业务项 code
}

const VerificationPersonnel = () => {
  // 规则
  const ruleRef = React.useRef<{
    validate: () => boolean
    getValue: () => RulesDataGroup
  }>()
  const [ruleValue, setRuleValue] = useState<any>(null)
  const [schema, setSchema] = useState({})
  const [disabled, setDisabled] = useState(true)
  const [checkLoading, setCheckLoading] = useState(false)
  const [validateResult, setValidateResult] = useState<ResultProps>({} as any)
  const { getValue } = usePersonScopeValue()
  const form = SchemaForm.createForm()
  const show = useRef(false)
  const location = useLocation()

  const { code } = queryString.parse(location.search) as unknown as QueryProps

  /**
   * 数据类型转换
   */
  const handleArrayToString = (rules) => {
    for (let i = 0; i < rules?.length; i++) {
      if (rules[i].rules) {
        handleArrayToString(rules[i].rules)
      } else {
        const isArrayValue = Array.isArray(rules[i].rightValue)
        const isArrayLabelValue = Array.isArray(rules[i].rightValueLabel)
        // eslint-disable-next-line
        rules[i] = {
          ...rules[i],
          rightValue: isArrayValue ? rules[i].rightValue?.join(',') : rules[i].rightValue,
          rightValueLabel: isArrayLabelValue
            ? rules[i].rightValueLabel?.join(',')
            : rules[i].rightValueLabel,
        }
      }
    }
  }

  const recursion = (rules) => {
    const useRules = cloneDeep(rules)

    let useFormArr: any[] = []
    for (let i = 0; i < useRules?.length; i++) {
      if (!useRules[i].rules) {
        useFormArr.push(useRules[i])

        useRules[i] = {
          ...useRules[i],
          rightValueLabel: useRules[i].assignmentTypeName
            ? // eslint-disable-next-line no-irregular-whitespace
              `${useRules[i].assignmentTypeName}　${useRules[i].rightValueLabel.join(',')}`
            : useRules[i].rightValueLabel.join(','),
        }
      } else {
        const { useFormArr: subUseFormArr, useRules: subUseRules } = recursion(useRules[i].rules)
        useRules[i] = {
          ...useRules[i],
          rules: subUseRules,
        }
        useFormArr = useFormArr.concat(subUseFormArr)
      }
    }

    return { useFormArr, useRules }
  }

  /**
   * 创建表单项
   */
  const createFormComb = (item) => {
    const extendSchema = {
      [item.key]: {
        label: item.label,
        component: ({ value, onChange, ...props }) =>
          DynamicComponent({
            ...props,
            value,
            onChange,
            code: item.code,
            controlType: item.type,
          }),
        required: [true, `请选择${item.label}`],
      },
    }

    return extendSchema
  }

  /**
   * 创建 schema 配置
   */
  const createSchema = (formItems) => {
    let obj = {
      userNums: {
        label: '选择人员',
        component: ({ onChange }) => (
          <UserFilterSelect
            onChange={(value) => {
              setDisabled(!(value as Array<any>)?.length)
              onChange?.(value)
            }}
            isFilterInner={false}
            maxTagCount="responsive"
            showSearch
            multiple
            maxTagPlaceholder={maxTagPlaceholder()}
          />
        ),
        required: [true, '请选择人员'],
        divider: true,
      },
    }
    const result = {}
    formItems.forEach((item: any) => {
      // 非业务赋值
      if (!item.assignmentType) {
        return
      }
      result[item.leftValue] ??= {
        label: item.leftValueLabel,
        code: item.dictionaryCode,
        type: item.controlType,
      }
      item.rightControlType &&
        (result[item.rightValue[0]] ??= {
          label: item.rightValueLabel[0],
          code: item.rightDictionaryCode,
          type: item.rightControlType,
        })
    })
    Object.keys(result).forEach((key) => {
      obj = {
        ...obj,
        ...createFormComb({
          key,
          ...result[key],
        }),
      }
    })

    setSchema(obj)
  }

  const handleCheck = async () => {
    setCheckLoading(true)
    try {
      const values = await form?.validateFields()
      const { rules: checkData = {} } = getValue() as any
      handleArrayToString(checkData.rules)

      const matchRuleMap = {}
      Object.keys(values).forEach((key) => {
        matchRuleMap[key] = values[key].map((item) => item.value).join(',')
      })

      const params = {
        matchRuleMap,
        userNums: values.userNums.map((v) => v.value),
        userRangeRuleConfig: JSON.stringify(checkData),
      }
      const { data: result } = await userRangeCheck(params)

      const data = result.data
      show.current = true
      setValidateResult({
        success: data.passUsers,
        fail: data.notPassUsers,
      })
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('error:', error)
    } finally {
      setCheckLoading(false)
    }
  }

  useEffect(() => {
    const cacheRule = getValue()
    if (!cacheRule) {
      return
    }
    const { useFormArr, useRules } = recursion(cacheRule.rules.rules)
    setRuleValue({ ...cacheRule.rules, rules: useRules })
    createSchema(useFormArr)
  }, [])

  return (
    <div
      style={{
        margin: '24px',
        background: 'white',
      }}
    >
      <Alert
        message="根据合同包设置人员范围条件，选择人员、指定特殊条件值，模拟校验人员是否在该条件范围内。"
        showIcon
        type="info"
        closable
      />
      <div style={{ display: 'flex' }}>
        <div className={styled.external} id="left">
          <div className={styled.leftFont}>条件设置</div>
          <div className={styled.leftContent}>
            {/* @ts-ignore */}
            <ConditionSelect value={ruleValue} onRef={ruleRef} readOnly code={code} />
          </div>
        </div>
        <div className={styled.center}>
          <ArrowRightOutlined className={styled.icon} />
        </div>
        <div className={styled.external} style={{ position: 'relative' }}>
          <div className={styled.leftFont}>模拟人员&特殊条件值</div>
          <ValidateSchema schema={schema} form={form} />
        </div>
      </div>
      <div className={styled.btn}>
        <Button type="primary" onClick={handleCheck} disabled={disabled} loading={checkLoading}>
          校验一下
        </Button>
      </div>
      <div className={styled.line} />
      {show.current && <Result result={validateResult} />}
    </div>
  )
}

export default VerificationPersonnel
