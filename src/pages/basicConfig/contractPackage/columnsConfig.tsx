import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Button, message, Tooltip } from 'antd'
import { SchemaColumnType } from '@amazebird/antd-schema-table'
import config from '@/config'

// 组件
import PermissionDropdown, {
  ActionProps,
  PermissionDropdownProps,
} from '@/components/permissionDropdown'
import PermissionAction from '@/components/permissionAction'
import { STATUS } from '@/constants'
import { CONTRACT_PACKAGE } from '@/constants/rbac-code'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import { downloadVar, getRelatedItems } from '@/api/basicConfig/package'
import { getUserRangeNameList, getContractTemplateList } from '@/service/package'
import { usePersonScopeValue } from '@/hooks/usePersonScopeValue'

import { operateActions } from './common'

const { baseRoute } = config

interface IUseColumnsProps {
  action: any
}

type IUseColumns = (props: IUseColumnsProps) => SchemaColumnType

type IMoceAction = {
  actions: ActionProps[]
  isDetailPage?: boolean
} & Omit<PermissionDropdownProps, 'actions'>

// 更多
export const MoreAction = ({ actions = [], isDetailPage = false, ...restProps }: IMoceAction) => (
  <PermissionDropdown actions={actions} {...restProps}>
    {isDetailPage ? <Button>更多</Button> : '更多'}
  </PermissionDropdown>
)

/**
 * 表格列配置
 */
export const useColumnsConfig: IUseColumns = ({ action }) => {
  const navigate = useNavigate()
  const { removeValue, setValue } = usePersonScopeValue()

  // 字符串转换数组
  const handleStringToArray = (rules) => {
    for (let i = 0; i < rules?.length; i++) {
      if (rules[i].rules) {
        handleStringToArray(rules[i].rules)
      } else {
        // eslint-disable-next-line
        rules[i] = {
          ...rules[i],
          rightValue: rules[i]?.rightValue?.split(','),
          rightValueLabel: rules[i]?.rightValueLabel?.split(','),
        }
      }
    }
  }

  // 处理缓存数据
  const handleLocalStorage = (record) => {
    const userRangeRuleConfig = JSON.parse(record.userRangeRuleConfig)
    handleStringToArray(userRangeRuleConfig.rules)

    const obj = {
      name: record.userRangeName,
      rules: userRangeRuleConfig,
    }

    removeValue()
    setValue(obj)
  }

  const moreActions = (
    status: number | string,
    record: { id: number; name: string; status: number; [field: string]: any },
    successCallback?: (actionName: string) => void,
  ) => {
    const isEnabled = String(status) === '1'
    const actionParams = isEnabled
      ? {
          component: '停用',
          code: CONTRACT_PACKAGE.LIST.DISABLED,
          onClick: () => operateActions(CONTRACT_PACKAGE.LIST.DISABLED, record, successCallback),
        }
      : {
          component: '启用',
          code: CONTRACT_PACKAGE.LIST.ENABLED,
          onClick: () => operateActions(CONTRACT_PACKAGE.LIST.ENABLED, record, successCallback),
        }
    const actions = [
      actionParams,
      {
        component: <span style={{ color: 'red' }}>删除</span>,
        code: CONTRACT_PACKAGE.LIST.DELETE,
        onClick: () => {
          dhrReport.trace(REPORT_EVENT_TYPE.PACKAGE_DELETE, record)
          operateActions(CONTRACT_PACKAGE.LIST.DELETE, record, successCallback)
        },
      },
    ]
    return actions
  }

  return [
    {
      dataIndex: 'name',
      title: '合同包名称',
      width: 200,
      fixed: 'left',
      cell: {
        type: 'Text',
        props: {
          ellipsis: true,
        },
      },
    },
    {
      dataIndex: 'status',
      title: '状态',
      cell: 'Status',
      options: [...STATUS],
    },
    {
      dataIndex: 'number',
      title: '合同包编号',
    },
    {
      title: '分类',
      dataIndex: 'isSupportInner',
      cell: {
        type: 'Text',
        render: ({ record }) => {
          const arr: string[] = []
          if (record.isSupportInner) {
            arr.push('朴朴员工合同包')
          }
          if (record.isSupportOuter) {
            arr.push('自营第三方合同包')
          }
          return arr.length ? arr.join('、') : '--'
        },
      },
    },
    {
      dataIndex: 'contractTemplateName',
      title: '合同模板',
      width: 200,
      cell: {
        type: 'Text',
        props: {
          ellipsis: true,
        },
      },
    },
    {
      dataIndex: 'relatedBusinessItemName',
      title: '关联业务项',
      width: 150,
      render: (value, record) =>
        record.relatedApplicationName
          ? `${record.relatedApplicationName}-${record.relatedBusinessItemName}`
          : record.relatedBusinessItemName,
    },
    {
      dataIndex: 'validDurationDays',
      title: '短链有效时长',
      width: 110,
      cell: {
        type: 'Text',
        props: {
          ellipsis: true,
        },
      },
      render: (value, record) => `${(record?.validDurationName || '--') + value}天`,
    },
    {
      dataIndex: 'userRangeName',
      title: '人员范围',
      width: 100,
      cell: {
        type: 'Text',
        props: {
          ellipsis: true,
        },
      },
    },
    {
      dataIndex: 'disableDate',
      title: '停用日期',
      cell: 'Date',
    },
    {
      dataIndex: 'timeCreate',
      title: '创建时间',
      cell: 'DateTime',
    },
    {
      dataIndex: 'userNameCreate',
      title: '创建人',
      width: 130,
      render: (value, record) => `${value || '--'}（${record.userIdCreate || '--'}）`,
    },
    {
      title: '操作',
      key: 'operator',
      width: 300,
      fixed: 'right',
      cell: {
        type: 'Operator',
        render: ({ record }) => (
          <>
            <PermissionAction code={CONTRACT_PACKAGE.LIST.VIEW} permissions={record.permissions}>
              <Button
                type="link"
                size="small"
                style={{
                  paddingLeft: 0,
                }}
                onClick={() => {
                  dhrReport.trace(REPORT_EVENT_TYPE.PACKAGE_VIEW, record)
                  localStorage.removeItem('PERSON_SCOPE_VALUE')
                  navigate(
                    `${baseRoute}/contract/basic-config/contract-package/view?id=${record.id}&isView=true`,
                  )
                }}
              >
                查看
              </Button>
            </PermissionAction>
            <PermissionAction code={CONTRACT_PACKAGE.LIST.EDIT} permissions={record.permissions}>
              <Button
                type="link"
                size="small"
                onClick={() => {
                  dhrReport.trace(REPORT_EVENT_TYPE.PACKAGE_EDIT, record)
                  // 移除人员设置范围缓存
                  removeValue()
                  navigate(
                    `${baseRoute}/contract/basic-config/contract-package/edit?id=${record.id}`,
                  )
                }}
              >
                编辑
              </Button>
            </PermissionAction>
            <PermissionAction
              code={CONTRACT_PACKAGE.LIST.DOWNLOAD_VAR}
              permissions={record.permissions}
            >
              <Button
                type="link"
                size="small"
                onClick={() => {
                  dhrReport.trace(REPORT_EVENT_TYPE.PACKAGE_VARIABLE_EXPORT, record)
                  downloadVar([record.id]).then(() => {
                    message.success('导出成功')
                    action.refresh()
                  })
                }}
              >
                导出变量
              </Button>
            </PermissionAction>
            <PermissionAction
              code={CONTRACT_PACKAGE.LIST.VERIFICATION_PERSONNEL}
              permissions={record.permissions}
            >
              <Tooltip title="可点击模拟是否能根据设置条件，找到对应人员。">
                <Button
                  type="link"
                  size="small"
                  onClick={() => {
                    handleLocalStorage(record)
                    window.open(
                      `${baseRoute}/contract/basic-config/contract-package/verification-personnel${
                        record.relatedBusinessItemCode
                          ? `?code=${record.relatedBusinessItemCode}`
                          : ''
                      }`,
                    )
                  }}
                >
                  校验人员
                </Button>
              </Tooltip>
            </PermissionAction>
            <PermissionDropdown
              actions={moreActions(record.status, record, () => {
                action.refresh()
              })}
              permissions={record.permissions}
            >
              更多
            </PermissionDropdown>
          </>
        ),
      },
    },
  ]
}

/**
 * 搜索项配置
 */
export const useSearchColumns = () => {
  const getRelated = async () => {
    const {
      data: { data },
    } = await getRelatedItems()
    const result: any = []
    data.forEach((item) => {
      const obj = {
        label: `${item.applicationName}-${item.businessItemName}`,
        value: `${item.applicationCode}-${item.businessItemCode}`,
      }
      result.push(obj)
    })
    return result
  }

  return [
    {
      dataIndex: 'name',
      title: '合同包名称',
      component: 'Input',
      props: {
        maxLength: 500,
        allowClear: true,
      },
    },
    {
      dataIndex: 'number',
      title: '合同包编号',
      component: 'Input',
      props: {
        allowClear: true,
      },
    },
    {
      dataIndex: 'status',
      title: '状态',
      component: 'Select',
      options: [
        {
          label: '全部',
          value: '',
        },
        ...STATUS,
      ],
    },
    {
      title: '分类',
      dataIndex: 'classification',
      component: 'Select',
      options: [
        {
          label: '朴朴员工合同包',
          value: 0,
        },
        {
          label: '自营第三方合同包',
          value: 1,
        },
      ],
      props: {
        mode: 'multiple',
      },
    },
    {
      dataIndex: 'contractTemplateName',
      title: '合同模板',
      component: 'Select',
      props: {
        showSearch: true,
        filterOption: (text, node) => node.label?.includes(text),
      },
      options: getContractTemplateList,
    },
    {
      dataIndex: 'relatedBusinessItemCode',
      title: '关联业务项',
      component: 'Select',
      options: getRelated,
      props: {
        placeholder: '请选择关联业务项',
        showSearch: true,
        allowClear: true,
        optionFilterProp: 'label',
      },
    },
    {
      dataIndex: 'userRangeName',
      title: '人员范围',
      component: 'Select',
      props: {
        placeholder: '请选择人员',
        showSearch: true,
        filterOption: (text, node) => node.label?.includes(text),
      },
      options: getUserRangeNameList,
    },
    {
      dataIndex: 'disableDate',
      title: '停用日期',
      component: 'RangePicker',
    },
  ]
}
