import React, { memo, useEffect, useImperativeHandle, useRef } from 'react'
import { SchemaTable, SchemaColumnType, Delete } from '@amazebird/antd-schema-table'
import { Empty } from 'antd'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { isEqual } from 'lodash-es'
import { getRangePeoplePage } from '@/api/basicConfig/package'
// 类型
import { RuleItem } from '@/types/package'
import { ExcludePeopleType } from './ConditionSet'
import styles from './style.module.less'

export type tableRefType = {
  refresh: () => void
  resetFields: () => void
  setTotal: (total: number) => void
}
type Props = {
  contractPackageId?: number
  isView?: boolean
  echoTotal?: (total: number) => any
  deleteAction?: (record: ExcludePeopleType) => any
  callBack?: (actionName: string) => void
  userRangeRules?: RuleItem[]
  onRef?: React.Ref<tableRefType>
}

// 人员范围
function RangePeople({
  isView,
  echoTotal,
  deleteAction,
  callBack,
  contractPackageId,
  userRangeRules,
  onRef,
}: Props) {
  const action = SchemaTable.createAction()
  const form = SchemaForm.createForm()
  const ruleRef = useRef<RuleItem[]>()
  // 指定人员
  const includeNumList = useRef<string[]>([])
  // 搜索参数
  const searchParams = useRef()

  // 请求列表
  const requestApi = async ({ pagination, filter }) => {
    // TODO: 没有条件不进行搜索 (等后端处理错误后放开)
    if (ruleRef.current === undefined || ruleRef.current.length === 0) {
      echoTotal?.(0)
      return {
        total: 0,
        data: [],
      }
    }
    const { current, pageSize } = pagination
    const params = {
      page: current,
      size: pageSize,
      contractPackageId,
      userRangeRules: ruleRef.current,
      ...filter,
    }
    searchParams.current = filter
    return getRangePeoplePage(params)
      .then(({ data: resData }) => {
        echoTotal?.(resData.count)
        return {
          success: true,
          data: resData.data,
          total: resData.count,
        }
      })
      .catch(() => {
        echoTotal?.(0)
        return {
          data: [],
          total: 0,
        }
      })
  }

  const searchColumns = [
    {
      dataIndex: 'keyword',
      title: '搜索',
      component: 'Input',
      placeholder: '请输入关键字',
      props: {
        onFocus: () => {
          callBack?.('focus')
        },
      },
    },
  ]

  // 表格列
  const columns: SchemaColumnType = [
    {
      dataIndex: 'num',
      title: '人员',
      cell: 'Text',
      render(value, record) {
        return `${value || '--'}:${record.name || '--'}`
      },
    },
    {
      dataIndex: 'departmentName',
      title: '部门',
      cell: 'Text',
    },
    {
      dataIndex: 'postName',
      title: '岗位',
      width: 120,
      cell: {
        type: 'Text',
        props: {
          ellipsis: true,
        },
      },
    },
    {
      dataIndex: 'postLevelName',
      title: '职位',
      cell: 'Text',
    },
    {
      dataIndex: 'employmentTypeName',
      title: '员工类型',
      cell: 'Text',
    },
    {
      title: '操作',
      key: 'operator',
      width: 60,
      cell: {
        type: 'Operator',
        render: ({ record }) => {
          const disabled = includeNumList.current.includes(record.num)
          return (
            <Delete
              title="提示"
              // content={disabled ? '指定人员不可移除！' : '移除后不可恢复，确定要移除吗？'}
              record={record}
              // disabled={disabled}
              onDelete={() => {
                // 不调用接口，传到ConditionResult去触发刷新列表
                deleteAction?.({
                  label: record.name,
                  value: record.num,
                  isExclude: disabled,
                })
              }}
              popconfirmProps={{
                placement: 'topRight',
              }}
            >
              移除
            </Delete>
          )
        },
      },
    },
  ]

  // 如果查看
  if (isView) {
    columns.pop()
  }

  useImperativeHandle(onRef, () => ({
    refresh: () => {
      // 判断关键字是否与之前一样
      if (!isEqual(searchParams.current, form.getFieldsValue(true))) {
        action.refresh()
      }
    },
    resetFields: () => form.resetFields(),
    setTotal: (total) => echoTotal?.(total),
  }))

  useEffect(() => {
    if (Array.isArray(userRangeRules)) {
      if (ruleRef.current && isEqual(ruleRef.current, userRangeRules)) {
        return
      }
      ruleRef.current = userRangeRules
      // 合同包编辑第一次，进来了，但不会触发refresh，需要setTimeout
      setTimeout(() => {
        action.refresh({ resetPage: true })
      })
      // 指定人员
      includeNumList.current =
        userRangeRules.find((item) => item.fieldName === 'includeNums')?.fieldValue?.split(',') ||
        []
    }
  }, [contractPackageId, userRangeRules])

  return (
    <div className={styles.scopeTable}>
      {userRangeRules === undefined || userRangeRules?.length === 0 ? (
        <Empty />
      ) : (
        <SchemaTable
          form={form}
          action={action}
          columns={columns}
          searchColumns={searchColumns}
          request={requestApi}
          searchProps={{
            labelWidth: 'auto',
          }}
          manualRequest
        />
      )}
    </div>
  )
}

export default memo(RangePeople)
