import React from 'react'
import { Input, Button } from 'antd'
import { RuleItem } from '@/types/package'
// 埋点
import dmsReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
// 组件
import { useNavigate } from 'react-router-dom'
import config from '@/config'
import PeopleScope from './PeopleScopeModal'
import { nameStyle } from '../common'

const { baseRoute } = config

type PeopleScopeProp = {
  isView: boolean
  value?: any
  packageId?: number
  userRangeRules?: RuleItem[]
  echoData?: (title: string, formData: []) => void
  values?: any
  businessItemCode?: string
  form?: any
}

// 输入框
function Index({
  isView,
  echoData,
  packageId,
  userRangeRules,
  values,
  form,
  businessItemCode,
  ...restProps
}: PeopleScopeProp) {
  const value = JSON.parse(localStorage.getItem('PERSON_SCOPE_VALUE') as string)?.name
  // const rules = useRef(userRangeRules)
  const navigate = useNavigate()
  // 打开人员范围弹窗
  const openPeopleScope = () => {
    let url = `${baseRoute}/contract/basic-config/contract-package/person-scope${
      businessItemCode ? `?code=${businessItemCode}` : ''
    }`

    if (isView) {
      dmsReport.trace(REPORT_EVENT_TYPE.PACKAGE_SCOPE_VIEW, values)
      url += '&readOnly=true'
    }
    // PeopleScope.show({
    //   title: value,
    //   id: packageId,
    //   userRangeRules: rules.current,
    //   onOk: (title, list) => {
    //     // 需要保存一下，schemaForm的component没有重新触发
    //     rules.current = list || []
    //     echoData?.(title, list) // 数据
    //   },
    // })
    localStorage.setItem('contractPackage', JSON.stringify(form?.getFieldsValue(true)))
    navigate(url)
  }

  // 查看
  const ViewCondition = () =>
    value ? (
      <Button type="link" style={nameStyle} onClick={openPeopleScope}>
        {value}
      </Button>
    ) : (
      <>--</>
    )

  return (
    <>
      {isView ? (
        <ViewCondition />
      ) : (
        <Input
          {...restProps}
          value={value}
          readOnly
          onClick={openPeopleScope}
          placeholder="请选择人员范围"
        />
      )}
      <PeopleScope isView={isView} />
    </>
  )
}
export default Index
