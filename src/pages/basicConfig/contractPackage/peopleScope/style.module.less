@import '@/themes/theme.less';

.titleBox {
    :global {
        .@{ant-prefix}-form-item {
            margin-bottom: 0
        }
    }
}

.editTitle {
    cursor: pointer;
    margin-left: 10px;
    vertical-align: middle !important;

    &:hover {
        color: @primary-color;
    }

    &.finish {
        color: @primary-color;
    }

    &.cancel {
        color: @error-color
    }
}

.inputTitle {
    display: inline-block;
    max-width: 500px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
}

.editInput {
    max-width: 340px;
}

// 查看人员范围
.scopeTable {
    :global {
        .schema-table-search {
            padding: 0;
        }

        .@{ant-prefix}-card-body {
            padding: 0;
        }
    }
}


// 步骤条
.stepBox {
    :global {
        .@{ant-prefix}-steps-item-title {
            color: #000000d9 !important;
            font-weight: 500;
        }

        .@{ant-prefix}-steps-item-description {
            padding-top: 12px;
            margin-bottom: 20px;
        }
    }

    &View {
        :global {
            .@{ant-prefix}-steps-item-tail {
                &:after {
                    background-color: @primary-color !important;
                }
            }

            .@{ant-prefix}-steps-item-icon {
                background-color: @primary-color;
                border-color: @primary-color;

                .@{ant-prefix}-steps-icon {
                    color: #fff;
                }
            }


        }

    }
}
