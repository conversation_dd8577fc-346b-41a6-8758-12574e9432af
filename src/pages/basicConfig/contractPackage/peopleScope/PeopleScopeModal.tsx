import React, { useEffect, useState, useRef, memo } from 'react'
import { Button, Modal, ModalProps, Steps } from 'antd'
import { isEqual } from 'lodash-es'
import classNames from 'classnames'
import { RuleItem } from '@/types/package'
// 接口
import { getContractPackage } from '@/api/basicConfig/package'
// 埋点
import dmsReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
// 组件
import ConditionSet, { ConditionRefType, ExcludePeopleType } from './ConditionSet'
import RangePeople, { tableRefType } from './RangePeople'
import ConditionResult from './ConditionResult'
// 样式

import styles from './style.module.less'
import UpdateName, { TitleRefType } from './UpdateName'

type Payload = {
  id?: number
  title?: string
  userRangeRules?: RuleItem[]
  onOk?: (name: string, formData: []) => void
}

type RuleProps = RuleItem[] | undefined

type PeopleScopeProps = ModalProps & { isView?: boolean }

type ModalType = React.NamedExoticComponent<PeopleScopeProps> & {
  show(payload?: Payload): void
}

const PeopleScope: ModalType = memo(
  ({ isView = false, ...restProps }: PeopleScopeProps) => {
    const [open, setOpen] = useState(false)
    const [disabled, setDisabled] = useState(!isView)
    const [total, setTotal] = useState(0)
    const [title, setTitle] = useState('')
    const payloadRef = useRef<Payload>({})
    const [activeKey, setActiveKey] = useState<number>(0)
    const [excludePeopleList, setExcludePeopleList] = useState<ExcludePeopleType[]>([])
    const [ruleTabs, setRuleTabs] = useState<Record<number, RuleProps>>({}) // 范围人员如果是undefined不请求接口，[]空数组请求接口（默认请求，加载全部）
    const conditionRef = useRef<ConditionRefType>(null)
    const titleRef = useRef<TitleRefType>(null)
    const tableRef = useRef<tableRefType>(null)
    const [contractPackageId, setContractPackageId] = useState<undefined | number>()
    const firstFlag = useRef<boolean>(false)

    const onChange = (_activeKey: number, isRefresh?: boolean) => {
      // 不是查看页
      if (!isView) {
        // 需要做一层对比
        // 点范围人员，需要请求接口，点击其他tabPane不允许请求
        const ruleData = conditionRef.current?.getData()
        // 存在搜索关键字变动，没有进行搜索
        if ([2, 3].includes(_activeKey) && !isEqual(ruleTabs[_activeKey], ruleData)) {
          setRuleTabs({
            ...ruleTabs,
            [_activeKey]: ruleData,
          })
        } else if (isRefresh) {
          // 用于【选择条件中查找人员】按钮的使用
          tableRef.current?.refresh()
        }
      }
      setActiveKey(_activeKey)
    }

    // 回显范围人员的总数
    const echoTotal = (num: number) => {
      setTotal(num)
    }

    // 移除人员
    const deleteAction = (record: ExcludePeopleType) => {
      if (record.isExclude) {
        // 移除的人员是否在指定人员里
        conditionRef.current?.removeUserId(record.value)
      } else {
        setExcludePeopleList([record])
      }
    }

    // 回调操作
    const echoAction = (actionName: string, _disabled = true) => {
      if (actionName === 'disabled') {
        if (title) {
          // 初始化的时候，有条件需要跳到 2
          if (firstFlag.current && !_disabled) {
            setActiveKey(2)
          } else {
            setActiveKey(1) // 造成来回跳动
          }
        }
        // 负责按钮禁用（若未设置人员条件，【确定】按钮置灰不可点击；）
        setDisabled(_disabled)
        firstFlag.current = false
      } else if (actionName === 'search') {
        if (activeKey !== 2) {
          // 不是查看人员范围，不清空表单筛选项
          tableRef.current?.resetFields()
        }
        onChange(2, true)
      } else if (actionName === 'clear') {
        setActiveKey(1)
        // 清空查看人员范围
        setRuleTabs({
          ...ruleTabs,
          2: [],
        })
      } else if (actionName === 'rules') {
        onChange(activeKey)
      }
    }

    const items = [
      {
        title: '设置人员范围名称',
        key: 0,
        description: (
          <UpdateName
            title={title}
            onRef={titleRef}
            isView={isView}
            callback={(actionName) => {
              if (actionName === 'focus') {
                onChange(0)
              }
            }}
          />
        ),
      },
      {
        title: '选择条件',
        key: 1,
        subTitle: isView ? (
          <span style={{ fontSize: '12px' }}>
            已选择
            <Button type="link" size="small" style={{ cursor: 'text' }}>
              {ruleTabs[1]?.length}
            </Button>
            个条件
          </span>
        ) : (
          ''
        ),
        description: isView ? (
          <ConditionResult rules={ruleTabs[1]} />
        ) : (
          <ConditionSet
            onRef={conditionRef}
            userRangeRules={ruleTabs[1]}
            excludePeopleList={excludePeopleList}
            echoAction={echoAction}
          />
        ),
      },
      {
        title: `查看人员范围${total > 0 ? `（${total}）` : ''}`,
        key: 2,
        description: (
          <RangePeople
            onRef={tableRef}
            echoTotal={echoTotal}
            deleteAction={deleteAction}
            callBack={(actionName) => {
              if (actionName === 'focus' && !isView) {
                onChange(2)
              }
            }}
            isView={isView}
            contractPackageId={contractPackageId}
            userRangeRules={ruleTabs[2]}
          />
        ),
        forceRender: true,
      },
    ]

    // 取消
    const onCancel = () => {
      setTotal(0)
      // 需要先清空，再关闭弹窗
      setRuleTabs({})
      // 不能去掉setTimeout，否则的话，修改后点取消不会触发ConditionSet组件的userRangeRules
      setTimeout(() => {
        setOpen(false)
      })
    }

    const onOk = async () => {
      if (isView) {
        onCancel()
        return
      }
      const data = await titleRef.current?.validField()
      // 获取数据
      conditionRef.current
        ?.validField()
        .then((list) => {
          dmsReport.trace(REPORT_EVENT_TYPE.PACKAGE_SCOPE_CONFIRM, list)
          // 回调
          payloadRef.current.onOk && payloadRef.current.onOk(data?.title as string, list)
          setOpen(false)
        })
        .catch(() => {
          setActiveKey(1)
        })
    }

    // 初始化，需要给 条件选择，范围人员赋值
    const initRules = (userRangeRules: RuleProps): Record<string, RuleProps> | undefined => {
      if (userRangeRules === undefined || userRangeRules.length === 0) {
        return undefined
      }
      const ruleTab: Record<string, RuleProps> = {}
      items?.forEach((item) => {
        if (isView || ![0, 3].includes(item.key)) {
          // 如果是查看详情，都赋值；新增编辑可以通过onChange去赋值
          ruleTab[item.key] = userRangeRules
        }
      })
      setRuleTabs(ruleTab)
      return ruleTab
    }

    useEffect(() => {
      const lastShow = PeopleScope.show
      PeopleScope.show = (payload?: Payload) => {
        setOpen(true)
        setActiveKey(isView ? -1 : 1)
        firstFlag.current = true
        if (payload) {
          const id = payload.id
          setTitle(payload.title || '人员范围')
          id && setContractPackageId(id)
          if (isView && id) {
            // 第一次调用接口
            getContractPackage(id).then(({ data: { data } }) => {
              if (data.userRangeRules) {
                initRules(data.userRangeRules)
              }
              data.userRangeName && setTitle(data.userRangeName)
            })
          } else {
            // 编辑/新增
            const userRules = initRules(payload.userRangeRules)
            if (userRules === undefined) {
              setRuleTabs({
                2: [],
              })
            }
          }
          payloadRef.current = payload
        }
      }

      return () => {
        PeopleScope.show = lastShow
      }
    }, [])

    return (
      <Modal
        title="人员范围"
        centered
        style={{
          position: 'absolute',
          top: '100px',
          left: 'calc(50% - 550px)',
        }}
        width={1100}
        open={open}
        onCancel={onCancel}
        {...restProps}
        cancelText={isView ? () => null : undefined}
        okText={isView ? '关闭' : undefined}
        onOk={onOk}
        okButtonProps={{
          disabled,
        }}
        destroyOnClose // 标题需要重新渲染
      >
        <Steps
          className={classNames(styles.stepBox, isView ? styles.stepBoxView : undefined)}
          current={activeKey}
          // size="small"
          direction="vertical"
          items={items}
        />
      </Modal>
    )
  },
  () => true,
) as any

export default PeopleScope
