import React, { memo, useImperativeHandle } from 'react'
import { SchemaForm, Item } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/antd-schema-form'
import styles from './style.module.less'

export type TitleRefType = {
  validField: () => Promise<{ title: string }>
}

// 人员范围-标题编辑
function UpdateName({
  isView,
  title,
  onRef,
  callback,
}: {
  isView: boolean
  title: string
  onRef?: React.Ref<TitleRefType>
  callback?: (actionName: 'focus') => void
}) {
  const form = SchemaForm.createForm()

  const schema: SchemaType = {
    title: {
      label: '',
      component: 'Input',
      placeholder: '请输入人员范围名称',
      required: [true, '请输入人员范围名称'],
      props: {
        maxLength: 50,
        allowClear: true,
        onFocus: () => {
          callback?.('focus')
        },
      },
      max: 50,
    },
  }

  useImperativeHandle(onRef, () => ({
    validField: () =>
      new Promise((resolve, reject) => {
        form
          .validateFields()
          .then((formData: any) => {
            // 处理数据
            resolve(formData)
          })
          .catch(() => {
            reject()
          })
      }),
  }))

  const Title = (
    <SchemaForm form={form} initialValues={{ title }} schema={schema} className={styles.titleBox}>
      <Item
        field="title"
        label=""
        colon={false}
        wrapperCol={{
          flex: '0 0 350px',
        }}
      />
    </SchemaForm>
  )

  return isView ? <span style={{ color: '#000' }}>{title}</span> : Title
}

export default memo(UpdateName)
