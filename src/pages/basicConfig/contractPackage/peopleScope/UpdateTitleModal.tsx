import React, { useState } from 'react'
import { Modal } from 'antd'
import { EditOutlined } from '@ant-design/icons'
import type { SchemaType } from '@amazebird/antd-schema-form'
import { SchemaForm, Item } from '@amazebird/antd-schema-form'
import styles from './style.module.less'

// 人员范围-标题编辑
function UpdateTitle({
  isView,
  title,
  callBack,
}: {
  isView: boolean
  title: string
  callBack?: (title: string) => void
}) {
  const [open, setOpen] = useState(false)
  const form = SchemaForm.createForm()

  // 表单
  const schema: SchemaType = {
    name: {
      label: '范围名称',
      component: 'Input',
      required: true,
      type: 'string',
      max: 50,
    },
  }

  // 打开弹窗
  const openModal = () => {
    // 清空
    form.resetFields()
    setOpen(true)
  }

  // 标题修改完成
  const onFinish = async () => {
    const { name } = await form.validateFields(['name'])
    setOpen(false)
    callBack?.(name)
  }

  return (
    <>
      {title}
      {!isView && (
        <>
          <EditOutlined className={styles.editTitle} onClick={openModal} />
          <Modal
            title="范围名称"
            centered
            onOk={onFinish}
            onCancel={() => setOpen(false)}
            width={450}
            open={open}
          >
            <SchemaForm form={form} schema={schema}>
              <Item
                field="name"
                labelCol={{
                  span: 6,
                }}
                wrapperCol={{
                  span: 18,
                }}
              />
            </SchemaForm>
          </Modal>
        </>
      )}
    </>
  )
}

export default UpdateTitle
