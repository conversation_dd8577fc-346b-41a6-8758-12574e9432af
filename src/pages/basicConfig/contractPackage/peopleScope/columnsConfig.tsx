import { Observer } from '@amazebird/antd-schema-form'
import { TreeSelect as Dict } from '@galaxy/dict'
import { message } from 'antd'
import type { SchemaType } from '@amazebird/antd-schema-form'
import config from '@/config'
import { RuleItem } from '@/types/package'
import { maxTagPlaceholder } from '@/utils/utils'
// 组件
import UserFilterSelect from '@/components/business/UserFilterSelect'
import OrgsFilterSelect from '@/components/business/OrgsFilterSelect'
// 接口
import { getPositionList } from '@/service/package'

const { tenantId } = config

// 排除人员和指定人员
const ObserverProp = (str: string | string[]) =>
  Observer({
    // TODO: 搜索时支持输入多个姓名或工号，用“，”隔开，不区分“，”全角或半角（后端）
    watch: str,
    action: (value = []) => {
      // String 不能去掉，不然互斥不了
      const filterUsers = value.map((ele) => String(ele.value || ele)) // 初始化，可能不为对象数组，可能为字符串数组
      return {
        showSearch: true,
        multiple: true,
        labelInValue: true,
        autoClearSearchValue: false,
        maxTagCount: 'responsive',
        maxTagPlaceholder: maxTagPlaceholder(),
        filterUsers,
        key: JSON.stringify(filterUsers), // TODO 需要优化
      }
    },
  })

// 下拉多选
const multipleProps = (extraProps = {}) => ({
  props: {
    mode: 'multiple',
    showArrow: true,
    labelInValue: true,
    showSearch: true,
    multiple: true,
    allowClear: true,
    autoClearSearchValue: false,
    filterOption: (search, node) => node.label?.includes(search),
    maxTagCount: 'responsive',
    maxTagPlaceholder: maxTagPlaceholder(),
    ...extraProps,
  },
})

// 字典
const dictProps = (code: string, placeholder: string) => ({
  code,
  scope: ['EHR'],
  tenantId,
  placeholder: `请选择${placeholder}`,
  multiple: true,
})

// 表单
export const schema: SchemaType = {
  employmentForms: {
    label: '用工形式',
    component: Dict,
    // required: true,
    ...multipleProps(dictProps('BM_35', '用工形式')),
  },
  employmentTypes: {
    label: '员工类型',
    component: Dict,
    ...multipleProps(dictProps('BM_YGLX', '员工类型')),
  },
  departments: {
    label: '部门',
    component: OrgsFilterSelect,
    ...multipleProps({
      showSearch: true,
      placeholder: '请选择部门',
    }),
  },
  sequences: {
    label: '序列',
    component: Dict,
    ...multipleProps(dictProps('BM_sequence', '序列')),
  },
  postLevels: {
    label: '职位',
    component: Dict,
    ...multipleProps(dictProps('BM_ZWDJ', '职位')),
  },
  positionBases: {
    label: '岗位',
    component: 'Select',
    ...multipleProps({
      placeholder: '请选择岗位',
    }),
    options: getPositionList,
  },
  excludeNums: {
    label: '排除人员',
    component: UserFilterSelect,
    props: ObserverProp('includeNums'),
    placeholder: '请选择排除人员',
  },
  includeNums: {
    label: '指定人员',
    component: UserFilterSelect,
    props: ObserverProp('excludeNums'),
    placeholder: '请选择指定人员',
  },
  contractPackageUserRangeRules: {
    component: 'ArrayTable',
    type: 'array',
    props: {
      columns: [
        {
          key: 'fieldName',
          title: '字段名称',
          required: true,
          width: 300,
        },
        {
          key: 'conditionSymbol',
          title: '符号',
          required: true,
          width: 100,
        },
        {
          key: 'fieldValue',
          title: '字段值',
          required: true,
        },
        {
          key: 'operation',
          title: '操作',
          width: 100,
          cellType: 'operation',
          align: 'center',
        },
      ],
      operations: [
        {
          key: 'delete',
          onClick: ({ remove }, index, fields) => {
            if (fields.length <= 1) {
              message.warn('最少需要1个自定义条件')
              return
            }
            remove(index)
          },
        },
        'drag',
        {
          key: 'add',
          inline: true, // 添加的行都是最后一行
          onClick: ({ add }, _, fields) => {
            if (fields.length >= 10) {
              message.warn('最多添加10条自定义条件')
            } else {
              add({})
            }
          },
        },
      ],
    },
    item: {
      type: 'object',
      fields: {
        fieldName: {
          component: 'Select',
          placeholder: '请选择字段名称',
          options: [
            {
              label: '111',
              value: '1',
            },
            {
              label: '333',
              value: '3',
            },
          ],
          required: [true, '请选择字段名称'],
          props: {
            labelInValue: true,
          },
        },
        conditionSymbol: {
          component: 'Select',
          placeholder: '请选择符号',
          options: [
            {
              label: '等于',
              value: '=',
            },
            {
              label: '不等于',
              value: '!=',
            },
          ],
          required: [true, '请选择符号'],
        },
        fieldValue: {
          component: 'Select',
          placeholder: '请选择字段值',
          options: [
            {
              label: '111',
              value: '1',
            },
            {
              label: '333',
              value: '3',
            },
          ],
          required: [true, '请选择字段值'],
          ...multipleProps(),
        },
      },
    },
  },
}

// 获取对应中文和值
const getValueAndCn = (fieldVal) => {
  const type = Object.prototype.toString.call(fieldVal).slice(8, -1)
  let fieldValue: any
  let fieldValueCn: any
  switch (type) {
    case 'Array':
      fieldValue = []
      fieldValueCn = []
      fieldVal.forEach((element) => {
        fieldValue.push(element.value)
        fieldValueCn.push(element.label)
      })
      fieldValue = fieldValue.join(',')
      fieldValueCn = fieldValueCn.join(',')
      break
    case 'Object':
      // 对象
      fieldValue = fieldVal.value
      fieldValueCn = fieldVal.label
      break
    default:
      fieldValue = fieldVal
      fieldValueCn = fieldVal
      break
  }
  return {
    fieldValue,
    fieldValueCn,
  }
}

// 重新组装数据
export const rebuildData = (formData, isFilterEmpty = true) => {
  const userRangeRules: RuleItem[] = []
  Object.keys(formData).forEach((key) => {
    if (key === 'contractPackageUserRangeRules' && Array.isArray(formData[key])) {
      // 自定义条件
      formData[key].forEach((item) => {
        const { fieldValue, fieldValueCn } = getValueAndCn(item.fieldValue)
        fieldValue &&
          userRangeRules.push({
            fieldName: item.fieldName?.value,
            fieldNameCn: item.fieldName?.label,
            conditionSymbol: item.conditionSymbol,
            fieldValue,
            fieldValueCn,
            conditionType: 1,
          })
      })
    } else {
      const { fieldValue, fieldValueCn } = getValueAndCn(formData[key])
      // 固定条件
      if (fieldValue || !isFilterEmpty) {
        userRangeRules.push({
          fieldName: key,
          fieldNameCn: String(schema[key]?.label),
          fieldValue,
          fieldValueCn,
          conditionType: 0,
          conditionSymbol: '=', // 固定条件，符号默认为等于
        })
      }
    }
  })
  return userRangeRules
}
