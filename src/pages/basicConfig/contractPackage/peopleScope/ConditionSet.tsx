import React, { useImperativeHandle, useEffect, useState } from 'react'
import { SchemaForm, Item, FORM_MODE, FormItemGrid } from '@amazebird/antd-schema-form'
import '@amazebird/antd-field'
import { debounce } from 'lodash-es'
// 组件
import { Button, Space } from 'antd'
// 类型
import { RuleItem } from '@/types/package'
import { schema, rebuildData } from './columnsConfig'

export type ConditionRefType = {
  getData: () => RuleItem[]
  validField: () => Promise<any>
  removeUserId: (id: string) => any
}

export type ExcludePeopleType = { label: string; value: string; isExclude?: boolean }

function Condition({
  onRef,
  excludePeopleList = [],
  userRangeRules,
  echoAction,
}: {
  excludePeopleList: ExcludePeopleType[]
  userRangeRules?: RuleItem[]
  echoAction?: (
    action: 'disabled' | 'search' | 'focus' | 'clear' | 'rules',
    disabled?: boolean,
  ) => void
  onRef?: React.Ref<ConditionRefType>
}) {
  const form = SchemaForm.createForm()
  const [disabled, setDisabled] = useState(true)
  const initValues = {}

  // 监听 excludePeopleList
  useEffect(() => {
    if (excludePeopleList) {
      // 获取原有的排除人员
      const excludePeople = form.getFieldValue('excludeNums') || []
      // 需要把指定人员的去掉
      const includeNums = (form.getFieldValue('includeNums') || []).map((item) => item.value)
      const userNums = excludePeople.map((item) => item.value || item)
      let flag = false
      excludePeopleList.forEach((ele) => {
        // 不包括指定人员
        if (includeNums.includes(ele.value)) {
          return
        }
        if (!userNums.includes(ele.value)) {
          excludePeople.push(ele)
          flag = true
        }
      })
      if (flag) {
        form.setFieldsValue({
          excludeNums: excludePeople,
        }) // TODO: 有问题，下拉的数据和显示的数据中文不一样
        // 需要通知
        echoAction?.('rules')
      }
    }
  }, [excludePeopleList])

  // 初始化
  useEffect(() => {
    if (
      userRangeRules !== undefined &&
      Array.isArray(userRangeRules) &&
      userRangeRules.length > 0
    ) {
      const formData = {}
      // 赋值
      userRangeRules.forEach((rules) => {
        // 判断是否有值
        if (rules.fieldValue && schema[rules.fieldName] !== undefined) {
          const key = rules.fieldName
          // 判断是否多选
          if (key === 'contractPackageUserRangeRules') {
            // TODO: 自定义条件
          } else if (
            (schema[key].props as any)?.mode === 'multiple' ||
            ['excludeNums', 'includeNums'].includes(key)
          ) {
            // 固定条件
            const values = rules.fieldValue?.toString().split(',')
            const valueCns = rules.fieldValueCn?.toString().split(',')
            formData[key] = values.map((value: string, index: number) => ({
              label: valueCns[index] || undefined,
              value: ['departments', 'positionBases'].includes(key) ? Number(value) : value, // 组织树需要转成number，不然部门会存在回填不上的问题
            }))
          } else {
            formData[key] = rules.fieldValue
          }
        }
      })
      const flag = Object.keys(formData).length === 0
      setDisabled(flag)
      // 判断是否有值
      echoAction?.('disabled', flag)
      form.setFieldsValue(formData)
    } else {
      form.resetFields()
      echoAction?.('disabled', true)
    }
  }, [userRangeRules])

  // 字段改变
  const onValuesChange = debounce(() => {
    const values = form.getFieldsValue(true)
    const flag = Object.values(values).some((value) => {
      if (Array.isArray(value)) {
        return value.length > 0
      }
      return !!value
    })
    setDisabled(!flag)
    echoAction?.('disabled', !flag)
    if (!flag) {
      echoAction?.('clear')
    }
  }, 200)

  useImperativeHandle(onRef, () => ({
    validField: () =>
      new Promise((resolve, reject) => {
        form
          .validateFields()
          .then((formData) => {
            // 处理数据
            resolve(rebuildData(formData))
          })
          .catch(() => {
            reject()
          })
      }),
    getData: () => rebuildData(form.getFieldsValue(true)),
    removeUserId: (num: string) => {
      const includeNums = form.getFieldValue('includeNums') || []
      if (includeNums.length > 0) {
        const arr = includeNums.filter((item) => {
          return item.value !== num
        })
        form.setFieldsValue({
          includeNums: arr,
        })
        echoAction?.('search')
      }
    },
  }))

  return (
    <SchemaForm
      form={form}
      schema={schema}
      mode={FORM_MODE.ADD}
      initialValues={initValues}
      labelCol={{
        flex: '0 0 80px',
      }}
      wrapperCol={{
        flex: '1 1 0',
      }}
      onValuesChange={onValuesChange}
    >
      <FormItemGrid colCount={3}>
        {Object.keys(schema)
          .filter((field) => field !== 'contractPackageUserRangeRules')
          .map((field) => (
            <Item field={field} key={field} />
          ))}
      </FormItemGrid>
      {/* <HeaderTitle title={'自定义条件'} marginLeft={'0'} />
      <Item field="contractPackageUserRangeRules" label=" " colon={false} labelCol={{ flex: '0 0 40px' }} /> */}
      <Space style={{ marginLeft: '80px', marginBottom: '2px' }} size="middle">
        <Button
          type="primary"
          disabled={disabled}
          onClick={() => {
            echoAction?.('search')
          }}
        >
          查找人员
        </Button>
        <Button
          disabled={disabled}
          onClick={() => {
            form.resetFields()
            onValuesChange() // 回调不触发，需要手动调用
          }}
        >
          清空条件
        </Button>
      </Space>
    </SchemaForm>
  )
}

export default Condition
