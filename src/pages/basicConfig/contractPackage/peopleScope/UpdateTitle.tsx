import React, { useState, useRef, useCallback, memo } from 'react'
import { Input, Tooltip } from 'antd'
import { EditOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons'
import classNames from 'classnames'
import styles from './style.module.less'

// 人员范围-标题编辑
function UpdateTitle({
  isView,
  title,
  callBack,
}: {
  isView: boolean
  title: string
  callBack?: (title: string) => void
}) {
  const [isEdit, setIsEdit] = useState(false)
  const inputRef = useRef<any>()
  const [msgTip, setMsgTip] = useState('')
  const [inputVal, setInputVal] = useState(title)

  const validTitle = (value) => {
    if (!value || value.length === 0) {
      setMsgTip('请输入范围名称')
      return false
    }
    if (value.length > 50) {
      setMsgTip('范围名称不能超过 50 个字符')
      return false
    }
    setMsgTip('')
    return true
  }

  // 赋值
  const onChange = (e) => {
    const val = e.target.value
    setInputVal(val)
    if (!validTitle(val)) {
      setTimeout(() => {
        inputRef.current.focus()
      })
    }
  }

  // 点击标题
  const onClick = useCallback(() => {
    setIsEdit(true)
    setTimeout(() => {
      inputRef.current.focus()
    })
  }, [])

  // 保存
  const onFinish = () => {
    if (!validTitle(inputVal)) {
      return
    }
    callBack?.(inputVal)
    setIsEdit(false)
  }

  // 取消
  const onCancel = () => {
    setIsEdit(false)
    setInputVal(title)
    setMsgTip('')
  }

  const ref = useCallback((_ref) => {
    if (_ref !== null) {
      inputRef.current = _ref
    }
  }, [])

  const Title = isEdit ? (
    <div className={styles.titleBox}>
      <Input
        ref={ref}
        maxLength={50}
        value={inputVal}
        onChange={onChange}
        allowClear
        className={styles.editInput}
        placeholder="请输入范围名称"
      />
      <CheckOutlined className={classNames(styles.editTitle, styles.finish)} onClick={onFinish} />
      <CloseOutlined className={classNames(styles.editTitle, styles.cancel)} onClick={onCancel} />
      {msgTip && <span className={styles.errorMsg}>{msgTip}</span>}
    </div>
  ) : (
    <>
      <Tooltip title={title}>
        <span onDoubleClick={onClick} className={styles.inputTitle}>
          {title}
        </span>
      </Tooltip>
      <EditOutlined className={styles.editTitle} onClick={onClick} />
    </>
  )

  return isView ? <span>{title}</span> : Title
}

export default memo(UpdateTitle)
