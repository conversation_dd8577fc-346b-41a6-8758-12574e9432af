import React, { memo } from 'react'
import { Typography } from 'antd'
import { RuleItem } from '@/types/package'

const { Title, Paragraph } = Typography

/**
 * 合同包-人员范围-已设置条件组件
 * @param rules 条件列表
 */
function ConditionResult({ rules = [] }: { rules?: RuleItem[] }) {
  return (
    <div>
      <Typography>
        {rules.length
          ? rules.map((rule) => (
              <>
                <Title level={5} style={{ fontSize: '14px', marginBottom: '12px' }}>
                  {rule.fieldNameCn}
                </Title>
                <Paragraph>{rule.fieldValueCn}</Paragraph>
              </>
            ))
          : '暂未设置条件'}
      </Typography>
    </div>
  )
}

export default memo(ConditionResult)
