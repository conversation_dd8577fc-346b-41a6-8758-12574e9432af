import React, { CSSProperties } from 'react'
import { Modal, message, Button } from 'antd'
import { useNavigate } from 'react-router-dom'
import modalWrapperHoc from '@/components/modalWrapperHoc'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
// 接口
import {
  disablePackage,
  enablePackage,
  deleteContractPackage,
  hasDisabledRelaData,
  enableContractPackage,
  getPackageByDeadlineRuleId,
  getPackageByTypeRuleId,
  downloadVar,
} from '@/api/basicConfig/package'
import { ActionProps } from '@/components/permissionDropdown'
import { CONTRACT_PACKAGE } from '@/constants/rbac-code'
// 组件
import { IPackageItem } from '@/types/package'
import config from '@/config'
import EnableModal from './EnableModal'

const { baseRoute } = config

// 导出变量的公共方法
export const exportVariable = (params: IPackageItem[]) => {
  dhrReport.trace(REPORT_EVENT_TYPE.PACKAGE_VARIABLE_BATCH_EXPORT, params)
  return downloadVar(params.map((item) => item.id))
}

export const operates: (status: number | string, isDetailPage?: boolean) => ActionProps[] = (
  status,
  isDetailPage = false,
) => {
  const isEnabled = String(status) === '1'
  const actionParams = isEnabled
    ? {
        component: '停用',
        code: CONTRACT_PACKAGE.LIST.DISABLED,
        danger: true,
      }
    : {
        component: '启用',
        code: CONTRACT_PACKAGE.LIST.ENABLED,
      }
  const actions = [
    {
      component: '导出变量',
      code: CONTRACT_PACKAGE.LIST.DOWNLOAD_VAR,
    },
    actionParams,
    {
      component: '删除',
      code: CONTRACT_PACKAGE.LIST.DELETE,
      danger: true,
    },
    {
      component: '校验人员',
      code: CONTRACT_PACKAGE.LIST.VERIFICATION_PERSONNEL,
    },
  ]
  if (!isDetailPage) {
    actions.pop()
  }
  return actions
}

// 更多
export const operateActions = (
  key: string,
  record: { id: number; name: string; status: number; [field: string]: any },
  successCallback?: (actionName: string) => void,
) => {
  // 启用
  if (key === CONTRACT_PACKAGE.LIST.ENABLED || key === CONTRACT_PACKAGE.LIST.DISABLED) {
    const status = key === CONTRACT_PACKAGE.LIST.ENABLED
    const execute = () => {
      const text = status ? '启用' : '停用'
      Modal.confirm({
        title: text,
        content: status
          ? `确定要启用【${record.name}】吗？`
          : '停用后将不能被引用，确定要停用合同包吗？',
        okText: '确定',
        cancelText: '取消',
        onOk() {
          const api = status ? enablePackage : disablePackage
          api(record.id).then(() => {
            message.success(`${text}成功`)
            successCallback?.(key)
          })
        },
      })
    }
    dhrReport.trace(
      status ? REPORT_EVENT_TYPE.PACKAGE_ENABLE : REPORT_EVENT_TYPE.PACKAGE_DEACTIVATE,
      record,
    )
    if (status) {
      hasDisabledRelaData(record.id).then(({ data: { data } }) => {
        // 是否存在模板
        if (data?.templates) {
          // 弹窗
          modalWrapperHoc(EnableModal)({
            defaultValues: {
              ...data,
              contractTemplateIds: data.templates.map((item) => item.contractTemplateId),
            },
            onOk: (values: any) =>
              new Promise((resolve, reject) => {
                enableContractPackage(record.id, values)
                  .then(() => {
                    successCallback?.(key)
                    message.success('启用成功')
                    resolve(true)
                  })
                  .catch(() => {
                    reject()
                  })
              }),
          })
        } else {
          execute()
        }
      })
    } else {
      execute()
    }
  } else if (key === CONTRACT_PACKAGE.LIST.DELETE) {
    dhrReport.trace(REPORT_EVENT_TYPE.PACKAGE_DELETE, record)
    Modal.confirm({
      title: '删除',
      content: '删除后不可恢复，确定要删除吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () =>
        deleteContractPackage(record.id).then(() => {
          successCallback?.(key)
          message.success('删除成功')
        }),
    })
  } else if (key === CONTRACT_PACKAGE.LIST.DOWNLOAD_VAR) {
    dhrReport.trace(REPORT_EVENT_TYPE.PACKAGE_VARIABLE_EXPORT, record)
    downloadVar([record.id]).then(() => {
      message.success('导出成功')
      successCallback?.(key)
    })
  } else if (key === CONTRACT_PACKAGE.LIST.VERIFICATION_PERSONNEL) {
    window.open(
      `${baseRoute}/contract/basic-config/contract-package/verification-personnel${
        record.originRelatedBusinessItemCode ? `?code=${record.originRelatedBusinessItemCode}` : ''
      }`,
    )
  }
}

// （合同类型规则，合同期限规则）跳转合同包
export const RedirectToPackagePage = ({
  number,
  id,
  type,
  record,
}: {
  number: number
  id: number
  type: 1 | 2 | 3
  record: any
}) => {
  // 跳转
  const navigate = useNavigate()

  const redirect = async () => {
    const apiRequest = {
      1: getPackageByDeadlineRuleId, // 合同期限
      2: getPackageByTypeRuleId, // 合同类型
    }
    if (type === 3) {
      dhrReport.trace(REPORT_EVENT_TYPE.VARIABLE_PACKAGE_VIEW, record)
      // 合同模板
      navigate(`${baseRoute}/contract/basic-config/contract-package?templateId=${id}`)
      return
    }
    if (type === 1) {
      dhrReport.trace(REPORT_EVENT_TYPE.DEADLINE_RULE_PACKAGE_VIEW, record)
    }
    if (type === 2) {
      dhrReport.trace(REPORT_EVENT_TYPE.TYPE_RULE_PACKAGE_VIEW, record)
    }
    if (apiRequest[type] !== undefined) {
      const {
        data: { data },
      } = await apiRequest[type](id)
      if (data.length > 0) {
        // 合同包编号
        const codes = data.map((item) => item.number || item.contractPackageNumber).join(',')
        navigate(`${baseRoute}/contract/basic-config/contract-package?codes=${codes}`)
      } else {
        message.info('关联的合同包为空，请尝试刷新列表！')
      }
    }
  }

  return number ? (
    <Button
      type="link"
      size="small"
      style={{
        paddingLeft: 0,
        verticalAlign: 'middle',
      }}
      onClick={() => redirect()}
    >
      {number}
      &nbsp;
      <span
        style={{
          color: 'rgba(0, 0, 0, 0.65)',
        }}
      >
        个合同包正使用
      </span>
    </Button>
  ) : (
    <>--</>
  )
}

export const nameStyle: CSSProperties = {
  padding: 0,
  whiteSpace: 'pre-wrap',
  textAlign: 'left',
}
