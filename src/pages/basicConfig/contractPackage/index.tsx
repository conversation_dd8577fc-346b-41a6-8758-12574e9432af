import React, { useEffect, useState } from 'react'
import { Button, message } from 'antd'
import { SchemaTable } from '@amazebird/antd-schema-table'
import { useStore } from '@/stores'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { useNavigate, useSearchParams } from 'react-router-dom'
import config from '@/config'
import { handleSort, stringTransform } from '@/utils/utils'
import { getContractPackagePage } from '@/api/basicConfig/package'
import useSelection from '@/hooks/useSelection'
import dhrReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import { CONTRACT_PACKAGE } from '@/constants/rbac-code'
import PermissionAction from '@/components/permissionAction'
import useCreateStore from '@/stores/useCreateStore'
import { IPackageItem } from '@/types/package'
import PeopleScope from './peopleScope/PeopleScopeModal'
import { useColumnsConfig, useSearchColumns } from './columnsConfig'
import { exportVariable } from './common'

const { baseRoute } = config

function ContractPackage() {
  const store = useStore((state) => state)
  const { setExtra } = store
  const action = SchemaTable.createAction()
  const form = SchemaForm.createForm()
  const [total, setTotal] = useState(0)
  const navigate = useNavigate()
  const [search] = useSearchParams()
  const codes = search.get('codes') || '' // 跳转过来的合同包编号
  const templateId = search.get('templateId') || '' // 跳转过来的合同模板
  const columns = useColumnsConfig({ action })
  const searchColumns = useSearchColumns()
  const { selectedRows, rowSelection } = useSelection<IPackageItem>()
  const permissions = useCreateStore((state: any) => state.permissions)
  const [loading, setLoading] = useState(false)

  // 请求列表
  const requestApi = ({ pagination, filter, sorter }) => {
    const {
      name,
      number,
      disableDate,
      userRangeName,
      status,
      contractTemplateName,
      relatedBusinessItemCode,
      // priority,
      classification,
    } = filter
    const { current, pageSize } = pagination
    const params: any = {
      page: current,
      size: pageSize,
      // contractPackageNameOrNumber: name,
      contractPackageName: stringTransform(name),
      contractPackageNumber: stringTransform(number),
      startDisableDate: disableDate?.[0]?.startOf('day')?.valueOf(),
      endDisableDate: disableDate?.[1]?.endOf('day')?.valueOf(),
      userRangeName,
      status,
      contractTemplateId: contractTemplateName,
      // priority,
    }
    if (relatedBusinessItemCode) {
      const related = relatedBusinessItemCode?.split('-')
      params.relatedApplicationCode = related[0]
      params.relatedBusinessItemCode = related[1]
    }
    if (classification) {
      if (classification.indexOf(0) !== -1) {
        params.isSupportInner = true
      }
      if (classification.indexOf(1) !== -1) {
        params.isSupportOuter = true
      }
    }
    params.sort = handleSort(sorter)
    return getContractPackagePage(params)
      .then(({ data: resData }) => {
        setTotal(resData.count)
        return {
          success: true,
          data: resData.data,
          total: resData.count,
        }
      })
      .catch(() => ({
        data: [],
        total: 0,
      }))
  }

  const getPermission = (code) => {
    if (selectedRows.length === 0) {
      // 未选择
      return [
        {
          code,
          hasPermission: false,
        },
      ]
    }
    // 已选择
    const hasPermission =
      selectedRows.findIndex(
        (i: any) => !i.permissions?.find((j) => j.code === code)?.hasPermission,
      ) === -1
    return [
      {
        code,
        hasPermission,
      },
    ]
  }

  // 创建按钮
  useEffect(() => {
    const CreateButton = (
      <PermissionAction code={CONTRACT_PACKAGE.CREATE} permissions={permissions}>
        <Button
          type="primary"
          onClick={() => {
            dhrReport.trace(REPORT_EVENT_TYPE.PACKAGE_CREATE, {})
            localStorage.removeItem('PERSON_SCOPE_VALUE')
            navigate(`${baseRoute}/contract/basic-config/contract-package/create`)
          }}
        >
          新增合同包
        </Button>
      </PermissionAction>
    )
    setExtra(CreateButton)
    return () => {
      setExtra(null)
    }
  }, [permissions])

  const getSearchParams = () => {
    if (codes) {
      return {
        number: codes,
      }
    }
    if (templateId) {
      return {
        contractTemplateName: Number(templateId),
      }
    }
    return {
      status: 1,
    }
  }

  return (
    <div
      style={{
        backgroundColor: 'white',
      }}
    >
      <SchemaTable
        form={form}
        action={action}
        columns={columns}
        searchColumns={searchColumns}
        request={requestApi}
        toolbar={{
          title: <span>{`合同包（共 ${total} 个）`}</span>,
          action: (
            <PermissionAction
              code={CONTRACT_PACKAGE.BATCH_DOWNLOAD_VAR}
              permissions={getPermission(CONTRACT_PACKAGE.BATCH_DOWNLOAD_VAR)}
            >
              <Button
                type="primary"
                loading={loading}
                onClick={() => {
                  if (selectedRows.length > 10) {
                    message.warning('导出数据限制最多10条')
                    return
                  }
                  setLoading(true)
                  exportVariable(selectedRows)
                    .then(() => {
                      message.success(
                        selectedRows.length === 10 ? '正在导出数据，请稍候…' : '导出成功',
                      )
                    })
                    .finally(() => setLoading(false))
                }}
              >
                导出变量
              </Button>
            </PermissionAction>
          ),
        }}
        searchProps={{
          initialValues: getSearchParams(),
        }} // 默认状态启用中,
        toolbarOptions={{
          setting: true,
        }}
        rowSelection={rowSelection}
      />
      <PeopleScope isView />
    </div>
  )
}

export default ContractPackage
