import React, {
  useState,
  useRef,
  useMemo,
  useEffect,
  useCallback,
  useImperativeHandle,
} from 'react'
import { Form, Spin, Tooltip } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { isEqual, isEmpty } from 'lodash-es'
import { RuleBuilder as RuleComponent } from '@pupu/brick'
import { RuleOptionsType, RulesDataGroup } from '@pupu/brick/lib/components/RuleBuilder/types'

import { PersonScopeMap, ConditionSelectGroupTooltip } from '@/constants/package'
import { getSettingPersonOptionsApi } from '@/api/basicConfig/package'
import RuleControl from '@/components/RuleControl'
import styles from './style.module.less'

// 特殊数据库类型
const specialSymbol = ['等于', '不等于']

// 连接符
const concatSymbol = '_'

interface OriginOptions {
  conditionType: number
  controlType: string
  description: string
  dictionaryCode: string
  id: number
  leftName: string
  leftDescription: string
  rightName: string
  rightDescription: string
}

type IProps = {
  code: string
  onRef?: any
  readOnly?: boolean
  valueLabelMap?: Record<string, any>
  value?: RulesDataGroup
  onChange?: (value: RulesDataGroup) => void
} & typeof RuleComponent

// 初始化规则
export const defaultRule: RulesDataGroup = {
  condition: 'AND',
  key: 'root',
  rules: [{}],
}

const ConditionSelect = ({
  onRef,
  value = defaultRule,
  onChange,
  readOnly,
  code,
  ...rest
}: IProps) => {
  const [ruleOptions, setRuleOptions] = useState<RuleOptionsType[]>([])
  const [originOptions, setOriginOptions] = useState<OriginOptions[]>([])
  const keyRef = useRef<number>(0)
  const ruleRef = useRef<RulesDataGroup>(value) // 用来切换，保持原来的值
  // 用于转换值（antd 4.17.1 版本select组件label是string类型，高版本是object类型）
  const ruleMapRef = useRef({})
  const loadingRef = useRef<boolean>(true)
  const [builder] = RuleComponent.useRuleBuilder()

  // 获取值
  const getValue = useCallback(() => {
    // 处理 leftValueLabel 为 object
    let isPass = true
    // 过滤掉字段和规则为空的
    const handleLeftValueLabel = (ruleList, result: any = []) => {
      ruleList.forEach((item) => {
        const currentOriginOptions = originOptions.find((v) => v.leftName === item.leftValue)
        let content: any = null
        if (item.leftValue && item.operator && item.rightValue && !isEmpty(item.rightValue.value)) {
          const isArrayValue = Array.isArray(item?.rightValue?.value)

          content = {
            key: item.key,
            leftValue: item.leftValue,
            leftValueLabel:
              ruleMapRef.current[item.leftValue] ||
              item.leftValue.slice(item.leftValue.lastIndexOf(concatSymbol) + 1),
            operator: item.operator,
            rightValue: isArrayValue
              ? (item?.rightValue?.value || []).map((v) => v.value || v)
              : [item?.rightValue?.value?.value],
            rightValueLabel: isArrayValue
              ? (item?.rightValue?.value || []).map((v) => v.label || v)
              : [item?.rightValue?.value?.label],
            assignmentType: item?.rightValue?.type?.value,
            assignmentTypeName: item?.rightValue?.type?.label,
            controlType: currentOriginOptions?.controlType,
            conditionType: currentOriginOptions?.conditionType || null,
            dictionaryCode: currentOriginOptions?.dictionaryCode || null,
            rightControlType: item.rightControlType || item?.rightValue?.value?.controlType || null,
            rightDictionaryCode:
              item.rightDictionaryCode || item?.rightValue?.value?.dictionaryCode || null,
          }
        } else if (item.rules) {
          const rules = handleLeftValueLabel(item.rules)
          if (rules.length !== 0) {
            content = {
              key: item.key,
              condition: item.condition,
              rules,
            }
          }
        } else {
          isPass = false
        }

        // 不为空的字段
        if (content !== null) {
          result.push(content)
        }
      })
      return result
    }
    const ruleState = ruleRef.current
    if (ruleState) {
      const rulesList = handleLeftValueLabel(ruleState.rules)
      return {
        condition: ruleState.condition,
        key: ruleState.key,
        rules: rulesList,
        isPass: isPass && rulesList.length > 0,
      }
    }
    return ruleState
  }, [originOptions])

  // 用useImperativeHandle暴露一些外部ref能访问的属性
  useImperativeHandle(onRef, () => ({
    validate: () => {
      const { isPass } = getValue()
      return isPass
    },
    getValue: () => {
      const result: any = getValue()
      if (result && result.isPass !== undefined) {
        delete result.isPass
      }
      return result
    },
  }))

  // 用来重新渲染
  const keyId = useMemo(() => {
    // 等待这里处理完渲染

    const isSame = isEqual(ruleRef.current, value)
    if (!isSame) {
      ruleRef.current = value
      // 非受控，如果是变更历史回填，需要重新渲染
      keyRef.current += 1
      // 刷新
      return keyRef.current
    }
    return undefined
  }, [value, code])

  // 生成默认配置
  const generateDefaultConfig = ({ value: v, valueSetter }) => ({
    value: v,
    placeholder: '请输入值',
    onChange: (e, option) => {
      const targetValue = e?.target ? e.target.value : e
      const _value = targetValue
      // 字符串和数字的转换
      valueSetter(
        _value,
        Array.isArray(option) ? option.join(',') : option?.label || option?.fullName,
      )
    },
  })

  // 格式化条件选择配置
  const formatSetting = (data) => {
    const result: RuleOptionsType[] = []
    const originRuleMap: Record<string, string> = {}

    Object.keys(PersonScopeMap).forEach((key: string) => {
      // 分组：通用条件 或者 特殊条件
      const optionGroupName = PersonScopeMap[key] as string

      // 过滤掉既不属于当前组别的条件项
      const childrenList = (data || [])
        // 过滤掉既不属于当前组别的条件项
        .filter((i) => Number(i.conditionType) === Number(key))

      if (!childrenList || !childrenList.length) {
        return
      }

      result.push({
        label: (
          <div className={styles.groupNameWrapper}>
            <span>{optionGroupName}</span>
            <Tooltip title={ConditionSelectGroupTooltip[key]}>
              <QuestionCircleOutlined className={styles.icon} />
            </Tooltip>
          </div>
        ),
        value: key,
        disabled: true,
        children: childrenList.map((item) => {
          // 记录条件的原始名称，后续回显需要用到
          originRuleMap[item.leftName] = item.leftDescription

          // 是否为特殊值  通用 - 1  特殊 - 2
          const isSpecial = item.conditionType === 2

          return {
            // 存在相同子级，添一下父级名称
            label: item.leftDescription,
            key: item.leftName,
            value: item.leftName,
            title: item.leftDescription,
            tooltip:
              optionGroupName === '通用条件'
                ? '条件值来源：人员基础数据'
                : '条件值来源：需和业务数据映射，如此处【部门（变更后）】对应OA表单【异动后部门】',
            operators: specialSymbol,
            validateInput: (v) => !isEmpty(v),
            renderInput: (props) => (
              <Form.Item
                {...(props?.validating
                  ? {
                      validateStatus: 'error',
                      help: '请输入值',
                    }
                  : {})}
              >
                <RuleControl
                  options={isSpecial ? data : null}
                  isSpecial={isSpecial}
                  type={item.controlType}
                  code={item.dictionaryCode}
                  {...generateDefaultConfig(props)}
                />
              </Form.Item>
            ),
          }
        }),
      })
    })

    return { result, originRuleMap }
  }

  useEffect(() => {
    getSettingPersonOptionsApi(code).then(({ data }) => {
      const origin = data?.data
      setOriginOptions(origin)

      const { result, originRuleMap } = formatSetting(origin)

      loadingRef.current = false

      setRuleOptions(result)
      ruleMapRef.current = originRuleMap
    })
  }, [])

  // 逻辑处理，先展示loading
  if (loadingRef.current) {
    return <Spin />
  }

  return (
    <RuleComponent
      className={styles.conditionSelect}
      key={keyId}
      builder={builder}
      defaultRules={value}
      ruleOptions={ruleOptions}
      readOnly={readOnly}
      {...rest}
      onChange={(rule) => {
        ruleRef.current = rule
      }}
      ruleWidthConfig={['30%', '20%', '50%']}
    />
  )
}

export default ConditionSelect
