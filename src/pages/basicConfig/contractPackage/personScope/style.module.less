.personScope {
  margin: 20px;
  background-color: white;
  padding: 20px 0px;

  .header,
  .content {
    padding: 20px 20px;
  }

  .header {
    border-bottom: 1px solid #EBEBEB;
  }

  .validate {
    margin-right: 10px;
  }

  :global {
    .@{ant-prefix}-select.@{ant-prefix}-select-in-form-item {
      width: 100% !important;
    }

    .brick-rule-builder-rule--readOnly {
      margin-bottom: 0;
      line-height: 1.4;

      .@{ant-prefix}-space {
        width: 100%;

        .@{ant-prefix}-space-item:nth-of-type(1) {
          flex: 2;
        }

        .@{ant-prefix}-space-item:nth-of-type(2) {
          flex: 1;
        }

        .@{ant-prefix}-space-item:nth-of-type(3) {
          flex: 6;
        }
      }
    }

  }
}


.groupNameWrapper {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;

  .icon {
    position: absolute;
    right: 10px;
  }
}