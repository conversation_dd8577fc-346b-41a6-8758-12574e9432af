import React, { useEffect, useImperativeHandle, useMemo, useRef } from 'react'

import type { SchemaType } from '@amazebird/antd-schema-form'
import { SchemaForm, Item, FORM_MODE } from '@amazebird/antd-schema-form'
import { RulesDataGroup } from '@pupu/brick/lib/components/RuleBuilder/types'
import { usePersonScopeValue } from '@/hooks/usePersonScopeValue'
import { cloneDeep } from 'lodash-es'

import ConditionSelect, { defaultRule } from './ConditionSelect'
import styles from './style.module.less'

interface IProps {
  personScopeRef: any
  code: string
  readOnly: boolean
}

const PersonScope = ({ personScopeRef, code, readOnly }: IProps) => {
  const { getValue } = usePersonScopeValue()
  const form = SchemaForm.createForm()
  const keyRef = useRef(0)

  // 规则
  const ruleRef = React.useRef<{
    validate: () => boolean
    getValue: () => RulesDataGroup
  }>()

  useImperativeHandle(personScopeRef, () => ({
    getValue: async () => {
      const { name = '' } = (await form?.validateFields()) || {}
      const params = {
        name,
        rules: ruleRef.current?.getValue() || defaultRule,
      }
      return params
    },
    clearValue: () => {
      form?.setFieldsValue({
        name: '',
        rules: defaultRule,
      })
    },
  }))

  // 用来重新渲染
  const keyId = useMemo(() => {
    keyRef.current += 1
    // 刷新
    return keyRef.current
  }, [code])

  const schema: SchemaType = {
    name: {
      label: '人员范围名称',
      component: 'Input',
      max: 50,
      required: true,
    },
    rules: {
      label: '条件设置',
      component: (props) => (
        <ConditionSelect code={code} onRef={ruleRef} readOnly={readOnly} {...props} />
      ),
      rules: [
        {
          required: true,
          validator: () =>
            ruleRef.current?.validate()
              ? Promise.resolve()
              : Promise.reject(new Error('请配置规则')),
          validateTrigger: 'onBlur',
        },
      ],
    },
  }

  // 格式化规则
  const formatRule = (rules, isReadOnly) => {
    if (!Array.isArray(rules)) {
      return []
    }
    const useRules = cloneDeep(rules)
    ;(useRules as Array<any>).forEach((rule) => {
      if (rule.rules) {
        // eslint-disable-next-line no-param-reassign
        rule.rules = formatRule(rule.rules, isReadOnly)
        return
      }

      const isArrayValue = Array.isArray(rule?.rightValue || [])
      // 条件选择组件右侧包含两个表单（值类型 & 值），需要组合两个值传入
      // eslint-disable-next-line no-param-reassign
      rule.rightValue = {
        value: isArrayValue
          ? rule.rightValue.map((v, index) => ({
              value: v,
              label: rule.rightValueLabel[index],
            }))
          : {
              value: rule.rightValue,
              label: rule.rightValueLabel,
              controlType: rule.rightControlType,
              dictionaryCode: rule.rightdictionaryCode,
            },
        type: {
          value: rule.assignmentType,
          label: rule.assignmentTypeName,
        },
      }

      /**
       * 可读时组件只会根据 rightValueLabel 的值进行对应的文本显示，但是需求需要使用 条件类型 + 值文本显示
       * 因此只读时需要将两个内容进行拼接再赋值给 rightValueLabel
       */
      const labelWithType = rule.assignmentTypeName
        ? // eslint-disable-next-line no-irregular-whitespace
          `${rule.assignmentTypeName}　${rule.rightValueLabel.join(',')}`
        : rule.rightValueLabel.join(',')

      // eslint-disable-next-line no-param-reassign
      rule.rightValueLabel = isReadOnly ? labelWithType : rule.rightValueLabel
    })

    return useRules
  }

  // 初始化人员范围
  const initPersonScope = (isReadOnly) => {
    const personScope = getValue()
    if (!personScope) {
      return
    }

    const formatPersonScope = {
      name: personScope?.name,
      rules: {
        ...personScope?.rules,
        // 需要组装一下 labelInValue 要不然部分表单回显会有问题
        rules: formatRule(personScope?.rules.rules, isReadOnly),
      },
    }

    form?.setFieldsValue(formatPersonScope)
  }

  useEffect(() => {
    initPersonScope(readOnly)
    ;(form as any)?.setState({
      mode: readOnly ? FORM_MODE.DETAIL : FORM_MODE.MODIFY,
    })
  }, [readOnly])

  return (
    <div className={styles.personScope}>
      <SchemaForm form={form} schema={schema} key={keyId}>
        <Item field="name" required labelCol={{ span: 3 }} wrapperCol={{ span: 8 }} />
        <Item field="rules" required={false} labelCol={{ span: 3 }} wrapperCol={{ span: 16 }} />
      </SchemaForm>
    </div>
  )
}

export { defaultRule }

export default PersonScope
