import React, { useState } from 'react'
import { Mo<PERSON>, Alert } from 'antd'
import { SchemaForm, Item, SchemaType } from '@amazebird/antd-schema-form'
// 接口
import {
  getContractTemplate,
  getRelatedContractType,
  getRelatedContractTimeLimit,
} from '@/service/package'

const EnableModal = (props: any) => {
  const { visible, close, onOk: okClick, defaultValues } = props
  const form = SchemaForm.createForm()
  const [loading, setLoading] = useState(false)

  // 通用处理options
  const handleOptions = (options: any[], id: number, name: string) => {
    if (options.length === 0) {
      return []
    }
    const flag = options.some((item) => item.value === id)
    if (flag) {
      return options
    }
    options.push({
      label: name,
      value: id,
    })
    return options
  }

  const schema: SchemaType = {
    contractTemplateIds: {
      label: '合同模板',
      component: 'Select',
      required: true,
      options: getContractTemplate,
      props: {
        mode: 'multiple',
      },
    },
    relatedContractTypeRuleId: {
      label: '合同类型规则',
      component: 'Select',
      options: async () =>
        handleOptions(
          await getRelatedContractType(),
          defaultValues.relatedContractTypeRuleId,
          defaultValues.relatedContractTypeRuleName,
        ),
    },
    relatedContractDeadlineRuleId: {
      label: '合同期限规则',
      component: 'Select',
      options: async () =>
        handleOptions(
          await getRelatedContractTimeLimit(),
          defaultValues.relatedContractDeadlineRuleId,
          defaultValues.relatedContractDeadlineRuleName,
        ),
    },
  }

  const onOk = async () => {
    try {
      const values = await form?.validateFields()
      setLoading(true)
      okClick(values)
        .then(() => {
          close()
        })
        .finally(() => {
          setLoading(false)
        })
    } catch (error) {
      console.error(error)
    }
  }

  return (
    <Modal
      title="启用"
      open={visible}
      onCancel={close}
      onOk={onOk}
      width={650}
      okButtonProps={{
        loading,
      }}
    >
      <SchemaForm form={form} schema={schema} defaultValues={defaultValues}>
        <Alert
          message="启用前请先确认关联模板/规则，确定后系统将会移除被停用的数据！"
          type="warning"
          showIcon
          style={{
            marginBottom: '20px',
          }}
        />
        <Item field="contractTemplateIds" />
        <Item field="relatedContractTypeRuleId" />
        <Item field="relatedContractDeadlineRuleId" />
      </SchemaForm>
    </Modal>
  )
}

export default EnableModal
