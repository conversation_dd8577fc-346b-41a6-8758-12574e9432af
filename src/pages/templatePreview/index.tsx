import React, { useEffect, useState, useRef, useCallback } from 'react'
import { Empty, message } from 'antd'
import { useSearchParams } from 'react-router-dom'
import { toCamelCase } from '@galaxy/utils'
import { getBatchTemplate } from '@/api/template'
import PageLoading from '@/components/pageLoading'
import { getFileDataServiceApi } from '@/service/fileService'
import PdfEdit from '@/components/pdfEditor'
import styles from './style.module.less'

function TemplatePreview() {
  const [search] = useSearchParams()
  const [loading, setLoading] = useState(false)
  const [splitLineWidth, setSplitLineWidth] = useState(0)

  const pageIndexRef = useRef(0)
  const totalRef = useRef(0)
  const [total, setTotal] = useState(0)
  const errorMsgRef = useRef('')

  const numRef = useRef<{ [key: number]: number }>({})

  const [pdfList, setPdfList] = useState<{ file: any; name: string }[]>([])

  const docLoaded = (pageTotal: number, pageIndex?: number) => {
    pageIndexRef.current += 1
    totalRef.current += pageTotal
    if (pageIndex !== undefined) {
      numRef.current[pageIndex] = pageTotal
    }

    if (pageIndexRef.current === pdfList.length) {
      // 全部加载完，需要做一次累加
      Object.keys(numRef.current).forEach((num: any) => {
        if (num > 0) {
          numRef.current[num] += numRef.current[num - 1]
        }
      })
      setTotal(totalRef.current)
    }
  }

  const calcSpileLineWidth = () =>
    setSplitLineWidth(document.querySelector('.react-pdf__Document')?.clientWidth || 0)

  const pageHeaderParams = useCallback(
    (index) => ({
      total,
      pageIndex: index,
      preTotal: numRef.current[index - 1] || 0,
    }),
    [total, numRef],
  )

  const TemplateView = () =>
    pdfList.length > 0 ? (
      <>
        {pdfList.map((item: any, index) => (
          <React.Fragment key={item.id}>
            <div
              style={{
                height: '2px',
                backgroundColor: '#c7c7c7',
                position: 'relative',
                zIndex: '99',
                width: splitLineWidth,
                margin: '0 auto',
              }}
            />
            <PdfEdit
              readOnly
              key={item.id}
              useVirtual={false}
              useActionSlot={false}
              sealCode={item.sealCode}
              initBoxes={item.varsCoords}
              file={item.file}
              signType={item.signType}
              initSearch={item.initSearch}
              initFontParams={item.fontParams}
              pageHeaderConfig={pageHeaderParams(index)}
              docLoaded={docLoaded}
            />
          </React.Fragment>
        ))}
      </>
    ) : (
      <div
        style={{
          padding: '30px',
          backgroundColor: 'white',
        }}
      >
        <Empty description={errorMsgRef.current || '暂无数据'} />
      </div>
    )

  // 获取模板的变量
  const getTemplateVarsCoords: (
    titleMap: Record<string, string>,
    sealList?: any,
    variableList?: any,
  ) => [] = (titleMap = {}, sealList = [], variableList = []) => {
    const list: any = []

    const handleVar = (isSeal: boolean, item: any) => {
      const info = JSON.parse(item.info)
      const obj: any = {
        ...info,
      }

      if (isSeal) {
        obj.type = item.type
      } else {
        obj.title = titleMap[toCamelCase(item.variableCode)] || item.variableName
      }
      list.push(obj)
    }

    sealList.forEach((item: any) => {
      handleVar(true, item)
    })

    variableList.forEach((item: any) => {
      handleVar(false, item)
    })

    return list
  }

  useEffect(() => {
    const ids = search.get('id')
    if (ids) {
      setLoading(true)
      getBatchTemplate(ids)
        .then(async ({ data: { data } }) => {
          const fileList: any = []
          const templateNames: string[] = []
          const fileDataList = await Promise.all(
            data.map((item) => getFileDataServiceApi(item.fileId)),
          )

          data.forEach((item, index) => {
            try {
              const initSearch = [
                item?.templateContent?.sealKeyWord || '',
                item?.templateContent?.signatureKeyWord || '',
              ]
              fileList.push({
                id: item.id,
                signType: item.signType,
                sealCode: item.sealCode,
                fontParams: {
                  fontType: item.fontType,
                  fontSize: item.fontSize,
                },
                file: {
                  name: item.fileName,
                  data: `data:application/pdf;base64,${fileDataList[index]}`,
                },
                varsCoords: getTemplateVarsCoords(
                  item?.variableNameMap,
                  item?.templateContent?.sealList,
                  item?.templateContent?.variableList,
                ),
                initSearch, // 高亮
              })
            } catch (e) {
              templateNames.push(item.name)
            }
          })
          setPdfList(fileList)
          if (templateNames.length > 0) {
            const msg = `模板【${templateNames.join('、')}】，pdf文件加载失败!`
            message.error(msg)
            errorMsgRef.current = msg
          }
        })
        .finally(() => {
          setLoading(false)
        })
    }
  }, [])

  useEffect(() => {
    calcSpileLineWidth()
  }, [])

  return (
    <div className={styles.templatePreview}>{loading ? <PageLoading /> : <TemplateView />}</div>
  )
}

export default TemplatePreview
