.extra {
  position: relative;
  white-space: nowrap;
  height: 30px;

  .resendBtn {
    margin-right: 10px;
  }

  .back {
    position: absolute;
    top: -34px;
    right: 5px;
    cursor: pointer;
    color: #595959;

    .icon {
      margin-right: 8px;
    }
  }
}

.basicInfo {
  background-color: white;
  padding: 0 24px;
}

.content {
  margin: 16px 24px 0;
  background-color: white;
  padding: 24px;

  .infoItem {
    display: flex;
    align-items: baseline;
    margin-bottom: 20px;

    &.normal {
      display: block;
    }

    .key {
      display: inline-block;
      min-width: 150px;
      text-align: right;
      color: rgba(0, 0, 0, 0.4);
    }

    .value {
      word-break: break-all;
    }

    .fileName {
      cursor: pointer;
      color: #1abb9c;
      padding: 0;
      text-align: left;
      white-space: break-spaces;
    }

    .fileIcon {
      color: #333333;
      margin-right: 3px;
    }

    .attachment {
      display: flex;
      flex-wrap: wrap;
    }
  }

  .sectionWrapper {
    margin: 20px 0px 40px 0px;
  }
}

.title {
  display: flex;
  align-items: center;

  .status {
    margin-left: 8px;
    width: 52px;
    height: 22px;
    text-align: center;
    line-height: 22px;
    font-size: 12px;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 400;

    &.yet {
      color: #595959;
      background: rgba(0, 0, 0, 0.04);
      border: 1px solid rgba(0, 0, 0, 0.15);
    }

    &.done {
      color: #1abb9c;
      background: rgba(26, 187, 156, 0.1);
      border: 1px solid rgba(26, 187, 156, 0.4);
    }

    &.process {
      color: #1890ff;
      background: #e6f7ff;
      border: 1px solid #91d5ff;
    }

    .default {
      color: #595959;
      background: rgba(0, 0, 0, 0.04);
      border: 1px solid rgba(0, 0, 0, 0.15);
    }
  }
}
