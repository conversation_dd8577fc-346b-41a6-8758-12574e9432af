import React from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Button, message } from 'antd'
import { RollbackOutlined } from '@ant-design/icons'
import { isLocalNetwork } from '@/utils/utils'
import PermissionAction from '@/components/permissionAction'
import { resendApi, invalidApi, getSignDetail } from '@/api/thirdparty-sign-manage'
import { THIRDPARTY_SIGN_MANAGE } from '@/constants/rbac-code'
import { useSigned } from '@/hooks/useSigned'
// 埋点
import dmsReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import { OFFER_BUSINESS_CODE_LIFT, OFFER_BUSINESS_CODE_SWITCH } from '@/constants/sign-manage'
import moment from 'moment'
import { resendSign, invalidSign } from '../actions'

import styles from './styles.module.less'

interface IProps {
  data: any
  refresh: () => void
}

const Extra = (props: IProps) => {
  const { data, refresh } = props
  const { status } = data
  const navigate = useNavigate()
  const [search] = useSearchParams()
  const tabKey = search.get('tabKey')
  const { remove, cancel } = useSigned({ tabKey: tabKey || 'signed', isThirdparty: true })

  // 重发
  const handleResend = async (record, value) => {
    await resendApi({
      signId: record.id,
      ...value,
    })
    message.success('操作成功')
    refresh && refresh()
  }

  // 失效
  const handleInvalid = async (record, value) => {
    await invalidApi({
      signId: record.id,
      ...value,
    })
    message.success('操作成功')
    refresh && refresh()
  }

  // 根据类型判断是否到达生效日期
  // 主体换签解除的生效日期是合同到期日期
  // 主体换签切换的生效日期是合同开始日期
  const validateEffectiveDate = (record: any) => {
    if (record?.businessItemCode === OFFER_BUSINESS_CODE_LIFT) {
      return Number(record?.contractEndTime) < moment().valueOf()
    }
    return Number(record?.contractStartTime) < moment().valueOf()
  }

  const handleCancel = async (recordData: any) => {
    if (
      [OFFER_BUSINESS_CODE_LIFT, OFFER_BUSINESS_CODE_SWITCH].includes(
        recordData?.businessItemCode,
      ) &&
      validateEffectiveDate(recordData)
    ) {
      const id = recordData.id
      const {
        data: { data: mainDetail },
      } = await getSignDetail(id)
      if (mainDetail?.relatedRenewVisaSignFileName) {
        const {
          data: { data: renewDetail },
        } = await getSignDetail(mainDetail.relatedRenewVisaSignId)
        if (renewDetail?.status === 1) {
          // 【场景3】已签署“主体换签解除”、已签署“主体换签切换”——已到达生效日期
          message.error('主体换签切换到达生效日期，EHR已写入花名册，不可作废！')
          return
        }
      }
    }
    cancel(recordData.id, recordData.businessItemCode)
  }

  const dmsData = {
    tabKey,
    ...data,
  }

  return (
    <div className={styles.extra}>
      {(window.__POWERED_BY_QIANKUN__ || isLocalNetwork()) && (
        <span
          className={styles.back}
          onClick={() => {
            navigate(-1)
          }}
        >
          <RollbackOutlined className={styles.icon} />
          返回
        </span>
      )}
      {status === 0 && (
        <>
          <PermissionAction
            code={THIRDPARTY_SIGN_MANAGE.SIGNING.RESEND}
            permissions={data.permissions}
          >
            <Button
              type="primary"
              className={styles.resendBtn}
              onClick={() => {
                dmsReport.trace(REPORT_EVENT_TYPE.SIGNING_RESEND, dmsData)
                resendSign({
                  onOk: (values) => handleResend(data, values),
                })
              }}
            >
              重发
            </Button>
          </PermissionAction>
          <PermissionAction
            code={THIRDPARTY_SIGN_MANAGE.SIGNING.EXPIRED}
            permissions={data.permissions}
          >
            <Button
              type="primary"
              onClick={() => {
                dmsReport.trace(REPORT_EVENT_TYPE.SIGNED_FAILURE, dmsData)
                invalidSign({
                  record: data,
                  onOk: (values) => handleInvalid(data, values),
                })
              }}
            >
              失效
            </Button>
          </PermissionAction>
        </>
      )}
      {status === 1 && (
        <>
          {/* V1.5.0 屏蔽配置 */}
          {/* <PermissionAction
            code={THIRDPARTY_SIGN_MANAGE.SIGNED.REMOVE}
            permissions={data.permissions}
          >
            <Button
              type="primary"
              onClick={() => {
                dmsReport.trace(REPORT_EVENT_TYPE.DEFUNCT_RESCIND, dmsData)
                remove(data.id)
              }}
            >
              解除合同
            </Button>
          </PermissionAction> */}
          <PermissionAction
            code={THIRDPARTY_SIGN_MANAGE.SIGNED.CANCEL}
            permissions={data.permissions}
          >
            <Button
              type="primary"
              style={{
                marginLeft: 8,
              }}
              onClick={() => {
                dmsReport.trace(REPORT_EVENT_TYPE.OBSOLETE_VOID, dmsData)
                handleCancel(data)
              }}
            >
              作废
            </Button>
          </PermissionAction>
        </>
      )}
    </div>
  )
}

export default Extra
