import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Row, Col } from 'antd'
import { FileOutlined } from '@ant-design/icons'
import { SignDetailOtherProps } from '@/types/sign-manage'
import { OFFER_BUSINESS_CODE } from '@/constants/sign-manage'
// 埋点
import dmsReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import config from '@/config'
import moment from 'moment'
import classNames from 'classnames'

import styles from '../styles.module.less'

const { baseRoute } = config

interface IProps {
  tabKey: string
  data: SignDetailOtherProps
}

const SignInfo = ({ data, tabKey }: IProps) => {
  const navigate = useNavigate()

  const isSigned = data.status === 1 // 已签署
  const isSignVoid = data.status === 3 // 已作废

  return (
    <Row gutter={16}>
      <Col className={styles.infoItem} span={8}>
        <span className={styles.key}>发起人：</span>
        <span>
          {data.sponsorName
            ? `${data.sponsorName}${data.sponsorNum ? `（${data.sponsorNum}）` : ''}`
            : '--'}
        </span>
      </Col>
      <Col className={styles.infoItem} span={8}>
        <span className={styles.key}>发起签署时间：</span>
        <span>
          {data.timeCreate ? moment(data.timeCreate).format('YYYY-MM-DD HH:mm:ss') : '--'}
        </span>
      </Col>
      <Col className={classNames(styles.infoItem, styles.fileItem)} span={8}>
        <span className={styles.key}>签署文件：</span>
        {data.fileName ? (
          <>
            <FileOutlined className={styles.fileIcon} />
            <span
              className={data.businessItemName !== 'Offer签署' ? styles.fileName : ''}
              onClick={() => {
                if (data.businessItemName === 'Offer签署') {
                  return
                }
                dmsReport.trace(REPORT_EVENT_TYPE.SIGN_FILE_VIEW, {
                  ...data,
                  _tabKey: tabKey,
                })
                navigate(
                  `${baseRoute}/contract/sign-manage/thirdparty/preview?id=${data.id}&name=${data.fileName}&type=SignFile&tabKey=${tabKey}`,
                )
              }}
            >
              {data.fileName}
            </span>
          </>
        ) : (
          '--'
        )}
      </Col>
      {
        // 已作废、已签署
        (isSigned || isSignVoid) && (
          <Col className={styles.infoItem} span={8}>
            <span className={styles.key}>合同签订日期：</span>
            <span>
              {data.contractSignDate ? moment(data.contractSignDate).format('YYYY-MM-DD') : '--'}
            </span>
          </Col>
        )
      }
      {(data.relatedSignFileName || isSigned) && (
        <Col className={classNames(styles.infoItem, styles.fileItem)} span={8}>
          <span className={styles.key}>关联作废协议：</span>
          {data.relatedSignFileName ? (
            <>
              <FileOutlined className={styles.fileIcon} />
              <span
                className={
                  data.relatedBusinessItemCode === OFFER_BUSINESS_CODE ? '' : styles.fileName
                }
                onClick={() => {
                  if (data.relatedBusinessItemCode === OFFER_BUSINESS_CODE) {
                    return
                  }
                  navigate(
                    `${baseRoute}/contract/sign-manage/thirdparty/preview?id=${data.id}&name=${data.relatedSignFileName}&type=RelatedFile`,
                  )
                }}
              >
                {data.relatedSignFileName}
              </span>
            </>
          ) : (
            '--'
          )}
        </Col>
      )}
      {(data.relatedRenewVisaSignFileName || isSigned) && (
        <Col className={classNames(styles.infoItem, styles.fileItem)} span={8}>
          <span className={styles.key}>关联换签合同：</span>
          {data.relatedRenewVisaSignFileName ? (
            <>
              <FileOutlined className={styles.fileIcon} />
              <span
                className={
                  data.relatedBusinessItemCode === OFFER_BUSINESS_CODE ? '' : styles.fileName
                }
                onClick={() => {
                  if (data.relatedBusinessItemCode === OFFER_BUSINESS_CODE) {
                    return
                  }
                  navigate(
                    `${baseRoute}/contract/sign-manage/thirdparty/preview?id=${data.id}&name=${data.relatedRenewVisaSignFileName}&type=RelatedFile`,
                  )
                }}
              >
                {data.relatedRenewVisaSignFileName}
              </span>
            </>
          ) : (
            '--'
          )}
        </Col>
      )}
      {
        // 已签署
        isSigned && (
          <Col className={styles.infoItem} span={8}>
            <span className={styles.key}>是否生效：</span>
            <span>{data.isEffect ? '是' : '否'}</span>
          </Col>
        )
      }
    </Row>
  )
}

export default SignInfo
