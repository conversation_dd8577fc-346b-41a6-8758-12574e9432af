import React from 'react'
import { Row, Col, Button } from 'antd'
import { SchemaTable } from '@amazebird/antd-schema-table'
import { SignDetailOtherProps } from '@/types/sign-manage'
import moment from 'moment'
import classNames from 'classnames'

import config from '@/config'
import styles from '../styles.module.less'

const { baseRoute } = config
interface IProps {
  data: SignDetailOtherProps
}

const OtherInfo = ({ data }: IProps) => {
  const isSigning = data.status === 0 // 签署中
  const isSigned = data.status === 1 // 已签署
  const isSignExpired = data.status === 2 // 已失效
  const isSignVoid = data.status === 3 // 已作废

  const isRemove = data.invalidType === 'CM_FC_003' // 失效分类=解除到期失效

  const columns = [
    {
      title: '序号',
      width: 10,
      key: 'index',
      cell: ({ index }) => index + 1,
    },
    {
      title: '重发原因',
      dataIndex: 'reason',
      cell: 'Text',
    },
    {
      title: '重发时间',
      dataIndex: 'sendTime',
      cell: ({ record }) =>
        record.sendTime ? moment(record.sendTime).format('YYYY-MM-DD HH:mm:ss') : '--',
    },
  ]

  const DynamicComponent = () => {
    if (isRemove) {
      return (
        <>
          <Col className={styles.infoItem} span={8}>
            <span className={styles.key}>解除原因：</span>
            <span className={styles.value}>{data.removeTypeName || '--'}</span>
          </Col>
          <Col className={styles.infoItem} span={8}>
            <span className={styles.key}>解除备注：</span>
            <span className={styles.value}>{data.removeRemark}</span>
          </Col>
        </>
      )
    }
    if (data.invalidType === 'CM_FC_002') {
      return (
        <>
          <Col className={styles.infoItem} span={8}>
            <span className={styles.key}>失效原因：</span>
            <span className={styles.value}>{data.invalidReason || '--'}</span>
          </Col>
          <Col className={styles.infoItem} span={8}>
            <span className={styles.key}>失效备注：</span>
            <span className={styles.value}>{data.invalidRemark || '--'}</span>
          </Col>
        </>
      )
    }
    return null
  }

  return (
    <>
      <Row gutter={16}>
        <Col className={styles.infoItem} span={8}>
          <span className={styles.key}>数据来源：</span>
          <span className={styles.value}>{data.dataSource || '--'}</span>
        </Col>
        <Col className={styles.infoItem} span={8}>
          <span className={styles.key}>关联业务项：</span>
          <span className={styles.value}>{data.businessItemName || '--'}</span>
        </Col>
        <Col className={styles.infoItem} span={8}>
          <span className={styles.key}>关联申请单号：</span>
          <span className={styles.value}>{data.relatedApplyNumber || '--'}</span>
        </Col>
      </Row>
      {
        // 已失效
        isSignExpired && (
          <Row gutter={16}>
            <Col className={styles.infoItem} span={8}>
              <span className={styles.key}>失效分类：</span>
              <span className={styles.value}>{data.invalidTypeName || '--'}</span>
            </Col>
            <Col className={styles.infoItem} span={8}>
              <span className={styles.key}>{isRemove ? '解除时间：' : '失效时间：'}</span>
              <span className={styles.value}>
                {data.invalidTime ? moment(data.invalidTime).format('YYYY-MM-DD HH:mm:ss') : '--'}
              </span>
            </Col>
            {DynamicComponent()}
            {/* 附件 */}
            {data.invalidAttachment && (
              <Col className={styles.infoItem} span={8}>
                <span className={styles.key}>附件：</span>
                <div className={styles.attachment}>
                  {JSON.parse(data.invalidAttachment)?.map((item, index) => {
                    const extension = item.name?.split('.').pop().toLowerCase()
                    return (
                      <React.Fragment key={item.uuid}>
                        <Button
                          type="link"
                          style={{
                            padding: 0,
                            textAlign: 'start',
                          }}
                          target="_blank"
                          size="small"
                          href={`${baseRoute}/contract/sign-manage/thirdparty/preview?name=${item.name}&ext=${extension}&uuid=${item.uuid}`}
                        >
                          {item.name}
                        </Button>
                        {index < JSON.parse(data.invalidAttachment || '[]').length - 1 && (
                          <span>、</span>
                        )}
                      </React.Fragment>
                    )
                  })}
                </div>
              </Col>
            )}
          </Row>
        )
      }
      {
        // 已作废
        isSignVoid && (
          <Row gutter={16}>
            <Col className={styles.infoItem} span={8}>
              <span className={styles.key}>作废时间：</span>
              <span className={styles.value}>
                {data.cancelTime ? moment(data.cancelTime).format('YYYY-MM-DD HH:mm:ss') : '--'}
              </span>
            </Col>
            <Col className={styles.infoItem} span={8}>
              <span className={styles.key}>作废原因：</span>
              <span className={styles.value}>{data.cancelTypeName || '--'}</span>
            </Col>
            <Col className={styles.infoItem} span={8}>
              <span className={styles.key}>作废备注：</span>
              <span className={styles.value}>{data.cancelRemark || '--'}</span>
            </Col>
          </Row>
        )
      }
      {
        // 签署中
        isSigning && (
          <>
            <Row gutter={16}>
              <Col className={styles.infoItem} span={8}>
                <span className={styles.key}>重发次数：</span>
                <span className={styles.value}>
                  {data.reSendHistory ? data.reSendHistory.length : '--'}
                </span>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col className={classNames(styles.infoItem, styles.normal)} offset={1} span={23}>
                <SchemaTable
                  stripe
                  tableClassName="custom-table-class-name"
                  dataSource={data.reSendHistory || []}
                  columns={columns}
                  footer={() => null}
                  pagination={false}
                />
              </Col>
            </Row>
          </>
        )
      }
      {
        // 已签署且解除
        isSigned && data.type === 1 && data.removeTypeName && (
          <Row gutter={16}>
            <Col className={styles.infoItem} span={8}>
              <span className={styles.key}>解除原因：</span>
              <span className={styles.value}>{data.removeTypeName || '--'}</span>
            </Col>
          </Row>
        )
      }
      {
        // 已签署且作废
        isSigned && data.type === 1 && data.cancelTypeName && (
          <Row gutter={16}>
            <Col className={styles.infoItem} span={8}>
              <span className={styles.key}>作废原因：</span>
              <span className={styles.value}>{data.cancelTypeName || '--'}</span>
            </Col>
          </Row>
        )
      }
    </>
  )
}

export default OtherInfo
