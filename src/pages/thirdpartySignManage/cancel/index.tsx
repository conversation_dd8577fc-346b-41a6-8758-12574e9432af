import React, { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { message, Skeleton } from 'antd'
import { getTemplateList } from '@/api/template'
import {
  getTemplateAndVar,
  invalidContract,
  getVariablesApi,
  getSignDetail,
} from '@/api/thirdparty-sign-manage'
import {
  OFFER_BUSINESS_CODE_LIFT,
  OFFER_BUSINESS_CODE_SWITCH,
  TEMPLATE_TYPE,
} from '@/constants/sign-manage'
import { ActiveType, IComponentSlot } from '@/types/sign-manage'
import config from '@/config'
import moment from 'moment'
import Launch from '../launch'
import CancelForm from './Form'

const { baseRoute } = config

const Cancel = () => {
  const [search] = useSearchParams()
  const navigate = useNavigate()
  const [getTemplateLoading, setGetTemplateLoading] = useState(true)
  const [templateList, setTemplateList] = useState([])
  const [variables, setVariables] = useState([])
  const [relatedRenewContractNumber, setRelatedRenewContractNumber] = useState(undefined) // 关联换签合同编号，用于confirm弹窗

  // 获取模板列表
  const getTemplate = async () => {
    setGetTemplateLoading(true)
    try {
      const { data: result } = await getTemplateList({
        fddStatus: 1,
        type: TEMPLATE_TYPE.CANCEL,
        typeCode: TEMPLATE_TYPE.CANCEL,
        isSupportOuter: true,
        isShowSealKeyWord2: false,
      })

      const list = result?.data.map((v) => ({
        label: v.name,
        value: v.id,
      }))
      setTemplateList(list)
    } finally {
      setGetTemplateLoading(false)
    }
  }

  // 获取变量
  const getVariables = async () => {
    const { data: result } = await getVariablesApi()
    setVariables(result.data.filter((item) => item.isEnable))
  }

  // 根据类型判断是否到达生效日期(场景2)
  // 主体换签解除的生效日期是合同到期日期
  // 主体换签切换的生效日期是合同开始日期
  const validateEffectiveDate = (record: any) => {
    if (record?.businessItemCode === OFFER_BUSINESS_CODE_LIFT) {
      return Number(record?.contractEndTime) > moment().valueOf()
    }
    return Number(record?.contractStartTime) > moment().valueOf()
  }

  // 获取主体合同以及换签合同详情，并判断是否需要提示弹窗
  // 针对于【场景2】已签署“主体换签解除”、已签署“主体换签切换”——未到达生效日期
  // 并且满足 关联换签合同是否存在& 关联换签合同状态=已签署 & 关联换签合同的关联作废合同不存在
  const handleDetails = async () => {
    const businessItemCode = search.get('businessItemCode')
    if ([OFFER_BUSINESS_CODE_LIFT, OFFER_BUSINESS_CODE_SWITCH].includes(businessItemCode)) {
      const id = search.get('id')
      const {
        data: { data: mainDetail },
      } = await getSignDetail(id)
      if (mainDetail?.relatedRenewVisaSignFileName && validateEffectiveDate(mainDetail)) {
        const {
          data: { data: renewDetail },
        } = await getSignDetail(mainDetail.relatedRenewVisaSignId)
        if (renewDetail?.status === 1 && !renewDetail?.relatedSignId) {
          setRelatedRenewContractNumber(renewDetail?.contractNumber)
        }
      }
    }
  }

  useEffect(() => {
    getTemplate()
    handleDetails()
    getVariables()
  }, [])

  return (
    <Launch
      variables={variables}
      componentSlot={(props: IComponentSlot) =>
        !getTemplateLoading ? (
          <CancelForm {...props} templateList={templateList} />
        ) : (
          <Skeleton style={{ padding: '30px 20px' }} />
        )
      }
      relatedRenewContractNumber={relatedRenewContractNumber}
      apiRequest={(activeName: ActiveType) =>
        ({ packageId, remark, reasonCode, vars }: any) => {
          if (activeName === 'GET_TEMPLATE_CONTENT') {
            return getTemplateAndVar({
              signId: search.get('id') as string,
              templateId: packageId,
            })
          }
          if (activeName === 'RESULT') {
            const params = {
              signId: search.get('id') as string,
              templateId: packageId,
              remark,
              reasonCode,
              variables: vars,
            }

            return invalidContract(params).then(() => {
              message.success('保存成功')
              navigate(`${baseRoute}/contract/sign-manage/thirdparty?activeKey=signing`)
            })
          }
          return Promise.reject()
        }}
    />
  )
}

export default Cancel
