import React, { useState, useEffect } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { Ta<PERSON>, Button } from 'antd'
import type { TabsProps } from 'antd'
import { createAction } from '@amazebird/antd-schema-table'
import { getPermission } from '@galaxy/rbac'

import config from '@/config'
import { useStore } from '@/stores'
import useCreateStore from '@/stores/useCreateStore'
import { THIRDPARTY_SIGN_MANAGE } from '@/constants/rbac-code'
import PermissionAction from '@/components/permissionAction'
import Signed from './signed'
import Signing from './signing'
import SignVoid from './signVoid'
import SignExpired from './signExpired'

import styles from './styles.module.less'

const { baseRoute } = config

const thirdpartySignManage = () => {
  const store = useStore((state) => state)
  const { setExtra } = store

  const navigate = useNavigate()
  const [search, setSearch] = useSearchParams()
  const [activeKey, setActiveKey] = useState<any>(search.get('activeKey') || 'signing')
  const permissions = useCreateStore((state: any) => state.permissions)
  const actions = {
    signing: createAction(),
    signed: createAction(),
    signExpired: createAction(),
    signVoid: createAction(),
  }

  const items = [
    {
      key: 'signing',
      label: '签署中',
      children: <Signing action={actions.signing} tabKey="signing" />,
      hasPermission: getPermission(THIRDPARTY_SIGN_MANAGE.TABS.SIGNING),
    },
    {
      key: 'signed',
      label: '已签署',
      children: <Signed action={actions.signed} tabKey="signed" />,
      hasPermission: getPermission(THIRDPARTY_SIGN_MANAGE.TABS.SIGNED),
    },
    {
      key: 'signExpired',
      label: '已失效',
      children: <SignExpired action={actions.signExpired} tabKey="signExpired" />,
      hasPermission: getPermission(THIRDPARTY_SIGN_MANAGE.TABS.SIGN_EXPIRED),
    },
    {
      key: 'signVoid',
      label: '已作废',
      children: <SignVoid action={actions.signVoid} tabKey="signVoid" />,
      hasPermission: getPermission(THIRDPARTY_SIGN_MANAGE.TABS.SIGN_VOID),
    },
  ]

  const menu: TabsProps['items'] = items.filter((i: any) => i.hasPermission)

  const onChange = (key) => {
    setActiveKey(key)
    actions[key].refresh()
    setSearch((prev) => {
      prev.set('activeKey', key)
      return prev
    })
  }

  useEffect(() => {
    setExtra(
      <PermissionAction code={THIRDPARTY_SIGN_MANAGE.START_SIGN} permissions={permissions}>
        <Button
          type="primary"
          onClick={() => {
            navigate(`${baseRoute}/contract/sign-manage/thirdparty/launch`)
          }}
        >
          发起签署
        </Button>
      </PermissionAction>,
    )
    return () => {
      setExtra(null)
    }
  }, [permissions, activeKey])

  return (
    <div className={styles.thirdpartySignManage}>
      <Tabs defaultActiveKey="signing" items={menu} onChange={onChange} activeKey={activeKey} />
    </div>
  )
}

export default thirdpartySignManage
