import React, { useEffect, useState, useMemo } from 'react'
import { useStore } from '@/stores'
import { useSearchParams } from 'react-router-dom'
import { message, Button, Image } from 'antd'
import { getPermission } from '@galaxy/rbac'
// 埋点
import dmsReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'

import PdfEdit from '@/components/pdfEditor'
import PageLoading from '@/components/pageLoading'
import { downloadFile } from '@/utils/utils'
import {
  getSignFileApi,
  previewTemplateForLog,
  downloadTemplateForLog,
} from '@/api/thirdparty-sign-manage'
import { THIRDPARTY_SIGN_MANAGE } from '@/constants/rbac-code'

import UploadSdk from '@galaxy/upload'
import config from '@/config'
import styles from './style.module.less'

const { tenantId } = config

const uploadSdk = new UploadSdk({
  clientCode: 'CONTRACT',
  tenantId,
  oss: { service: '/admin/contract' },
})

const View = () => {
  const store = useStore((state) => state)
  const { setExtra } = store

  const [total, setTotal] = useState(0)
  const [search] = useSearchParams()
  const [loading, setLoading] = useState(true)
  const [file, setFile] = useState<string>('')
  const [fileUrl, setFileUrl] = useState('')

  const uuid = search.get('uuid')
  const ext = search.get('ext') || 'pdf'

  const docLoaded = (pageTotal: number) => setTotal(pageTotal)

  const getSignFile = async (id) => {
    const { data: result } = await getSignFileApi(id)
    setFile(`data:application/pdf;base64,${result.data}`)
  }

  const handleDownload = async () => {
    const condition = uuid ? fileUrl : file
    if (!condition) {
      message.warn('请等待文件加载完成')
      return
    }
    dmsReport.trace(REPORT_EVENT_TYPE.SIGN_FILE_DOWNLOAD, Object.fromEntries(search.entries()))
    if (uuid) {
      try {
        const response = await fetch(fileUrl)
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${search.get('name')}`
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)
      } catch (error) {
        console.error('下载文件失败:', error)
      }
    } else {
      downloadFile(file, `${search.get('name')}.pdf`)
      downloadTemplateForLog(search.get('id'))
    }
  }

  const getFileUrl = async (objectKey) => {
    const { url } = await uploadSdk.download(objectKey)
    return url
  }

  const handleDownloadImage = async () => {
    if (!fileUrl) {
      message.warn('请等待文件加载完成')
      return
    }
    try {
      const response = await fetch(fileUrl)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${search.get('name')}` // 设置下载文件名
      document.body.appendChild(link)
      link.click()
      link.remove()
      // 清理创建的 URL 对象
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('下载文件失败:', error)
    }
  }

  const pageHeaderParams = useMemo(
    () => ({
      total,
      preTotal: 0,
      pageIndex: 0,
    }),
    [total],
  )

  useEffect(() => {
    const id = search.get('id')
    if (!id && !uuid) {
      message.warn('缺少签署id或者合同/协议类型')
      return
    }
    if (uuid) {
      getFileUrl(uuid).then((url) => {
        setFileUrl(url)
      })
    } else {
      previewTemplateForLog(id)
      getSignFile(id)
    }
  }, [])

  useEffect(() => {
    const CreateButton = (
      <Button type="primary" onClick={ext === 'pdf' ? handleDownload : handleDownloadImage}>
        下载
      </Button>
    )

    setExtra(getPermission(THIRDPARTY_SIGN_MANAGE.SIGN_FILE.DOWNLOAD) ? CreateButton : null)
    return () => {
      setExtra(null)
    }
  }, [file, fileUrl])

  useEffect(() => {
    const condition = uuid ? fileUrl : file
    setLoading(!condition)
  }, [file, fileUrl])

  const renderFile = () => {
    if (ext === 'pdf') {
      return (
        <PdfEdit
          extraClassName={styles.pdfWrapper}
          useActionSlot={false}
          readOnly
          useVirtual={false}
          file={{ data: uuid ? fileUrl : file }}
          pageHeaderConfig={pageHeaderParams}
          docLoaded={docLoaded}
        />
      )
    }
    return (
      <Image
        src={fileUrl}
        preview={false} // 关闭预览功能
        width={1000}
        height={1000}
        style={{
          objectFit: 'contain', // 保持图片比例
        }}
      />
    )
  }

  return <div className={styles.viewWrapper}>{loading ? <PageLoading /> : renderFile()}</div>
}

export default View
