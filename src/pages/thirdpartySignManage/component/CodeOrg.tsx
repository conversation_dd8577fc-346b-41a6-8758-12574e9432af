import React from 'react'
import OrgsFilterSelect from '@/components/business/OrgsFilterSelect'

const OrgWrap = (props: any) => {
  const { onChange, multiple = false, ...restProps } = props
  const changeFn = (_, node) => {
    if (multiple) {
      onChange(
        node?.map((i) => ({
          value: i.id,
          label: i.orgName,
          code: i.code,
        })),
      )
    } else {
      onChange({
        value: node.id,
        label: node.orgName,
        code: node.code,
      })
    }
  }
  return <OrgsFilterSelect {...restProps} multiple={multiple} onChange={changeFn} />
}

export default OrgWrap
