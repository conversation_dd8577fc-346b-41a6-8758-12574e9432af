import React, { useState } from 'react'
import { Modal, Alert, message } from 'antd'
import { SchemaForm, Item } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/antd-schema-form'
import { TreeSelect as Dict } from '@galaxy/dict'
import { getContractPackageList } from '@/api/thirdparty-sign-manage'

const CancelModal = (props: any) => {
  const { visible, close, onOk: okClick, onSuccess } = props
  const form = SchemaForm.createForm()
  const [loading, setLoading] = useState(false)

  // 获取合同包列表
  const getContractPackage = async () => {
    const params = {
      status: 1,
    }
    return getContractPackageList(params).then(({ data: resData }) => {
      if (resData?.data) {
        return resData?.data.map((i) => ({
          value: i.id,
          label: i.name,
        }))
      }
      return []
    })
  }

  const schema: SchemaType = {
    packageId: {
      label: '作废协议',
      component: 'Select',
      required: true,
      options: getContractPackage,
      props: {
        showSearch: true,
        optionFilterProp: 'label',
      },
    },
    reasonCode: {
      label: '作废原因',
      component: Dict,
      props: {
        code: 'OBSOLETE_REASON',
        placeholder: '请选择作废原因',
        showSearch: true,
        allowClear: true,
      },
      rules: [
        {
          required: true,
          message: '请选择作废原因',
        },
      ],
    },
    remark: {
      label: '备注',
      component: 'Input.TextArea',
      required: true,
      min: 10,
      max: 200,
    },
  }

  const onOk = async () => {
    try {
      const values = await form?.validateFields()
      setLoading(true)
      okClick({
        ...values,
      })
        .then(() => {
          close()
          message.success('操作成功')
          onSuccess?.()
        })
        .finally(() => {
          setLoading(false)
        })
    } catch (error) {
      console.error(error)
    }
  }

  return (
    <Modal
      title="作废"
      visible={visible}
      onCancel={close}
      onOk={onOk}
      width={650}
      okButtonProps={{
        loading,
      }}
    >
      <Alert
        message="操作后将会发送作废协议给员工签署，签署完成后原本签署记录将会作废。"
        type="info"
        showIcon
        style={{
          marginBottom: 24,
        }}
      />
      <SchemaForm form={form} schema={schema}>
        <Item field="packageId" />
        <Item field="reasonCode" />
        <Item field="remark" />
      </SchemaForm>
    </Modal>
  )
}

export default CancelModal
