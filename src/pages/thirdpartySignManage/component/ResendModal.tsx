import React, { useState } from 'react'
import { Modal, Alert } from 'antd'
import { TreeSelect as Dict } from '@galaxy/dict'
import { SchemaForm, Item } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/antd-schema-form'

import styles from './style.module.less'

const ResendModal = (props: any) => {
  const { visible, close, onOk: okClick } = props
  const form = SchemaForm.createForm()
  const [loading, setLoading] = useState(false)

  const schema: SchemaType = {
    reSendReasonCode: {
      label: '重发原因',
      component: Dict,
      required: [true, '请选择重发原因'],
      props: {
        code: 'RESEND_REASON',
        placeholder: '请选择重发原因',
        showSearch: true,
        allowClear: true,
        filter: (data) => data.value !== 'CM_RR_005',
      },
    },
  }

  const onOk = async () => {
    try {
      const values = await form?.validateFields()
      setLoading(true)
      okClick({
        ...values,
      })
        .then(() => {
          close()
        })
        .finally(() => {
          setLoading(false)
        })
    } catch (error) {
      console.error(error)
    }
  }

  return (
    <Modal
      title="重发"
      open={visible}
      onCancel={close}
      onOk={onOk}
      width={650}
      okButtonProps={{
        loading,
      }}
    >
      <Alert
        className={styles.alert}
        message={
          <>
            操作后将会发送新的签署短链；
            <br />
            若合同内容有误，无需重发，应失效合同后重新发起业务流程。
          </>
        }
        type="info"
        showIcon
      />
      <SchemaForm
        className={styles.schemaForm}
        form={form}
        schema={schema}
        labelCol={{
          span: 6,
        }}
      >
        <Item field="reSendReasonCode" />
      </SchemaForm>
    </Modal>
  )
}

export default ResendModal
