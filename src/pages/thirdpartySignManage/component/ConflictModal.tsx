import React from "react";
import { Divider, Modal, Table } from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'


type IProps = {
    close: () => void,
    visible: boolean,
    data: any[]
}

const columns = [
    {
        dataIndex: 'userName',
        title: '姓名',
        width: 200,
        render: (text, record) => `${text}${record.userNum ? `（${record.userNum}）` : ''}`,
    },
    {
        dataIndex: 'businessItem',
        title: '关联业务项',
    },
    {
        dataIndex: 'fileName',
        title: '签署文件',
    },
]

const ConflictModal = (props: IProps) => {
    const { close, visible, data = [] } = props
    return (
        <Modal
            width={600}
            visible={visible}
            onOk={close}
            onCancel={close}
            okText="关闭"
            cancelButtonProps={{
                style: { display: 'none' }
            }}
            title={<div style={{ display: 'flex', alignItems: 'center' }}><ExclamationCircleOutlined style={{ color: '#faad14', marginRight: 8, fontSize: 24 }}/>提示</div>}
        >
            <div style={{ marginBottom: 24 }}>以下人员存在业务项冲突，需要等到合同生效，才可以发起下一个业务项签署。请重新选择人员进行签署！</div>
            <Table
                dataSource={data}
                columns={columns}
            />
        </Modal>
    )
}

export default ConflictModal