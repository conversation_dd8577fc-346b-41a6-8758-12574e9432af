import React from 'react'
import { TreeSelect as Dict } from '@galaxy/dict'
import { Button } from 'antd'
// 埋点
import type { SchemaColumnType } from '@amazebird/antd-schema-table'
import dmsReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'

import PositionSelect from '@/components/positionSelect'
import {
  // getFilesList,
  getContractSubject,
  // getRelatedNumber,
  getDataSourceList,
} from '@/api/thirdparty-sign-manage'
import { OFFER_BUSINESS_CODE } from '@/constants/sign-manage'
import config from '@/config'
import { maxTagPlaceholder } from '@/utils/utils'
import CodeOrg from '../component/CodeOrg'

const { tenantId, baseRoute } = config

const columns: SchemaColumnType = [
  {
    dataIndex: 'userName',
    title: '姓名',
    cell: ({ text, record }) =>
      text && record.userNum ? `${text}（${record.userNum}）` : `${text || '--'}`,
    width: 150,
    fixed: 'left',
  },
  {
    dataIndex: 'fileName',
    title: '签署文件',
    cell: {
      type: 'Text',
      props: {
        ellipsis: true,
      },
      render: ({ text, record }, dom) =>
        record.businessItemCode === OFFER_BUSINESS_CODE ? (
          text
        ) : (
          <Button
            type="link"
            style={{
              padding: 0,
              width: '100%',
              textAlign: 'start',
            }}
            target="_blank"
            size="small"
            href={`${baseRoute}/contract/sign-manage/thirdparty/preview?id=${record.id}&name=${text}`}
            onClick={() => {
              dmsReport.trace(REPORT_EVENT_TYPE.SIGN_FILE_VIEW, record)
            }}
          >
            {dom}
          </Button>
        ),
    },
    width: 200,
    fixed: 'left',
  },
  {
    dataIndex: 'departmentName',
    title: '部门',
    width: 150,
    cell: {
      type: 'Text',
      props: {
        ellipsis: {
          direction: 'middle',
        },
      },
    },
  },
  {
    dataIndex: 'workCityName',
    title: '工作城市',
  },
  {
    dataIndex: 'postLevelName',
    title: '职位',
  },
  {
    dataIndex: 'postName',
    title: '岗位',
  },
  {
    dataIndex: 'entryDate',
    title: '入职日期',
    cell: 'Date',
    width: 100,
  },
  {
    dataIndex: 'phoneNumber',
    title: '手机号',
  },
  {
    dataIndex: 'idCardNumber',
    title: '身份证号码',
    width: 200,
  },
  {
    dataIndex: 'contractSubjectName',
    title: '合同主体',
    cell: {
      type: 'Text',
      props: {
        ellipsis: true,
      },
    },
    width: 200,
  },
  {
    dataIndex: 'contractTypeName',
    title: '合同/协议类型',
  },
  {
    dataIndex: 'contractNumber',
    title: '合同编号',
    cell: {
      type: 'Text',
      props: {
        ellipsis: true,
      },
    },
    width: 250,
  },
  {
    dataIndex: 'fddContractId',
    title: '法大大ID',
    cell: {
      type: 'Text',
      props: {
        ellipsis: true,
      },
    },
    width: 250,
  },
  {
    dataIndex: 'contractStartTime',
    title: '合同开始日期',
    cell: 'Date',
    width: 100,
  },
  {
    dataIndex: 'contractEndTime',
    title: '合同到期日期',
    cell: 'Date',
    width: 100,
  },
  {
    dataIndex: 'contractDeadline',
    title: '合同期限',
    sorter: true,
  },
  {
    dataIndex: 'dataSource',
    title: '数据来源',
  },
  {
    dataIndex: 'businessItemName',
    title: '关联业务项',
    width: 150,
  },
  {
    dataIndex: 'relatedApplyNumber',
    title: '关联申请单号',
    cell: {
      type: 'Text',
      props: {
        ellipsis: true,
      },
    },
    width: 180,
  },
  {
    dataIndex: 'timeCreate',
    // dataIndex: 'sponsorTime',
    title: '发起签署时间',
    cell: 'DateTime',
    width: 150,
  },
  {
    dataIndex: 'sponsorName',
    title: '发起人',
    width: 150,
    cell: ({ text, record }) =>
      text ? `${text}${record.sponsorNum ? `（${record.sponsorNum}）` : ''}` : '--',
  },
]

const searchColumns = [
  {
    dataIndex: 'nameOrNum',
    title: '姓名',
    component: 'Input',
    placeholder: '请输入姓名或工号',
    props: {
      allowClear: true,
    },
  },
  {
    dataIndex: 'idCardNumber',
    title: '身份证号码',
    component: 'Input',
    max: 50,
    props: {
      allowClear: true,
    },
  },
  // {
  //   dataIndex: 'fileId',
  //   title: '签署文件',
  //   component: 'Select',
  //   options: () => getFilesList(0),
  //   props: {
  //     showSearch: true,
  //     mode: 'multiple',
  //     optionFilterProp: 'label',
  //     maxTagCount: 'responsive',
  //     maxTagPlaceholder: maxTagPlaceholder(),
  //   },
  // },
  // V1.5.0调整为输入框
  {
    dataIndex: 'fileName',
    title: '签署文件',
    component: 'Input',
    placeholder: '请输入签署文件',
    props: {
      allowClear: true,
    },
  },
  {
    dataIndex: 'contractSubject',
    title: '合同主体',
    component: 'Select',
    options: () => getContractSubject(0),
    props: {
      showSearch: true,
      mode: 'multiple',
      optionFilterProp: 'label',
      maxTagCount: 'responsive',
      maxTagPlaceholder: maxTagPlaceholder(),
    },
  },
  {
    dataIndex: 'dataSource',
    title: '数据来源',
    component: 'Select',
    options: () => getDataSourceList(0),
    props: {
      showSearch: true,
      // mode: 'multiple',
      optionFilterProp: 'label',
      // maxTagCount: 'responsive',
      // maxTagPlaceholder: maxTagPlaceholder(),
    },
  },
  {
    dataIndex: 'businessItemCode',
    title: '关联业务项',
    component: Dict,
    props: {
      code: 'BUSINESS_ITEM',
      placeholder: '请选择关联业务项',
      showSearch: true,
      allowClear: true,
    },
  },
  // {
  //   dataIndex: 'relatedApplyNumber',
  //   title: '关联申请单号',
  //   component: 'Select',
  //   options: () => getRelatedNumber(0),
  //   props: {
  //     showSearch: true,
  //     optionFilterProp: 'label',
  //   },
  // },
  // V1.5.0调整为输入框
  {
    dataIndex: 'relatedApplyNumber',
    title: '关联申请单号',
    component: 'Input',
    placeholder: '请输入关联申请单号',
    props: {
      allowClear: true,
    },
  },
  {
    dataIndex: 'contractTime',
    title: '合同开始日期',
    component: 'RangePicker',
  },
  {
    dataIndex: 'department',
    title: '部门',
    component: CodeOrg,
    props: {
      placeholder: '请选择部门',
      showSearch: true,
      multiple: true,
      maxTagCount: 'responsive',
      maxTagPlaceholder: maxTagPlaceholder(),
    },
  },
  {
    title: '工作城市',
    dataIndex: 'workCityCode',
    component: Dict,
    props: {
      code: 'BM_73',
      scope: ['EHR'],
      placeholder: '请选择工作城市',
      tenantId,
      multiple: true,
      showSearch: true,
      allowClear: true,
      maxTagCount: 'responsive',
      maxTagPlaceholder: maxTagPlaceholder(),
    },
  },
  {
    title: '职位',
    dataIndex: 'postLevel',
    component: Dict,
    props: {
      code: 'BM_ZWDJ',
      scope: ['EHR'],
      placeholder: '请选择职位',
      tenantId,
      multiple: true,
      showSearch: true,
      allowClear: true,
      maxTagCount: 'responsive',
      maxTagPlaceholder: maxTagPlaceholder(),
    },
  },
  {
    dataIndex: 'positionIds',
    title: '岗位',
    component: PositionSelect,
    props: {
      mode: 'multiple',
      showSearch: true,
      maxTagCount: 'responsive',
      maxTagPlaceholder: maxTagPlaceholder(),
    },
  },
]

export { columns, searchColumns }
