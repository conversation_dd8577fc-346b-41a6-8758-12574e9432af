import React from 'react'
import { SchemaForm, Item } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/antd-schema-form'
import { TreeSelect as Dict } from '@galaxy/dict'
import classNames from 'classnames'
import { getTemplateList } from '@/api/template'
import { IComponentSlot } from '@/types/sign-manage'
import { TEMPLATE_TYPE } from '@/constants/sign-manage'

import styles from '../launch/style.module.less'

const InvalidForm = ({ extraClassName, form }: IComponentSlot) => {
  // 获取模板列表
  const getTemplate = async () => {
    return getTemplateList({
      fddStatus: 1,
      type: TEMPLATE_TYPE.REMOVE,
      typeCode: TEMPLATE_TYPE.REMOVE,
      isSupportOuter: true,
    }).then(({ data: resData }) => {
      if (resData?.data) {
        return resData?.data.map((v) => ({
          label: v.name,
          value: v.id,
        }))
      }
      return []
    })
  }

  const schema: SchemaType = {
    packageId: {
      label: '解除协议',
      component: 'Select',
      required: true,
      options: getTemplate,
      props: {
        showSearch: true,
        optionFilterProp: 'label',
      },
    },
    reasonCode: {
      label: '解除原因',
      component: Dict,
      props: {
        code: 'REMOVE_REASON',
        placeholder: '请选择解除原因',
        showSearch: true,
        allowClear: true,
      },
      rules: [
        {
          required: true,
          message: '请选择解除原因',
        },
      ],
    },
    remark: {
      label: '备注',
      component: 'Input.TextArea',
      required: true,
      min: 10,
      max: 200,
    },
  }

  return (
    <div className={classNames(styles.schemaWrapper, extraClassName)}>
      <SchemaForm form={form} schema={schema}>
        <Item field="packageId" />
        <Item field="reasonCode" />
        <Item field="remark" />
      </SchemaForm>
    </div>
  )
}
export default InvalidForm
