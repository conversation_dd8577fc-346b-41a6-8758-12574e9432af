import React, { useEffect, useState, useRef, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button, Steps, Modal, ConfigProvider, message } from 'antd'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { isEqual } from 'lodash-es'
import { VariableProps } from '@/types/pdf-edit'
import zhCN from 'antd/lib/locale/zh_CN'
import {
  TemplateContentAndVarsMapProps,
  SignConfirmHandler,
  IComponentSlot,
  ActiveType,
} from '@/types/sign-manage'
import LaunchConfirm from './Confirm'

import styles from './style.module.less'

interface ILaunchProps {
  isExposeModule?: boolean
  variables: VariableProps[]
  /** 用于解除合同和作废的插槽 */
  componentSlot: ({ extraClassName }: IComponentSlot) => React.ReactNode
  apiRequest?: (activeName: ActiveType) => (values: any) => Promise<any>
  // 点击确定时，展示confirm需要用到的关联换签合同编号
  relatedRenewContractNumber?: string
}

const steps = [
  {
    key: 'step1',
    title: '选择合同',
  },
  {
    key: 'step2',
    title: '确认合同内容',
  },
]

const Launch = ({
  variables,
  componentSlot,
  apiRequest,
  isExposeModule,
  relatedRenewContractNumber,
}: ILaunchProps) => {
  const [current, setCurrent] = useState(0)
  const [loading, setLoading] = useState(false)
  const [basicValues, setBasicValues] = useState({})
  const [pageLoading, setPageLoading] = useState(false)
  const [aboutTemplates, setAboutTemplates] = useState<TemplateContentAndVarsMapProps>({} as any)
  const varsElementRef = useRef<SignConfirmHandler>(null)

  const form = SchemaForm.createForm()
  const navigate = useNavigate()

  const handleCancel = () => {
    const values = form?.getFieldsValue()
    const isNotEmpty = Object.keys(values).some((key) => values[key])
    if (!isNotEmpty) {
      navigate(-1)
      return
    }
    Modal.confirm({
      title: '提示',
      content: '您编辑的内容尚未保存，确定要离开此页面吗？',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        navigate(-1)
      },
    })
  }

  /**
   * 将模板列表区分为主、附模板
   */
  const transformTemplate = (template: any[] = []) => {
    let mainTemplateId = null as any
    const additionalTemplateIds: number[] = []
    for (let i = 0; i < template.length; i++) {
      if (template[i].isMain) {
        mainTemplateId = Number(template[i].id)
      } else {
        additionalTemplateIds.push(Number(template[i].id))
      }
    }
    if (!mainTemplateId) {
      throw new Error('不存在主模板')
    }
    return [mainTemplateId, additionalTemplateIds]
  }

  const submitData = async () => {
    setLoading(true)
    try {
      const values = await form?.validateFields()
      let newVarsMap = {}

      if (isExposeModule) {
        newVarsMap = varsElementRef.current?.getVarsMapForExposeModule()
      } else {
        newVarsMap = await varsElementRef.current?.getVarsMap()
      }
      const newVars: { code: string; value: string }[] = []
      // eslint-disable-next-line guard-for-in, no-restricted-syntax
      for (const key in newVarsMap) {
        const value = newVarsMap[key]
        if (!value) {
          message.warn('还有未确认的变量信息')
          return
        }
        newVars.push({
          value,
          code: key,
        })
      }

      if (apiRequest !== undefined) {
        await apiRequest('RESULT')({
          ...values,
          vars: newVars,
        })
      }
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async () => {
    if (relatedRenewContractNumber) {
      Modal.info({
        title: '提示',
        content: `关联换签合同编号为${relatedRenewContractNumber}尚未作废，请稍后前往处理！`,
        onOk: () => {
          submitData()
        },
      })
    } else {
      submitData()
    }
  }

  /**
   * 获取模板详情以及相关数据
   */
  const getTemplatesDetail = async (values) => {
    if (apiRequest !== undefined) {
      const { data: result } = await apiRequest('GET_TEMPLATE_CONTENT')(values)
      setAboutTemplates(result.data)
    }
  }

  const handleNext = async () => {
    // 第一步切换到第二步需要请求，因此判断逻辑稍微复杂
    if (current === 0) {
      try {
        setPageLoading(true)
        await form?.validateFields()

        const values = form?.getFieldsValue()
        const { template = [], ...restValues } = values

        let mainTemplateId = null
        let additionalTemplateIds = null
        if (template.length > 0) {
          ;[mainTemplateId, additionalTemplateIds] = transformTemplate(template)
        }

        // 值没更改过不需要重复请求
        if (isEqual(values, basicValues)) {
          setCurrent((c) => c + 1)
        } else {
          const params = {
            ...restValues,
            mainTemplateId,
            additionalTemplateIds,
          }

          // 否则先进行请求再进行页面跳转
          await getTemplatesDetail(params)
          setBasicValues(values)
          setCurrent((c) => c + 1)
        }
      } finally {
        setPageLoading(false)
      }
    } else {
      // 确认合同变量信息的form校验
      await varsElementRef.current?.validateFields()
      setCurrent((c) => c + 1)
    }
  }

  const Footer = (
    <div className={styles.footer}>
      <Button className={styles.btn} onClick={handleCancel}>
        取消
      </Button>
      {current !== 0 && (
        <Button className={styles.btn} onClick={() => setCurrent((num) => num - 1)}>
          上一步
        </Button>
      )}
      {current === 1 && (
        <Button
          className={styles.btn}
          type="primary"
          onClick={() => handleSubmit()}
          loading={loading}
        >
          确定
        </Button>
      )}
      {current !== 1 && (
        <Button
          className={styles.btn}
          type="primary"
          onClick={() => handleNext()}
          loading={pageLoading}
        >
          下一步
        </Button>
      )}
    </div>
  )

  const newVars = useMemo(
    () =>
      aboutTemplates.variables?.reduce((cur, item) => {
        // eslint-disable-next-line no-param-reassign
        cur[item.code] = item.value
        return cur
      }, {}),
    [aboutTemplates.variables],
  )

  useEffect(() => {
    // 第2步与第3部共用一个组件，切换时需要回到顶部
    varsElementRef.current?.initWrapperPosition()
  }, [current])

  return (
    <ConfigProvider locale={zhCN} prefixCls="contract">
      <div className={styles.createWrapper}>
        <div className={styles.step}>
          <Steps current={current} items={steps} className={styles.steps} />
        </div>
        <div className={styles.contentWrapper}>
          {componentSlot({
            extraClassName: current !== 0 ? styles.switch : '',
            form,
          })}
          <LaunchConfirm
            isExposeModule={isExposeModule}
            variables={variables}
            ref={varsElementRef}
            varsMap={newVars || {}}
            isPreview={current === 2}
            templates={aboutTemplates.details || []}
            extraClassName={current === 0 ? styles.switch : ''}
          />
        </div>
        {Footer}
      </div>
    </ConfigProvider>
  )
}

export default Launch
