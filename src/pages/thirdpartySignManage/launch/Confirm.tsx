import React, {
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useCallback,
} from 'react'
import { LoadingOutlined } from '@ant-design/icons'
import { Spin } from 'antd'
import classNames from 'classnames'
import { toCamelCase } from '@galaxy/utils'
import { cloneDeep } from 'lodash-es'
import PdfEdit from '@/components/pdfEditor'
import { SignConfirmHandler } from '@/types/sign-manage'
import { getFileDataServiceApi } from '@/service/fileService'
import { VariableProps } from '@/types/pdf-edit'
import LaunchConfirmDetailSlot from './DetailSlot'

import styles from './style.module.less'

const antIcon = (
  // @ts-ignore
  <LoadingOutlined
    style={{
      fontSize: 24,
    }}
    spin
  />
)

type UseBoxes = Record<number, Array<any>>

interface IProps {
  isExposeModule?: boolean
  variables: VariableProps[]
  isPreview?: boolean
  templates: any[]
  varsMap: Record<string, string>
  extraClassName?: string
}

const LaunchConfirm = forwardRef<SignConfirmHandler, IProps>(
  ({ variables, templates, varsMap, isPreview, extraClassName, isExposeModule }, ref) => {
    const [highlightKey, setHighlightKey] = useState('')
    const [useBoxes, setUseBoxes] = useState<UseBoxes>({})
    const [useTemplates, setUseTemplates] = useState<any[]>([])
    const [useVarsMap, setUseVarsMap] = useState<Record<string, string>>(varsMap)
    const [pdfLoaded, setPdfLoaded] = useState(false)
    const formRef = useRef<any>()

    const pdfWrapperRef = useRef<HTMLDivElement>(null)

    // 页头相关变量
    const pageIndexRef = useRef(0)
    const totalRef = useRef(0)
    const [total, setTotal] = useState(0)
    const numRef = useRef<{ [key: number]: number }>({})

    useImperativeHandle(ref, () => ({
      getVarsMap: async () => {
        await formRef.current?.validateFields()
        return useVarsMap
      },
      getVarsMapForExposeModule: () => useVarsMap,
      initWrapperPosition: () => pdfWrapperRef.current?.scrollTo(0, 0),
      validateFields: formRef.current?.validateFields,
    }))

    /**
     * PDF 加载完成
     */
    const docLoaded = (pageTotal: number, pageIndex?: number) => {
      pageIndexRef.current += 1
      totalRef.current += pageTotal
      if (pageIndex !== undefined) {
        numRef.current[pageIndex] = pageTotal
      }

      if (pageIndexRef.current === templates.length) {
        // 全部加载完，需要做一次累加
        Object.keys(numRef.current).forEach((num: any) => {
          if (num > 0) {
            numRef.current[num] += numRef.current[num - 1]
          }
        })
        setTotal(totalRef.current)
      }
    }

    /**
     *  处理模板的变量
     */
    const getTemplateVarsCoords: (
      titleMap: Record<string, string>,
      sealList?: any,
      variableList?: any,
    ) => any = (titleMap, sealList = [], variableList = []) => {
      const list: any = []

      const handleVar = (isSeal: boolean, item: any) => {
        const info = JSON.parse(item.info)
        const obj: any = {
          ...item,
          ...info,
          ...(isSeal
            ? {}
            : {
                title: titleMap[toCamelCase(item.variableCode)],
                variableCode: item.variableCode,
              }),
        }
        list.push(obj)
      }

      sealList.forEach((item: any) => {
        handleVar(true, item)
      })

      variableList.forEach((item: any) => {
        handleVar(false, item)
      })

      return list
    }

    const formatTemplate = async () => {
      if (templates.length === 0) {
        return
      }
      setPdfLoaded(false)
      const templateList: any[] = []
      const fileDataList = await Promise.all(
        templates.map((template) => getFileDataServiceApi(template.fileId)),
      )

      templates.forEach((template, index) => {
        const { templateContent = {}, fileName = '' } = template
        const file = {
          name: fileName,
          data: `data:application/pdf;base64,${fileDataList[index]}`,
        }
        templateList.push({
          file,
          fontParams: { fontType: template.fontType, fontSize: template.fontSize },
          highlightTexts: [templateContent.sealKeyWord, templateContent.signatureKeyWord],
          ...template,
        })
      })
      setUseTemplates(templateList)
      setPdfLoaded(true)
    }

    // 表单项聚焦
    const onItemFocus = (key) => setHighlightKey(key)

    // 表单项变化
    const onItemBlur = (value, key) => {
      setUseVarsMap((vars) => ({
        ...vars,
        ...{
          [key]: value,
        },
      }))
      setHighlightKey('')
    }

    const pageHeaderParams = useCallback(
      (index) => ({
        total,
        pageIndex: index,
        preTotal: numRef.current[index - 1] || 0,
      }),
      [total, numRef],
    )

    // 根据变量映射关系改变变量值
    useEffect(() => {
      const result = {}
      const keys = Object.keys(useVarsMap)
      templates.forEach((template, index) => {
        const initBoxes = getTemplateVarsCoords(
          template?.variableNameMap,
          template?.templateContent?.sealList,
          template?.templateContent?.variableList,
        )

        const newBoxes = initBoxes.map((box) => {
          if (keys.includes(box.variableCode)) {
            return {
              ...box,
              content: useVarsMap[box.variableCode],
            }
          }
          return box
        })
        result[index] = newBoxes
      })
      setUseBoxes(result)
    }, [useVarsMap])

    // 初始化变量映射关系
    useEffect(() => {
      setUseVarsMap(cloneDeep(varsMap))
    }, [varsMap])

    useEffect(() => {
      formatTemplate()
    }, [templates])

    return (
      <div className={classNames(extraClassName, styles.launchConfirm)}>
        {pdfLoaded ? (
          <>
            <div className={styles.pdfEditWrapper} ref={pdfWrapperRef}>
              <div className={styles.scrollWrapper}>
                {useTemplates.map((item: any, index) => (
                  <>
                    <div style={{ margin: '1px auto' }} />
                    <PdfEdit
                      readOnly
                      extraClassName={styles.pdfEditor}
                      useVirtual={false}
                      useActionSlot={false}
                      key={item.fileId}
                      file={item.file}
                      sealCode={item.sealCode}
                      signType={item.signType}
                      initBoxes={useBoxes[index]}
                      initFontParams={item.fontParams}
                      highlightKey={highlightKey}
                      initSearch={item.highlightTexts}
                      pageHeaderConfig={pageHeaderParams(index)}
                      docLoaded={docLoaded}
                    />
                  </>
                ))}
              </div>
            </div>
            {!isPreview && (
              <LaunchConfirmDetailSlot
                isExposeModule={isExposeModule}
                variables={variables}
                formRef={formRef}
                onItemFocus={onItemFocus}
                onItemBlur={onItemBlur}
                varsMap={useVarsMap}
              />
            )}
          </>
        ) : (
          <div className={styles.loading}>
            <Spin indicator={antIcon} />
            <p>正在生成合同内容，请耐心等待～</p>
          </div>
        )}
      </div>
    )
  },
)

export default LaunchConfirm
