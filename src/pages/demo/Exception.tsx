import React from 'react'
import { But<PERSON> } from 'antd'
import { useNavigate } from 'react-router-dom'
import config from '@/config'

const { baseRoute } = config

const Exception = function () {
  const navigate = useNavigate()

  const goExceptionPage = (code) => {
    navigate(`${baseRoute}/${code}`)
  }

  return (
    <>
      <Button onClick={() => goExceptionPage(403)}>403</Button>
      <Button onClick={() => goExceptionPage(404)}>404</Button>
      <Button onClick={() => goExceptionPage(500)}>500</Button>
    </>
  )
}

export default Exception
