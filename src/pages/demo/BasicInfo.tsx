import React, { useMemo } from 'react'
import type { SchemaType } from '@amazebird/schema-form'
import { SchemaForm, Item } from '@amazebird/antd-schema-form'

import styles from './style.module.less'

interface IProps {
  footer: React.ReactNode
}

const BasicInfo = ({ footer }: IProps) => {
  const form = SchemaForm.createForm()
  const schema: SchemaType = useMemo(
    () => ({
      a: {
        label: '模板名称',
        component: 'Input',
        required: true,
      },
      b: {
        label: '模板类型',
        component: 'Select',
        required: true,
      },
      c: {
        label: '签署方式',
        component: 'Select',
        required: true,
      },
      d: {
        label: '关联印章',
        component: 'Select',
        required: true,
      },
      e: {
        label: '模板说明',
        component: 'Input.TextArea',
      },
    }),
    [],
  )
  return (
    <div className={styles.schemaWrapper}>
      <SchemaForm schema={schema} form={form} className={styles.schema}>
        <Item field="a" />
        <Item field="b" />
        <Item field="c" />
        <Item field="d" />
        <Item field="e" />
      </SchemaForm>
      {footer}
    </div>
  )
}

export default BasicInfo
