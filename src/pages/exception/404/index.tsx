import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { Result, Button } from 'antd'
import config from '@/config'

const { baseRoute } = config

export default function () {
  return (
    <Result
      status="404"
      title="404"
      style={{
        background: 'none',
      }}
      subTitle="页面没有找到"
      extra={
        <Link to={`${baseRoute}/`}>
          <Button type="primary">首页</Button>
        </Link>
      }
    />
  )
}
