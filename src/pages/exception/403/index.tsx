import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { Result, Button } from 'antd'
import config from '@/config'

const { baseRoute } = config

export default function () {
  return (
    <Result
      status="403"
      title="403"
      style={{
        background: 'none',
      }}
      subTitle="Sorry, you don't have access to this page."
      extra={
        <Link to={`${baseRoute}/`}>
          <Button type="primary">Back to home</Button>
        </Link>
      }
    />
  )
}
