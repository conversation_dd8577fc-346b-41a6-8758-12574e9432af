import React, { useRef, useEffect } from 'react'
import { SchemaTable, SchemaColumnType } from '@amazebird/antd-schema-table'
import { Button } from 'antd'
import { FileZipOutlined } from '@ant-design/icons'
import { getOperateLogPage } from '@/api/operate-log'
import { getFunctionModuleList, getUserBehaviorList } from '@/service/operateLog'
// import FieldContainer from '@/components/fieldContainer'
import { YES_OR_NO } from '@/constants'
import { formatDate } from '@/utils/utils'
// 埋点
import dmsReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import { getFileUrlServiceApi } from '@/service/fileService'

function OperateLog() {
  const isSearch = useRef<boolean>(false)
  // 请求列表
  const requestApi = ({ pagination, filter }) => {
    const {
      operatorNumOrName,
      operationContent,
      hasAccessory,
      timeCreate,
      userBehavior,
      functionModule,
    } = filter
    const { current, pageSize } = pagination
    const params = {
      page: current,
      size: pageSize,
      operatorNumOrName,
      operationContent,
      hasAccessory,
      startTime: timeCreate?.[0]?.startOf('minute')?.valueOf(),
      endTime: timeCreate?.[1]?.endOf('minute')?.valueOf(),
      userBehavior,
      functionModule,
    }
    if (isSearch.current) {
      dmsReport.trace(REPORT_EVENT_TYPE.OPERATE_LOG_QUERY, params)
      isSearch.current = false
    }
    return getOperateLogPage(params)
      .then(({ data: resData }) => ({
        success: true,
        data: resData.data,
        total: resData.count,
      }))
      .catch(() => ({
        data: [],
        total: 0,
      }))
  }

  // 表格列
  const columns: SchemaColumnType = [
    {
      dataIndex: 'userBehavior',
      title: '用户行为',
      cell: 'Text',
      options: getUserBehaviorList,
      width: 100,
    },
    {
      dataIndex: 'functionModule',
      title: '功能模块',
      cell: 'Text',
      options: getFunctionModuleList,
      width: 100,
    },
    {
      dataIndex: 'operationContent',
      title: '操作内容',
      width: 300,
      cell: {
        type: 'Text',
        props: {
          ellipsis: true,
        },
      },
      // render(value) {
      //   return <FieldContainer text={value} />
      // },
    },
    {
      dataIndex: 'userNameCreate',
      title: '操作人',
      cell: 'Text',
      render(value, record) {
        return `${value || '--'}(${record.userIdCreate || '--'})`
      },
    },
    {
      dataIndex: 'timeCreate',
      title: '操作时间',
      cell: 'DateTime',
      width: 150,
    },
    {
      dataIndex: 'fileId',
      title: '附件',
      cell: 'Text',
      render(value, record) {
        if (!value) {
          return '--'
        }
        const arr = record.fileName?.split('.')
        return (
          <Button
            type="link"
            style={{
              padding: 0,
            }}
            onClick={async () => {
              dmsReport.trace(REPORT_EVENT_TYPE.OPERATE_LOG_ATTACHMENT_DOWNLOAD, record)
              const url = await getFileUrlServiceApi(record.fileId, {
                response: {
                  'content-disposition': `attachment;filename=${encodeURIComponent(
                    `${`${arr[0]}_${formatDate(new Date(), '{y}{m}{d}{h}{i}{s}')}`}.${arr[1]}`,
                  )}`,
                },
              })
              window.location.href = url
            }}
            size="small"
          >
            <FileZipOutlined />
            {arr?.[0]}
          </Button>
        )
      },
    },
  ]

  const searchColumns = [
    {
      dataIndex: 'userBehavior',
      title: '用户行为',
      component: 'Select',
      options: getUserBehaviorList,
      props: {
        showSearch: true,
        filterOption: (search, node) => node.label?.includes(search),
      },
      max: 50,
      type: 'string',
    },
    {
      dataIndex: 'functionModule',
      title: '功能模块',
      component: 'Select',
      props: {
        showSearch: true,
        filterOption: (search, node) => node.label?.includes(search),
      },
      options: getFunctionModuleList,
    },
    {
      dataIndex: 'timeCreate',
      title: '操作时间',
      component: 'RangePicker',
      props: {
        format: 'YYYY-MM-DD HH:mm',
        placeholder: ['开始时间', '结束时间'],
        showTime: {
          format: 'HH:mm',
        },
      },
    },
    {
      dataIndex: 'operatorNumOrName',
      title: '操作人',
      component: 'Input',
      placeholder: '请输入姓名或工号',
      max: 50,
      type: 'string',
      props: {
        allowClear: true,
      },
    },
    {
      dataIndex: 'operationContent',
      title: '操作内容',
      component: 'Input',
      max: 50,
      type: 'string',
      props: {
        allowClear: true,
      },
    },
    {
      dataIndex: 'hasAccessory',
      title: '是否有附件',
      component: 'Select',
      options: YES_OR_NO,
    },
  ]

  return (
    <div
      style={{
        backgroundColor: 'white',
      }}
    >
      <SchemaTable
        columns={columns}
        searchColumns={searchColumns}
        request={requestApi}
        onSearch={() => {
          // 获取不到分页信息，只能声明ref处理
          isSearch.current = true
        }}
      />
    </div>
  )
}

export default OperateLog
