import React from 'react'
import { useStore } from '@/stores'
import { Button } from 'antd'
import { setTraffic } from '@/utils/traffic'

let clickCount = 0
let timer

const Extra = () => {
  const store = useStore((state) => state)
  const { setTrafficBtn, showTrafficBtn } = store

  const onClick = () => {
    clickCount += 1
    clearTimeout(timer)
    timer = setTimeout(() => {
      clickCount = 0
    }, 500)

    if (clickCount === 3) {
      try {
        setTraffic()
      } finally {
        clickCount = 0
        setTrafficBtn(true)
      }
    }
  }

  return !showTrafficBtn ? (
    <Button
      onClick={onClick}
      style={{
        opacity: 0,
      }}
    >
      测试租户
    </Button>
  ) : null
}

export default Extra
