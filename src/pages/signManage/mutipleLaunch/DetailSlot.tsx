import React, { useEffect, useState } from 'react'
import { Empty } from 'antd'
import { SchemaForm, SchemaType, Item } from '@amazebird/antd-schema-form'
import { getVariablesApi } from '@/api/sign-manage'
import { VariableProps } from '@/types/pdf-edit'
import { dataTypeMap } from '@/constants/sign-manage'

import styles from './style.module.less'

interface IProps {
  formRef?: any
  varsMap: Record<string, string>
  onItemFocus: (value: number) => void
  onItemBlur: (value: string, index: number) => void
}

const LaunchConfirmDetailSlot = ({ formRef, onItemFocus, onItemBlur, varsMap }: IProps) => {
  const [variables, setVariables] = useState<VariableProps[]>([])
  const form = SchemaForm.createForm()

  const schemaKeys = Object.keys(varsMap)

  if (formRef) {
    // eslint-disable-next-line no-param-reassign
    formRef.current = form
  }

  const generateProps = (key, type) => ({
    onFocus: () => {
      onItemFocus(key)
    },
    onBlur: () => {
      const value =
        type === 3 ? form?.getFieldValue(key).format('YYYY年MM月DD日') : form?.getFieldValue(key)
      onItemBlur(value, key)
    },
  })

  const getExtendsProps = (type) => ({
    showTime: type === 4,
    format: type === 3 ? 'YYYY年MM月DD日' : undefined,
    precision: type === 1 ? 0 : undefined,
  })

  const dateParse = (dateString) => {
    if (!dateString) {
      return null
    }
    const dateArray = dateString
      .split('年')
      .join('-')
      .split('月')
      .join('-')
      .split('日')
      .join('')
      .split(' ')
    const date = new Date(dateArray[0])
    const timestamp = date.getTime()
    return timestamp
  }

  const schema: SchemaType = {}
  const initialValues = {}

  Object.keys(varsMap).forEach((key) => {
    const vars = variables.find((v) => {
      return v.variableCode === key
    })

    if (!vars) {
      return
    }

    const component = vars.valueType !== undefined ? dataTypeMap[vars.valueType] : null

    // 构造一个默认值
    const isDateValue = vars.valueType && [3, 4].includes(vars.valueType)
    initialValues[key] = isDateValue ? dateParse(varsMap[key]) : varsMap[key]
    schema[key] = {
      label: vars.variableName,
      component,
      disabled: !vars.isMutable,
      props: {
        autocomplete: 'off',
        ...getExtendsProps(vars.valueType),
        ...generateProps(key, vars.valueType),
        style: {
          width: '100%',
        },
      },
    }
    // 直接写在schema[key]上，会出现日期会有校验
    if (vars.isMutable) {
      schema[key].required = [
        true,
        `${[0, 1, 2].includes(vars.valueType as number) ? '请输入' : '请选择'}${vars.variableName}`, // 由于label自定义，导致require的文字出错
      ]
      schema[key].max = 50
    }
  })

  form?.setFieldsValue(initialValues)

  const getVariables = async () => {
    const { data: result } = await getVariablesApi()
    setVariables(result.data.filter((item) => item.isEnable))
  }

  useEffect(() => {
    getVariables()
  }, [])

  return (
    <div className={styles.launchConfirmDetailSlot}>
      <div className={styles.header}>确认合同变量信息</div>
      {Object.keys(schema).length > 0 ? (
        <SchemaForm schema={schema} form={form} className={styles.schemaContent} labelWrap>
          {schemaKeys.map((key) => (
            <Item
              key={key}
              field={key}
              colon={false}
              label={
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: (schema[key]?.label as string)?.length >= 20 ? '10px' : undefined,
                  }}
                >
                  <span>{schema[key]?.label as string}</span>
                  <span>&nbsp;：</span>
                </div>
              }
            />
          ))}
        </SchemaForm>
      ) : (
        <Empty description="暂无变量数据" />
      )}
    </div>
  )
}

export default LaunchConfirmDetailSlot
