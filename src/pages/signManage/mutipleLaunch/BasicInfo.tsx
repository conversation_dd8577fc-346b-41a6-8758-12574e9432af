import React, { useMemo } from 'react'
import { Spin } from 'antd'
import type { SchemaType } from '@amazebird/schema-form'
import { SchemaForm, Item, Observer } from '@amazebird/antd-schema-form'
import { getBusinessListApi } from '@/api/sign-manage'
import classNames from 'classnames'
import UserFilterSelect from '@/components/business/UserFilterSelect'
import ContractTemplate from '@/pages/basicConfig/contractPackage/edit/ContractTemplate'
import styles from './style.module.less'

interface IProps {
  form: any
  isEdit: boolean
  initValues?: any
  extraClassName?: string
}

const LaunchBasicInfo = ({ form, isEdit, initValues, extraClassName }: IProps) => {
  const isLoading = useMemo(
    () => isEdit && Object.keys(initValues).length === 0,
    [isEdit, initValues],
  )

  // 获取岗位列表
  const getBusinessList = async () => {
    const {
      data: { data },
    } = await getBusinessListApi()
    return data.map((option) => ({
      label: option.businessName,
      value: option.businessNameCode,
    }))
  }

  const schema: SchemaType = useMemo(
    () => ({
      businessItemCode: {
        label: '关联业务项',
        component: 'Select',
        required: [true, '请选择关联业务项'],
        props: {
          showSearch: true,
          allowClear: true,
        },
        options: getBusinessList,
      },
      user: {
        label: '人员',
        component: UserFilterSelect,
        required: [true, '请选择人员'],
        placeholder: '请选择人员',
        props: {
          multiple: 30,
          showSearch: true,
        },
      },
      template: {
        label: '合同模板',
        required: [true, '请选择合同模板'],
        component: ContractTemplate,
        observer: Observer({
          watch: 'businessItemCode',
          action: (value) => {
            return {
              disabled: !value,
              value: undefined,
              props: {
                isView: false,
                isPreview: false,
                relatedValue: value,
                isShowSealKeyWord2: false,
              },
            }
          },
        }),
        itemProps: {
          tooltip: '合同批量签署（多选模板）只能选择印章类型一致、关键字定位且关键字一致的模板',
        },
      },
    }),
    [],
  )

  return (
    <div
      className={classNames(styles.schemaWrapper, extraClassName, isLoading ? styles.loading : '')}
    >
      {isLoading ? (
        <Spin />
      ) : (
        <SchemaForm
          initialValues={initValues}
          schema={schema}
          form={form}
          className={styles.schema}
        >
          <Item field="businessItemCode" />
          <Item field="user" />
          <Item field="template" className={styles.templateSelectWrapper} />
        </SchemaForm>
      )}
    </div>
  )
}

export default LaunchBasicInfo
