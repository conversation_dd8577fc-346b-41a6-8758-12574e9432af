import React, {
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useCallback,
  useMemo,
} from 'react'
import { Spin, message } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'
import { toCamelCase } from '@galaxy/utils'
import {
  SignConfirmHandler,
  TemplateContentAndVarsMapProps,
  TemplateVar,
} from '@/types/sign-manage'
import PdfEdit from '@/components/pdfEditor'
import { getFileDataServiceApi } from '@/service/fileService'

import classNames from 'classnames'
import LaunchConfirmDetailSlot from './DetailSlot'

import styles from './style.module.less'

type UseBoxes = Record<number, Array<any>>

interface IProps {
  templates: any[]
  userSigns: TemplateContentAndVarsMapProps['userSigns']
  extraClassName?: string
}

const LaunchConfirm = forwardRef<SignConfirmHandler, IProps>(
  ({ templates, userSigns, extraClassName }, ref) => {
    const [total, setTotal] = useState(0)
    const [highlightKey, setHighlightKey] = useState('')
    const [useBoxes, setUseBoxes] = useState<UseBoxes>({})
    const [useTemplates, setUseTemplates] = useState<any[]>([])
    const [currentUserVarsMap, setCurrentUserVarsMap] = useState<Record<string, string>>({})
    const [userVarsMap, setUserVarsMap] = useState<Record<string, any>>({})
    const [pdfLoaded, setPdfLoaded] = useState(false)
    const [activeUser, setActiveUser] = useState<{ name: string; num: string }>({} as any)
    const formRef = useRef<any>(null)

    const pdfWrapperRef = useRef<HTMLDivElement>(null)

    // 页头相关变量
    const totalRef = useRef(0)
    const pageIndexRef = useRef(0)
    const numRef = useRef<{ [key: number]: number }>({})

    // 缓存用户变量值使用的 key
    const userVarMapKey = useMemo(() => `${activeUser.name}_${activeUser.num}`, [activeUser])

    useImperativeHandle(ref, () => ({
      getVarsMap: () => {
        return {
          ...userVarsMap,
          [userVarMapKey]: currentUserVarsMap,
        }
      },
      initWrapperPosition: () => pdfWrapperRef.current?.scrollTo(0, 0),
      validateFields: async () => {
        try {
          await formRef.current?.validateFields()

          const result = {
            ...userVarsMap,
            [userVarMapKey]: currentUserVarsMap,
          }

          // 检查已填写内容的人员数量是否等于前面选择的人员数量
          const isUserNumEqual = Object.keys(result).length === userSigns.length
          const yet: string[] = []

          // 检查是否有人员有还未填写的变量信息
          const isNotEmpty = Object.keys(result).every((person) => {
            const current = result[person]
            const personReady = Object.keys(current).every((key) => !!current[key])
            if (!personReady) {
              yet.push(person.split('_')[0])
            }
            return personReady
          })

          const pass = isUserNumEqual && isNotEmpty

          if (!pass) {
            message.warn(`${yet.join('、')}还未确认变量信息`)
            return Promise.reject(new Error('还有人员未确认变量信息'))
          }

          return Promise.resolve()
        } catch (error) {
          // 表单校验报错会走到这一步
          return Promise.reject(new Error('还有人员未确认变量信息'))
        }
      },
    }))

    /**
     * PDF 加载完成
     */
    const docLoaded = (pageTotal: number, pageIndex?: number) => {
      pageIndexRef.current += 1
      totalRef.current += pageTotal
      if (pageIndex !== undefined) {
        numRef.current[pageIndex] = pageTotal
      }

      if (pageIndexRef.current === templates.length) {
        // 全部加载完，需要做一次累加
        Object.keys(numRef.current).forEach((num: any) => {
          if (num > 0) {
            numRef.current[num] += numRef.current[num - 1]
          }
        })
        setTotal(totalRef.current)
      }
    }

    /**
     *  处理模板的变量
     */
    const getTemplateVarsCoords: (
      titleMap: Record<string, string>,
      sealList?: any,
      variableList?: any,
    ) => any = (titleMap, sealList = [], variableList = []) => {
      const list: any = []

      const handleVar = (isSeal: boolean, item: any) => {
        const info = JSON.parse(item.info)
        const obj: any = {
          ...item,
          ...info,
          ...(isSeal
            ? {}
            : {
                title: titleMap[toCamelCase(item.variableCode)],
                variableCode: item.variableCode,
              }),
        }
        list.push(obj)
      }

      sealList.forEach((item: any) => {
        handleVar(true, item)
      })

      variableList.forEach((item: any) => {
        handleVar(false, item)
      })

      return list
    }

    /**
     *  格式化模板
     */
    const formatTemplate = async () => {
      if (templates.length === 0) {
        return
      }
      setPdfLoaded(false)
      const templateList: any[] = []
      const fileDataList = await Promise.all(
        templates.map((template) => getFileDataServiceApi(template.fileId)),
      )

      templates.forEach((template, index) => {
        const { templateContent = {}, fileName = '' } = template
        const file = {
          name: fileName,
          data: `data:application/pdf;base64,${fileDataList[index]}`,
        }
        templateList.push({
          file,
          fontParams: { fontType: template.fontType, fontSize: template.fontSize },
          highlightTexts: [templateContent.sealKeyWord, templateContent.signatureKeyWord],
          ...template,
        })
      })
      setUseTemplates(templateList)
      setPdfLoaded(true)
    }

    // 表单项聚焦
    const onItemFocus = (key) => setHighlightKey(key)

    // 表单项变化
    const onItemBlur = (value, key) => {
      setCurrentUserVarsMap((vars) => ({
        ...vars,
        ...{
          [key]: value,
        },
      }))
      setHighlightKey('')
    }

    const pageHeaderParams = useCallback(
      (index) => ({
        total,
        pageIndex: index,
        preTotal: numRef.current[index - 1] || 0,
      }),
      [total, numRef],
    )

    const onFormatUserVars = (list: TemplateVar[]) => {
      const result = {}
      list.forEach((v) => {
        result[v.code] = v.value
      })
      return result
    }

    const onChangeUser = (user) => {
      const newUserVarsMap = {
        ...userVarsMap,
        [userVarMapKey]: currentUserVarsMap,
      }
      setUserVarsMap(newUserVarsMap)
      setActiveUser(user)
    }

    // 根据变量映射关系改变变量值
    useEffect(() => {
      const result = {}
      const keys = Object.keys(currentUserVarsMap)
      templates.forEach((template, index) => {
        const initBoxes = getTemplateVarsCoords(
          template?.variableNameMap,
          template?.templateContent?.sealList,
          template?.templateContent?.variableList,
        )

        const newBoxes = initBoxes.map((box) => {
          if (keys.includes(box.variableCode)) {
            return {
              ...box,
              content: currentUserVarsMap[box.variableCode],
            }
          }
          return box
        })
        result[index] = newBoxes
      })
      setUseBoxes(result)
    }, [currentUserVarsMap])

    useEffect(() => {
      pageIndexRef.current = 0
      totalRef.current = 0
      formatTemplate()
    }, [templates])

    useEffect(() => {
      if (Array.isArray(userSigns) && userSigns.length > 0) {
        // 默认设置第一个
        setActiveUser({ name: userSigns[0].userName, num: userSigns[0].userNum })
      }
      const result = {}
      userSigns.forEach(({ userName, userNum, variables }) => {
        const varMap = {}
        variables.forEach((item) => {
          varMap[item.code] = item.value
        })
        result[`${userName}_${userNum}`] = varMap
      })

      setUserVarsMap(result)
    }, [userSigns])

    useEffect(() => {
      const currentUser = userSigns.find((u) => u.userNum === activeUser.num)

      if (!currentUser) {
        return
      }
      const cacheUserVarsMap = userVarsMap[`${activeUser.name}_${activeUser.num}`]
      setCurrentUserVarsMap(cacheUserVarsMap || onFormatUserVars(currentUser.variables))
    }, [activeUser])

    return (
      <div className={classNames(extraClassName, styles.launchConfirm)}>
        {pdfLoaded ? (
          <>
            <div className={styles.person}>
              {userSigns.map((i) => (
                // eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions
                <p
                  key={i.userNum}
                  className={classNames(styles.item, activeUser.num === i.userNum && styles.active)}
                  onClick={() =>
                    onChangeUser({
                      name: i.userName,
                      num: i.userNum,
                    })
                  }
                >
                  {i.userName}
                </p>
              ))}
            </div>
            <div className={styles.pdfEditWrapper} ref={pdfWrapperRef}>
              <div className={styles.scrollWrapper}>
                {useTemplates.map((item: any, index) => (
                  <>
                    <div style={{ margin: '1px auto' }} />
                    <PdfEdit
                      readOnly
                      extraClassName={styles.pdfEditor}
                      useVirtual={false}
                      useActionSlot={false}
                      key={item.fileId}
                      file={item.file}
                      sealCode={item.sealCode}
                      signType={item.signType}
                      initBoxes={useBoxes[index]}
                      highlightKey={highlightKey}
                      initFontParams={item.fontParams}
                      initSearch={item.highlightTexts}
                      pageHeaderConfig={pageHeaderParams(index)}
                      docLoaded={docLoaded}
                    />
                  </>
                ))}
              </div>
            </div>
            <LaunchConfirmDetailSlot
              formRef={formRef}
              onItemFocus={onItemFocus}
              onItemBlur={onItemBlur}
              varsMap={currentUserVarsMap}
            />
          </>
        ) : (
          <div className={styles.loading}>
            <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
            <p>正在生成合同内容，请耐心等待～</p>
          </div>
        )}
      </div>
    )
  },
)

export default LaunchConfirm
