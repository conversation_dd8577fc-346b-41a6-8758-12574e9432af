import React, { useEffect, useState, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button, Steps, Modal, message } from 'antd'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { isEqual } from 'lodash-es'
import { getTemplateContent, startSign, downloadSign, getVariablesApi } from '@/api/sign-manage'
import { TemplateContentAndVarsMapProps, SignConfirmHandler } from '@/types/sign-manage'
import config from '@/config'
import modalWrapperHoc from '@/components/modalWrapperHoc'
import ConflictModal from '../component/ConflictModal'
import LaunchConfirm from './Confirm'
import LaunchBasicInfo from './BasicInfo'

import styles from './style.module.less'

const { baseRoute } = config

const MutipleLaunch = () => {
  const [current, setCurrent] = useState(0)
  const [loading, setLoading] = useState(false)
  const [basicValues, setBasicValues] = useState({})
  const [aboutTemplates, setAboutTemplates] = useState<TemplateContentAndVarsMapProps>({} as any)
  const [pageLoading, setPageLoading] = useState(false)
  const [downloading, setDownLoading] = useState(false)
  const varsElementRef = useRef<SignConfirmHandler>(null)

  const form = SchemaForm.createForm()
  const navigate = useNavigate()

  const steps = [
    {
      key: 'step1',
      title: '选择合同',
    },
    {
      key: 'step2',
      title: '确认合同内容',
    },
  ]

  const handleCancel = () => {
    const values = form?.getFieldsValue()
    const isNotEmpty = Object.keys(values).some((key) => values[key])
    if (!isNotEmpty) {
      navigate(-1)
      return
    }
    Modal.confirm({
      title: '提示',
      content: '您编辑的内容尚未保存，确定要离开此页面吗？',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        navigate(-1)
      },
    })
  }

  /**
   * 将模板列表区分为主、附模板
   */
  const transformTemplate = (template: any[] = []) => {
    let mainTemplateId = null as any
    const additionalTemplateIds: number[] = []
    for (let i = 0; i < template.length; i++) {
      if (template[i].isMain) {
        mainTemplateId = Number(template[i].id)
      } else {
        additionalTemplateIds.push(Number(template[i].id))
      }
    }
    if (!mainTemplateId) {
      throw new Error('不存在主模板')
    }
    return [mainTemplateId, additionalTemplateIds]
  }

  const handleSubmit = async () => {
    setLoading(true)

    try {
      await varsElementRef.current?.validateFields()
      const values = form?.getFieldsValue()
      const newVarsMap = varsElementRef.current?.getVarsMap()

      const userVariables = Object.keys(newVarsMap).map((user) => {
        const [userName, userNum] = user.split('_')

        const varMap = newVarsMap[user]
        const variables = Object.keys(varMap).map((code) => ({ code, value: varMap[code] }))
        return {
          userNum,
          userName,
          variables,
        }
      })
      const { template = [], businessItemCode } = values
      const [mainTemplateId, additionalTemplateIds] = transformTemplate(template)
      const params = {
        mainTemplateId,
        additionalTemplateIds,
        businessItemCode,
        userVariables,
      }
      await startSign(params)
      message.info('系统处理中，稍后将通过飞书通知您结果。')
      navigate(`${baseRoute}/contract/sign-manage/inside?activeKey=signing`)
      // if (result.data.userErrors?.length) {
      //   if (result.data.userErrors.length === 1) {
      //     const { userNum, userName, businessItem } = result.data.userErrors[0]
      //     Modal.confirm({
      //       title: '提示',
      //       content: `${userName}${
      //         userNum ? `（${userNum}）` : ''
      //       }当前存在【${businessItem}】合同，需要等到合同生效，才可以发起下一个业务项签署。请重新选择人员签署！`,
      //     })
      //   } else {
      //     modalWrapperHoc(ConflictModal)({
      //       data: result.data?.userErrors,
      //     })
      //   }
      // } else if (result.data.failUsers?.length) {
      //   if (result.data.failUsers.length === 1) {
      //     const { userNum, userName } = result.data.failUsers[0]
      //     Modal.confirm({
      //       title: '提示',
      //       content: `${userName}${
      //         userNum ? `（${userNum}）` : ''
      //       }调用法大大生成合同失败，请重新发起。若重新尝试后失败可联系开发同学处理`,
      //       okText: '关闭',
      //       cancelButtonProps: {
      //         style: {
      //           display: 'none',
      //         },
      //       },
      //     })
      //   } else {
      //     Modal.confirm({
      //       title: '提示',
      //       content: (
      //         <div>
      //           <div style={{ marginBottom: 24 }}>
      //             {`调用法大大生成合同共 ${userVariables.length} 个人，其中 ${
      //               userVariables.length - result.data.failUsers.length
      //             } 个成功，`}
      //             <span style={{ color: '#faad14' }}>{result.data.failUsers.length}</span>
      //             个失败。失败名单如下，请重新发起。若重新尝试后失败可联系开发同学处理！
      //           </div>
      //           <div>失败人员：</div>
      //           <div>
      //             {result.data.failUsers
      //               .map((i) => `${i.userName}${i.userNum ? `（${i.userNum}）` : ''}`)
      //               .join('、')}
      //           </div>
      //         </div>
      //       ),
      //       okText: '关闭',
      //       cancelButtonProps: {
      //         style: {
      //           display: 'none',
      //         },
      //       },
      //     })
      //   }
      // } else {
      //   message.success('操作成功')
      //   navigate(`${baseRoute}/contract/sign-manage/inside?activeKey=signing`)
      // }
    } finally {
      setLoading(false)
    }
  }

  /**
   * 获取模板详情以及相关数据
   */
  const getTemplatesDetail = async (values) => {
    const { user, businessItemCode, ...restValues } = values
    const useUser = user.map((v) => ({
      userName: v.label,
      userNum: v.value,
    }))
    const { data: result } = await getTemplateContent({
      ...restValues,
      businessItemCode,
      userVariables: useUser,
    })
    if (result.data) {
      if (result.data?.signUserErrors?.length) {
        if (result.data?.signUserErrors?.length > 1) {
          modalWrapperHoc(ConflictModal)({
            data: result.data?.signUserErrors,
          })
        } else {
          const { userNum, userName, businessItem } = result.data.signUserErrors[0]
          Modal.confirm({
            title: '提示',
            content: `${userName}${
              userNum ? `（${userNum}）` : ''
            }当前存在【${businessItem}】合同，需要等到合同生效，才可以发起下一个业务项签署。请重新选择人员签署！`,
            cancelText: '取消',
            okText: '确定',
          })
        }
      } else {
        setAboutTemplates(result.data)
        setBasicValues(values)
        setCurrent((c) => c + 1)
      }
    }
  }

  const handleNext = async () => {
    try {
      setPageLoading(true)
      await form?.validateFields()

      const values = form?.getFieldsValue()
      const { template = [], ...restValues } = values

      let mainTemplateId = null
      let additionalTemplateIds = null
      if (template.length > 0) {
        ;[mainTemplateId, additionalTemplateIds] = transformTemplate(template)
      }

      // 值没更改过不需要重复请求
      if (isEqual(values, basicValues)) {
        setCurrent((c) => c + 1)
      } else {
        const params = {
          ...restValues,
          mainTemplateId,
          additionalTemplateIds,
        }
        await getTemplatesDetail(params)
      }
    } finally {
      setPageLoading(false)
    }
  }

  const download = async () => {
    setDownLoading(true)
    try {
      const newVarsMap = varsElementRef.current?.getVarsMap()

      const userVariables = Object.keys(newVarsMap).map((user) => {
        const [userName] = user.split('_')

        const varMap = newVarsMap[user]

        const variables = {}
        Object.keys(varMap)
          .map((code) => ({ code, value: varMap[code] }))
          .forEach((item) => {
            variables[item.code] = item.value
          })
        return {
          name: userName,
          variableData: variables,
        }
      })

      const { data: result } = await getVariablesApi()
      const varData = {}

      Object.keys(Object.values(newVarsMap)[0] as Object).forEach((key) => {
        const name = result.data.find((i) => i.variableCode === key).variableName
        varData[key] = name
      })

      const params = {
        userVariableList: userVariables,
        variable: varData,
      }
      await downloadSign(params)
    } finally {
      setDownLoading(false)
    }
  }

  const Footer = (
    <div className={styles.footer}>
      <Button className={styles.btn} onClick={handleCancel}>
        取消
      </Button>
      {current !== 0 && (
        <Button className={styles.btn} onClick={() => setCurrent((num) => num - 1)}>
          上一步
        </Button>
      )}
      {current === 1 && (
        <Button className={styles.btn} onClick={() => download()} loading={downloading}>
          导出
        </Button>
      )}
      {current === 1 && (
        <Button
          className={styles.btn}
          type="primary"
          onClick={() => handleSubmit()}
          loading={loading}
        >
          确认发送
        </Button>
      )}
      {current !== 1 && (
        <Button
          className={styles.btn}
          type="primary"
          onClick={() => handleNext()}
          loading={pageLoading}
        >
          下一步
        </Button>
      )}
    </div>
  )

  useEffect(() => {
    // 第2步与第3部共用一个组件，切换时需要回到顶部
    varsElementRef.current?.initWrapperPosition()
  }, [current])

  return (
    <div className={styles.createWrapper}>
      <div className={styles.step}>
        <Steps current={current} items={steps} className={styles.steps} />
      </div>
      <div className={styles.contentWrapper}>
        <LaunchBasicInfo
          form={form}
          initValues={{}}
          isEdit={false}
          extraClassName={current !== 0 ? styles.switch : ''}
        />
        <LaunchConfirm
          ref={varsElementRef}
          templates={aboutTemplates.details || []}
          userSigns={aboutTemplates.userSigns || []}
          extraClassName={current === 0 ? styles.switch : ''}
        />
      </div>
      {Footer}
    </div>
  )
}

export default MutipleLaunch
