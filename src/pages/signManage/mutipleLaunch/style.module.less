.step {
  margin: 24px;
  padding: 18px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;

  .steps {
    width: 70%;
  }
}

.contentWrapper {
  margin: 24px 24px 36px 24px;
  overflow: hidden;
  background-color: white;
}

.footer {
  height: 50px;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: end;
  background-color: white;
  z-index: 99;

  .btn {
    margin-left: 10px;

    &:last-of-type {
      margin-right: 10px;
    }
  }
}


.schemaWrapper {
  min-height: 25vh;
  margin: 30px 10px;

  .templateSelectWrapper {
    :global {
      .@{ant-prefix}-form-item-control-input-content {
        display: flex;
        justify-content: space-around;
      }

      .@{ant-prefix}-form-item {
        flex-grow: 1;
        flex: 1;
        margin-bottom: 0;
      }
    }
  }
}

.launchConfirmDetailSlot {
  width: 380px;
  flex-shrink: 0;
  flex-grow: 0;
  border-left: 1px solid #d2d2d2;
  height: 100%;
  overflow-y: auto;

  @media screen and (max-width: 1600px) {
    width: 250px;

    :global {
      .@{ant-prefix}-row {
        display: unset;
      }

      .@{ant-prefix}-col-sm-8 {
        max-width: unset;
      }

      .@{ant-prefix}-form-item-label {
        text-align: left;
      }
    }
  }

  .header {
    font-weight: bold;
    border-bottom: 1px solid #d2d2d2;
    padding: 8px 16px;
    font-size: 16px;
    margin-bottom: 20px;
  }

  .schemaContent {
    padding: 10px 14px;

    :global {
      .@{ant-prefix}-form-item-control {
        max-width: unset;
      }

      .@{ant-prefix}-input-number {
        width: unset;
      }
    }
  }
}

.launchConfirm {
  display: flex;
  height: 72vh;
  overflow-x: auto;

  @media screen and (max-height: 800px) {
    height: 58vh;
    overflow: auto;
  }

  @media screen and (max-height: 1150px) {
    height: 65vh;
    overflow: auto;
  }

  .person {
    height: 100%;
    width: 100px;
    background-color: white;
    overflow: auto;
    flex-shrink: 0;

    // 滚动条滑槽样式
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
      border-radius: 8px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 8px;
      background: #ddd;
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #ccc;
    }

    &::-webkit-scrollbar-thumb:active {
      background: #999;
    }

    .item {
      padding: 10px 0px;
      margin: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        display: none;
        height: 100%;
        width: 4px;
        background-color: #1ABB9C;
        right: 0;
        top: 0;
      }

      &:hover,
      &.active {
        color: #1ABB9C;
        background-color: #E7F7F4;
      }

      &:hover::after,
      &.active::after {
        display: inline-block;
      }
    }
  }


  .pdfEditWrapper {
    flex: 1;
    height: 100%;
    scroll-behavior: smooth;
    background-color: #f0f2f5;

    .scrollWrapper {
      width: min-content;
      overflow: auto;
      height: 100%;
      margin: 0 auto;
      display: flex;
      flex-direction: column
    }
  }

  .loading {
    width: 100%;
    height: 35vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}

.switch {
  z-index: -1;
  opacity: 0;
  position: absolute;
  height: 0;
  overflow: hidden;
}