import React, { useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from 'antd'
// 埋点

import { SchemaTable } from '@amazebird/antd-schema-table'
import type { SchemaColumnType } from '@amazebird/antd-schema-table'
import dmsReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import { getSignVoidList } from '@/api/sign-manage'
import PermissionAction from '@/components/permissionAction'
import config from '@/config'
import { SIGN_MANAGE } from '@/constants/rbac-code'
import { stringTransform, handleSort } from '@/utils/utils'
import { columns as tableColumns, searchColumns } from './Columns'

const { baseRoute } = config

type Item = SchemaColumnType[number]

interface IProps {
  action: any
  tabKey?: string
}

const SignVoid = (props: IProps) => {
  const navigate = useNavigate()
  const [total, setTotal] = useState(0)
  const { action, tabKey } = props
  const operateColumns: Item = {
    title: '操作',
    key: 'operate',
    fixed: 'right',
    width: 100,
    cell: ({ record }) => (
      <PermissionAction code={SIGN_MANAGE.SIGN_VOID.VIEW} permissions={record.permissions}>
        <Button
          type="link"
          size="small"
          onClick={() => {
            dmsReport.trace(REPORT_EVENT_TYPE.SIGNING_VIEW, {
              ...record,
              _tabKey: tabKey,
            })
            navigate(
              `${baseRoute}/contract/sign-manage/inside/detail?id=${record.id}&tabKey=${tabKey}`,
            )
          }}
        >
          查看
        </Button>
      </PermissionAction>
    ),
  }
  const columns: SchemaColumnType = useMemo(() => tableColumns.concat(operateColumns), [])

  const requestApi = ({ filter, sorter, pagination }) => {
    const { current, pageSize } = pagination
    const { nameOrNum, contractTime, departmentCode, ...rest } = filter
    const params = {
      page: current,
      size: pageSize,
      nameOrNum: stringTransform(nameOrNum),
      contractStartTime: contractTime?.[0].startOf('day').valueOf(),
      contractEndTime: contractTime?.[1].endOf('day').valueOf(),
      departmentCode: departmentCode?.map((item) => item.code),
      sort: sorter && handleSort(sorter),
      ...rest,
    }
    return getSignVoidList(params)
      .then(({ data: resData }) => {
        if (resData.data) {
          setTotal(resData.count)
          return {
            success: true,
            data: resData.data,
            total: resData.count,
          }
        }
        return {
          rows: [],
          total: 0,
        }
      })
      .catch(() => ({
        rows: [],
        total: 0,
      }))
  }

  return (
    <div
      style={{
        background: 'white',
        margin: '0 24px',
      }}
    >
      <SchemaTable
        action={action}
        request={requestApi}
        columns={columns}
        searchColumns={searchColumns}
        toolbar={{
          title: <span>{`已作废（共 ${total} 个）`}</span>,
        }}
        toolbarOptions={{
          setting: true,
        }}
        searchProps={{
          labelWidth: 100,
        }}
      />
    </div>
  )
}

export default SignVoid
