import React, { useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button, message } from 'antd'
// 埋点

import { SchemaTable } from '@amazebird/antd-schema-table'
import { SchemaForm } from '@amazebird/antd-schema-form'
import type { SchemaColumnType } from '@amazebird/antd-schema-table'
import dmsReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'
import config from '@/config'
import useCreateStore from '@/stores/useCreateStore'
import PermissionAction from '@/components/permissionAction'
import modalWrapperHoc from '@/components/modalWrapperHoc'
import UploadModal from '@/components/uploadModal'
import { getSignedList, downloadTemplate, uploadTemplate, getSignDetail } from '@/api/sign-manage'
import { stringTransform, handleSort } from '@/utils/utils'
import { SIGN_MANAGE } from '@/constants/rbac-code'
import { useSigned } from '@/hooks/useSigned'
import { OFFER_BUSINESS_CODE_LIFT, OFFER_BUSINESS_CODE_SWITCH } from '@/constants/sign-manage'
import moment from 'moment'
import { columns as tableColumns, searchColumns } from './Columns'

type Item = SchemaColumnType[number]

const { baseRoute } = config

interface IProps {
  action: any
  tabKey?: string
}

const Signed = (props: IProps) => {
  const navigate = useNavigate()
  const [total, setTotal] = useState(0)
  const [key, setKey] = useState(1)
  const form = SchemaForm.createForm()
  const permissions = useCreateStore((state: any) => state.permissions)
  const { action, tabKey } = props
  const { remove, cancel } = useSigned({ tabKey: tabKey || 'signed' })

  // 根据类型判断是否到达生效日期
  // 主体换签解除的生效日期是合同到期日期
  // 主体换签切换的生效日期是合同开始日期
  const validateEffectiveDate = (record: any) => {
    if (record?.businessItemCode === OFFER_BUSINESS_CODE_LIFT) {
      return Number(record?.contractEndTime) < moment().valueOf()
    }
    return Number(record?.contractStartTime) < moment().valueOf()
  }

  const handleCancel = async (record: any) => {
    if (
      [OFFER_BUSINESS_CODE_LIFT, OFFER_BUSINESS_CODE_SWITCH].includes(record?.businessItemCode) &&
      validateEffectiveDate(record)
    ) {
      const id = record.id
      const {
        data: { data: mainDetail },
      } = await getSignDetail(id)
      if (mainDetail?.relatedRenewVisaSignFileName) {
        const {
          data: { data: renewDetail },
        } = await getSignDetail(mainDetail.relatedRenewVisaSignId)
        if (renewDetail?.status === 1) {
          // 【场景3】已签署“主体换签解除”、已签署“主体换签切换”——已到达生效日期
          message.error('主体换签切换到达生效日期，EHR已写入花名册，不可作废！')
          return
        }
      }
    }
    cancel(record.id, record.businessItemCode)
  }

  const operateColumns: Item = {
    title: '操作',
    key: 'operate',
    fixed: 'right',
    width: 120,
    cell: ({ record }) => {
      const dmsData = {
        ...record,
        _tabKey: tabKey,
      }
      return (
        <span>
          <PermissionAction code={SIGN_MANAGE.SIGNED.VIEW} permissions={record.permissions}>
            <Button
              type="link"
              size="small"
              onClick={() => {
                dmsReport.trace(REPORT_EVENT_TYPE.SIGNING_VIEW, dmsData)
                navigate(
                  `${baseRoute}/contract/sign-manage/inside/detail?id=${record.id}&tabKey=${tabKey}`,
                )
              }}
            >
              查看
            </Button>
          </PermissionAction>
          {/* V1.5.0 屏蔽配置 */}
          {/* <PermissionAction
            code={SIGN_MANAGE.SIGNED.REMOVE}
            permissions={record.permissions}
            tooltip={{
              isShowTip: true,
              title: '操作后将会发送解除协议给员工签署，待解除协议生效后原本签署记录将会失效。',
            }}
          >
            <Button
              type="link"
              size="small"
              onClick={() => {
                dmsReport.trace(REPORT_EVENT_TYPE.DEFUNCT_RESCIND, dmsData)
                remove(record.id)
              }}
            >
              解除合同
            </Button>
          </PermissionAction> */}
          <PermissionAction
            code={SIGN_MANAGE.SIGNED.CANCEL}
            permissions={record.permissions}
            tooltip={{
              isShowTip: true,
              title: '操作后将会发送作废协议给员工签署，签署完成后原本签署记录将会作废。',
              placement: 'topRight',
            }}
          >
            <Button
              type="link"
              size="small"
              onClick={() => {
                dmsReport.trace(REPORT_EVENT_TYPE.OBSOLETE_VOID, dmsData)
                handleCancel(record)
              }}
            >
              作废
            </Button>
          </PermissionAction>
        </span>
      )
    },
  }

  const columns: SchemaColumnType = useMemo(() => tableColumns.concat(operateColumns), [])

  // 导入合同模板
  const uploadTemple = () => {
    modalWrapperHoc(UploadModal)({
      downloadName: '导入合同模板',
      downloadFn: downloadTemplate,
      onOk: uploadTemplate,
      successFn: () => {
        form.resetFields()
        setKey((state) => state + 1)
        action.refresh()
      },
    })
  }

  const requestApi = ({ filter, pagination, sorter }) => {
    const { current, pageSize } = pagination
    const { nameOrNum, contractTime, departmentCode, ...rest } = filter
    const params = {
      page: current,
      size: pageSize,
      nameOrNum: stringTransform(nameOrNum),
      contractStartTime: contractTime?.[0].startOf('day').valueOf(),
      contractEndTime: contractTime?.[1].endOf('day').valueOf(),
      departmentCode: departmentCode?.map((item) => item.code),
      sort: handleSort(sorter),
      ...rest,
    }
    return getSignedList(params)
      .then(({ data: resData }) => {
        if (resData.data) {
          setTotal(resData.count)
          return {
            success: true,
            data: resData.data,
            total: resData.count,
          }
        }
        return {
          rows: [],
          total: 0,
        }
      })
      .catch(() => ({
        rows: [],
        total: 0,
      }))
  }

  return (
    <div
      style={{
        background: 'white',
        margin: '0 24px',
      }}
    >
      <SchemaTable
        form={form}
        action={action}
        request={requestApi}
        columns={columns}
        searchColumns={searchColumns}
        toolbar={{
          title: <span>{`已签署（共 ${total} 个）`}</span>,
          action: (
            <PermissionAction code={SIGN_MANAGE.SIGNED.UPLOAD} permissions={permissions}>
              <Button
                type="primary"
                onClick={() => {
                  dmsReport.trace(REPORT_EVENT_TYPE.SIGNED_IMPORT, {})
                  uploadTemple()
                }}
              >
                导入
              </Button>
            </PermissionAction>
          ),
        }}
        toolbarOptions={{
          setting: true,
        }}
        searchProps={{
          labelWidth: 100,
          key,
        }}
      />
    </div>
  )
}

export default Signed
