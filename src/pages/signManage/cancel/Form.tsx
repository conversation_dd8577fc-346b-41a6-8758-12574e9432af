import React from 'react'
import { SchemaFormProps } from '@amazebird/schema-form'
import type { SchemaType } from '@amazebird/antd-schema-form'
import { SchemaForm, Item } from '@amazebird/antd-schema-form'
import { TreeSelect as Dict } from '@galaxy/dict'
import classNames from 'classnames'

import styles from '../launch/style.module.less'

interface IProps {
  templateList: any[]
  extraClassName?: string
  form: SchemaFormProps['form']
  onPackageChange: (id: string) => void
}

const CancelForm = ({ extraClassName, form, onPackageChange, templateList }: IProps) => {
  const schema: SchemaType = {
    packageId: {
      label: '作废协议',
      component: 'Select',
      required: true,
      options: templateList,
      props: {
        showSearch: true,
        optionFilterProp: 'label',
      },
    },
    reasonCode: {
      label: '作废原因',
      component: Dict,
      props: {
        code: 'OBSOLETE_REASON',
        placeholder: '请选择作废原因',
        showSearch: true,
        allowClear: true,
      },
      rules: [
        {
          required: true,
          message: '请选择作废原因',
        },
      ],
    },
    remark: {
      label: '备注',
      component: 'Input.TextArea',
      required: true,
      min: 10,
      max: 200,
    },
  }

  return (
    <div className={classNames(styles.schemaWrapper, extraClassName)}>
      <SchemaForm
        form={form}
        schema={schema}
        onValuesChange={(values) => {
          if (values.packageId) {
            onPackageChange(values.packageId)
          }
        }}
      >
        <Item field="packageId" />
        <Item field="reasonCode" />
        <Item field="remark" />
      </SchemaForm>
    </div>
  )
}
export default CancelForm
