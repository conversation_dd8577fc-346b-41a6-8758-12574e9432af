import React, { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { message, Skeleton } from 'antd'
import { init as DictInit } from '@galaxy/dict'
import { init as uploadInit } from '@galaxy/upload'
import { getTemplateList } from '@/api/template'
import { pdfjs } from 'react-pdf/dist/esm/entry.webpack5'
import pdfjsWorker from 'react-pdf/dist/cjs/pdf.worker.entry'
import {
  getTemplateAndVar,
  invalidContract,
  getVariablesApi,
  getSignDetail,
} from '@/api/sign-manage'
import { ActiveType, IComponentSlot, SubmitParams } from '@/types/sign-manage'
import config from '@/config'
import { RequestUrlMap } from '@/types/template'
import {
  OFFER_BUSINESS_CODE_LIFT,
  OFFER_BUSINESS_CODE_SWITCH,
  TEMPLATE_TYPE,
} from '@/constants/sign-manage'
import { useExposeModuleRequest } from '@/hooks/useExposeModuleRequest'
import moment from 'moment'
import Launch from '../launch'
import CancelForm from './Form'

const { environment, baseRoute } = config

// 修改 pdfjs 工作路径，避免模块联邦情况下引入不正确的文件地址导致 PDF 文件无法渲染
pdfjs.GlobalWorkerOptions.workerSrc = pdfjsWorker

interface IProps {
  /**
   * 模块联邦请求 origin
   */
  origin: string
  /**
   * 是否为模块联邦使用
   */
  isExposeModule?: boolean
  /**
   * 提交方法 - 暴露给使用模块的提交业务逻辑处理方法
   */
  onSubmit?: (values: SubmitParams) => Promise<any> | any
  /**
   * url map
   * 支持引入模块根据该参数传入对应请求的 url
   * - getTemplateList：请求模板列表
   * - getVariables：请求变量
   * - getTemplateContent：请求模板内容
   * - cancelContract：作废合同
   */
  urlMap?: RequestUrlMap
}

const Cancel = ({ origin, onSubmit, isExposeModule, urlMap }: IProps) => {
  const [search] = useSearchParams()
  const navigate = useNavigate()
  const [getTemplateLoading, setGetTemplateLoading] = useState(true)
  const [templateList, setTemplateList] = useState([])
  const [variables, setVariables] = useState([])
  const [relatedRenewContractNumber, setRelatedRenewContractNumber] = useState(undefined) // 关联换签合同编号，用于confirm弹窗
  // 模块联邦向外暴露模块内容的时候用的请求实例
  const service = origin ? useExposeModuleRequest({ origin, urlMap }) : null

  // 获取模板列表
  const getTemplate = async () => {
    setGetTemplateLoading(true)
    try {
      const { data: result } = service
        ? await service.getTemplateList(TEMPLATE_TYPE.CANCEL, 1)
        : await getTemplateList({
            fddStatus: 1,
            type: TEMPLATE_TYPE.CANCEL,
            typeCode: TEMPLATE_TYPE.CANCEL,
            isSupportInner: true,
            isShowSealKeyWord2: false,
          })

      const list = result?.data.map((v) => ({
        label: v.name,
        value: isExposeModule ? v.number : v.id,
      }))
      setTemplateList(list)
    } finally {
      setGetTemplateLoading(false)
    }
  }

  // 获取变量
  const getVariables = async (id?) => {
    const { data: result } =
      service && id ? await service.getVariablesApi(id) : await getVariablesApi()
    setVariables(result.data.filter((item) => item.isEnable))
  }

  const onPackageChange = (id) => {
    if (isExposeModule) {
      getVariables(id)
    }
  }

  // 根据类型判断是否到达生效日期(场景2)
  // 主体换签解除的生效日期是合同到期日期
  // 主体换签切换的生效日期是合同开始日期
  const validateEffectiveDate = (record: any) => {
    if (record?.businessItemCode === OFFER_BUSINESS_CODE_LIFT) {
      return Number(record?.contractEndTime) > moment().valueOf()
    }
    return Number(record?.contractStartTime) > moment().valueOf()
  }

  // 获取主体合同以及换签合同详情，并判断是否需要提示弹窗
  // 针对于【场景2】已签署“主体换签解除”、已签署“主体换签切换”——未到达生效日期
  // 并且满足 关联换签合同是否存在& 关联换签合同状态=已签署 & 关联换签合同的关联作废合同不存在
  const handleDetails = async () => {
    const businessItemCode = search.get('businessItemCode') || ''
    if ([OFFER_BUSINESS_CODE_LIFT, OFFER_BUSINESS_CODE_SWITCH].includes(businessItemCode)) {
      const id = search.get('id')
      const {
        data: { data: mainDetail },
      } = await getSignDetail(id)
      if (mainDetail?.relatedRenewVisaSignFileName && validateEffectiveDate(mainDetail)) {
        const {
          data: { data: renewDetail },
        } = await getSignDetail(mainDetail.relatedRenewVisaSignId)
        if (renewDetail?.status === 1 && !renewDetail?.relatedSignId) {
          setRelatedRenewContractNumber(renewDetail?.contractNumber)
        }
      }
    }
  }

  useEffect(() => {
    getTemplate()
    handleDetails()
    /**
     * 非模块联邦时直接进行变量的请求
     *
     * 模块联邦时需要与模板 id 相关联再进行请求(onPackageChange)
     */
    if (!isExposeModule) {
      getVariables()
    }

    // 模块联邦情况下需要进行模块字典初始化
    if (isExposeModule) {
      DictInit({
        env: environment,
      })

      uploadInit({
        env: environment,
      })
    }
  }, [])

  return (
    <Launch
      isExposeModule={isExposeModule}
      variables={variables}
      componentSlot={(props: IComponentSlot) =>
        !getTemplateLoading ? (
          <CancelForm {...props} templateList={templateList} onPackageChange={onPackageChange} />
        ) : (
          <Skeleton style={{ padding: '30px 20px' }} />
        )
      }
      relatedRenewContractNumber={relatedRenewContractNumber}
      apiRequest={(activeName: ActiveType) =>
        ({ packageId, remark, reasonCode, vars }: any) => {
          if (activeName === 'GET_TEMPLATE_CONTENT') {
            const fn = service ? service.getTemplateAndVar : getTemplateAndVar

            return fn({
              signId: search.get('id') as string,
              templateId: packageId,
            })
          }
          if (activeName === 'RESULT') {
            const params = {
              signId: search.get('id') as string,
              templateId: packageId,
              remark,
              reasonCode,
              variables: vars,
            }

            if (onSubmit) {
              return onSubmit(params)
            }

            return (service ? service.invalidContract : invalidContract)(params).then(() => {
              message.success('保存成功')
              navigate(`${baseRoute}/contract/sign-manage/inside?activeKey=signing`)
            })
          }
          return Promise.reject()
        }}
    />
  )
}

export default Cancel
