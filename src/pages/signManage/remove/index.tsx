import React, { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { message } from 'antd'
import { ActiveType, IComponentSlot } from '@/types/sign-manage'
import { getTemplateAndVar, dischargeContract, getVariablesApi } from '@/api/sign-manage'
import config from '@/config'
import Launch from '../launch'
import RemoveForm from './Form'

const { baseRoute } = config

function Remove() {
  const [variables, setVariables] = useState([])
  const [search] = useSearchParams()
  const navigate = useNavigate()

  // 获取变量
  const getVariables = async () => {
    const { data: result } = await getVariablesApi()
    setVariables(result.data.filter((item) => item.isEnable))
  }

  useEffect(() => {
    getVariables()
  }, [])

  return (
    <Launch
      variables={variables}
      componentSlot={(props: IComponentSlot) => <RemoveForm {...props} />}
      apiRequest={(activeName: ActiveType) =>
        ({ packageId, remark, reasonCode, vars }: any) => {
          if (activeName === 'GET_TEMPLATE_CONTENT') {
            return getTemplateAndVar({
              signId: search.get('id') as string,
              templateId: packageId,
            })
          }
          if (activeName === 'RESULT') {
            return dischargeContract({
              signId: search.get('id') as string,
              templateId: packageId,
              remark,
              reasonCode,
              variables: vars,
            }).then(() => {
              message.success('保存成功')
              navigate(`${baseRoute}/contract/sign-manage/inside?activeKey=signing`)
            })
          }
          return Promise.reject()
        }}
    />
  )
}

export default Remove
