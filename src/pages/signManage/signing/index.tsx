import React, { useState, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button, message } from 'antd'
import { SchemaTable } from '@amazebird/antd-schema-table'
import type { SchemaColumnType } from '@amazebird/antd-schema-table'
import config from '@/config'
import { handleSort, stringTransform } from '@/utils/utils'
// 埋点
import dmsReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'

import { getSigningList, resendApi, invalidApi, linkCopy } from '@/api/sign-manage'
import { SIGN_MANAGE } from '@/constants/rbac-code'
import PermissionAction from '@/components/permissionAction'
import { OFFER_BUSINESS_CODE } from '@/constants/sign-manage'
import { columns as tableColumns, searchColumns } from './Columns'
import { resendSign, invalidSign } from '../actions'

const { baseRoute } = config

type Item = SchemaColumnType[number]

interface IProps {
  action: any
  tabKey?: string
}

const Signing = (props: IProps) => {
  const [total, setTotal] = useState(0)
  const [copyLoading, setCopyLoading] = useState(false)
  const [copyIndex, setCopyIndex] = useState<number | null>(null)
  const navigate = useNavigate()
  const { action, tabKey } = props

  // 重发
  const handleResend = async (record, value) => {
    await resendApi({
      signId: record.id,
      ...value,
    })
    action.refresh()
    message.success('操作成功')
  }

  // 失效
  const handleInvalid = async (record, value) => {
    await invalidApi({
      signId: record.id,
      ...value,
    })
    action.refresh()
    message.success('操作成功')
  }

  const operateColumns: Item = {
    title: '操作',
    key: 'operate',
    fixed: 'right',
    width: 200,
    cell: ({ record, index }) => {
      const dmsData = {
        ...record,
        _tabKey: tabKey,
      }
      return (
        <span>
          <PermissionAction code={SIGN_MANAGE.SIGNING.VIEW} permissions={record.permissions}>
            <Button
              type="link"
              size="small"
              onClick={() => {
                dmsReport.trace(REPORT_EVENT_TYPE.SIGNING_VIEW, dmsData)
                navigate(
                  `${baseRoute}/contract/sign-manage/inside/detail?id=${record.id}&tabKey=${tabKey}`,
                )
              }}
            >
              查看
            </Button>
          </PermissionAction>
          <PermissionAction code={SIGN_MANAGE.SIGNING.RESEND} permissions={record.permissions}>
            <Button
              type="link"
              size="small"
              onClick={() => {
                dmsReport.trace(REPORT_EVENT_TYPE.SIGNING_RESEND, dmsData)
                resendSign({
                  onOk: (values) => handleResend(record, values),
                })
              }}
            >
              重发
            </Button>
          </PermissionAction>
          <PermissionAction code={SIGN_MANAGE.SIGNING.EXPIRED} permissions={record.permissions}>
            <Button
              type="link"
              size="small"
              onClick={() => {
                dmsReport.trace(REPORT_EVENT_TYPE.SIGNED_FAILURE, dmsData)
                invalidSign({
                  record,
                  onOk: (values) => handleInvalid(record, values),
                })
              }}
            >
              失效
            </Button>
          </PermissionAction>
          <PermissionAction code={SIGN_MANAGE.SIGNING.COPY} permissions={record.permissions}>
            <Button
              type="link"
              size="small"
              disabled={record.businessItemCode === OFFER_BUSINESS_CODE}
              loading={copyLoading && copyIndex === index}
              onClick={async () => {
                setCopyIndex(index)
                setCopyLoading(true)
                try {
                  const { data: result } = await linkCopy(record.id)
                  await navigator.clipboard.writeText(result.data)
                  message.success('已复制签署链接')
                } catch (error) {
                  message.error('复制链接失败')
                } finally {
                  setCopyIndex(null)
                  setCopyLoading(false)
                }
              }}
            >
              复制链接
            </Button>
          </PermissionAction>
        </span>
      )
    },
  }

  const requestApi = ({ pagination, filter, sorter }) => {
    const { current, pageSize } = pagination
    const { nameOrNum, contractTime, department, postLevel, ...restFilter } = filter

    const params = {
      ...restFilter,
      nameOrNum: stringTransform(nameOrNum),
      postLevelCode: postLevel,
      departmentCode: department?.map((item) => item.code),
      contractStartTime: contractTime?.[0].startOf('day').valueOf(),
      contractEndTime: contractTime?.[1].endOf('day').valueOf(),
      page: current,
      size: pageSize,
      sort: handleSort(sorter),
    }
    return getSigningList(params)
      .then(({ data: resData }) => {
        if (resData.data) {
          setTotal(resData.count)
          return {
            success: true,
            data: resData.data,
            total: resData.count,
          }
        }
        return {
          rows: [],
          total: 0,
        }
      })
      .catch(() => ({
        rows: [],
        total: 0,
      }))
  }

  const columns: SchemaColumnType = useMemo(
    () => tableColumns.concat(operateColumns),
    [copyLoading],
  )

  return (
    <div
      style={{
        background: 'white',
        margin: '0 24px',
      }}
    >
      <SchemaTable
        action={action}
        request={requestApi}
        columns={columns}
        searchColumns={searchColumns}
        searchProps={{
          labelWidth: 100,
        }}
        toolbar={{
          title: <span>{`签署中（共 ${total} 个）`}</span>,
        }}
        toolbarOptions={{
          setting: true,
        }}
      />
    </div>
  )
}

export default Signing
