/*
 * @Author: 梁美座 <EMAIL>
 * @Date: 2024-07-17 10:03:00
 * @LastEditors: lichao <EMAIL>
 * @LastEditTime: 2025-02-27 19:57:13
 * @FilePath: /contract-webapp/src/pages/signManage/component/InvalidModal.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useState } from 'react'
import { Modal, Alert, Button, message } from 'antd'
import { TreeSelect as Dict } from '@galaxy/dict'
import { SchemaForm, Item, Observer } from '@amazebird/antd-schema-form'
import type { SchemaType } from '@amazebird/antd-schema-form'
import { RESIGN_BUSINESS_CODE, RESIGN_INVALID_REASON_CODE } from '@/constants'
import '@amazebird/antd-business-field'
import config from '@/config'
import styles from './style.module.less'

const { baseRoute } = config

const InvalidModal = (props: any) => {
  const { visible, close, record, onOk: okClick } = props
  const form = SchemaForm.createForm()
  const [loading, setLoading] = useState(false)

  const schema: SchemaType = {
    reasonTypeCode: {
      label: '失效原因',
      component: Dict,
      props: {
        code: 'INVALID_REASON',
        placeholder: '请选择失效原因',
        showSearch: true,
        allowClear: true,
        filter: (data) => {
          if (record.businessItemCode !== RESIGN_BUSINESS_CODE) {
            return !RESIGN_INVALID_REASON_CODE.includes(data.value)
          }
          return data
        },
      },
      required: [true, '请选择失效原因'],
    },
    remark: {
      label: '备注',
      component: 'Input.TextArea',
      required: true,
      min: 10,
      max: 200,
    },
    attachment: {
      label: '附件',
      component: 'Upload',
      visible: Observer({
        watch: 'reasonTypeCode',
        action: (reasonTypeCode) => {
          return reasonTypeCode === RESIGN_INVALID_REASON_CODE[0]
        },
      }),
      required: true,
      props: {
        clientCode: 'CONTRACT',
        oss: { service: '/admin/contract' },
        accept: 'application/pdf,image/jpeg',
        fileNameRender: (originNode, file) => {
          // 获取文件扩展名
          const extension = file.name.split('.').pop().toLowerCase()
          const uuid = file?.uuid
          return (
            <div style={{ whiteSpace: 'normal', wordBreak: 'break-all' }}>
              <Button
                type="link"
                style={{
                  padding: 0,
                  width: '100%',
                  textAlign: 'start',
                }}
                target="_blank"
                size="small"
                href={`${baseRoute}/contract/sign-manage/inside/preview?name=${file.name}&ext=${extension}&uuid=${uuid}`}
              >
                <span style={{ wordBreak: 'break-all', whiteSpace: 'pre-wrap' }}>{file.name}</span>
              </Button>
            </div>
          )
        },
        onChange: (fileList) => {
          if (fileList.length === 5) {
            message.warn('最多支持上传5个文件')
          }
        },
        maxNum: 5,
        remark: '支持扩展名：.pdf .jpg',
        fileSizeLimit: 50,
        multiple: true,
      },
    },
  }

  const onOk = async () => {
    try {
      const values = await form?.validateFields()
      const { attachment, ...restValues } = values
      const params = { ...restValues }
      if (attachment && attachment.length > 0) {
        const attachmentParam = JSON.stringify(
          attachment.map((item) => ({
            name: item.name,
            uuid: item.uuid,
          })),
        )
        params.invalidAttachment = attachmentParam
      }
      setLoading(true)
      okClick(params)
        .then(() => {
          close()
        })
        .finally(() => {
          setLoading(false)
        })
    } catch (error) {
      console.error(error)
    }
  }

  return (
    <Modal
      title="失效"
      open={visible}
      onCancel={close}
      onOk={onOk}
      width={650}
      okButtonProps={{
        loading,
      }}
    >
      <Alert
        className={styles.alert}
        message="操作后签署记录将流转到【已失效】页签。"
        type="info"
        showIcon
      />
      <SchemaForm
        className={styles.schemaForm}
        form={form}
        schema={schema}
        labelCol={{
          span: 6,
        }}
      >
        <Item field="reasonTypeCode" />
        <Item field="remark" />
        <Item field="attachment" />
      </SchemaForm>
    </Modal>
  )
}

export default InvalidModal
