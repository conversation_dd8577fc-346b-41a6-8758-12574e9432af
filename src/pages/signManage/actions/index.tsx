import { ModalFuncProps } from 'antd'
import modalWrapperHoc from '@/components/modalWrapperHoc'
import CancelModal from '../component/CancelModal'
import RemoveModal from '../component/RemoveModal'
import ResendModal from '../component/ResendModal'
import InvalidModal from '../component/InvalidModal'

// 作废
export const cancelSign = (params) => {
  modalWrapperHoc(CancelModal)(params)
}

// 解除
export const removeSign = (params) => {
  modalWrapperHoc(RemoveModal)(params)
}

// 重发
export const resendSign = (params: ModalFuncProps) => {
  modalWrapperHoc(ResendModal)(params)
}

// 无效
export const invalidSign = (params) => {
  modalWrapperHoc(InvalidModal)(params)
}
