import React, { useState, useEffect } from 'react'
import { useStore } from '@/stores'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { <PERSON><PERSON>, But<PERSON> } from 'antd'
import type { TabsProps } from 'antd'
import { getPermission } from '@galaxy/rbac'
// 埋点
import { createAction } from '@amazebird/antd-schema-table'
import dmsReport, { REPORT_EVENT_TYPE } from '@/utils/dmsReport'

import PermissionAction from '@/components/permissionAction'
import config from '@/config'
import { SIGN_MANAGE } from '@/constants/rbac-code'
import useCreateStore from '@/stores/useCreateStore'
import Signing from './signing'
import Signed from './signed'
import SignExpired from './signExpired'
import SignVoid from './signVoid'

import styles from './styles.module.less'

const { baseRoute } = config

const SignManage = () => {
  const store = useStore((state) => state)
  const { setExtra } = store

  const navigate = useNavigate()
  const [search, setSearch] = useSearchParams()
  const [activeKey, setActiveKey] = useState<any>(search.get('activeKey') || 'signing')
  const permissions = useCreateStore((state: any) => state.permissions)
  const actions = {
    signing: createAction(),
    signed: createAction(),
    signExpired: createAction(),
    signVoid: createAction(),
  }

  const items = [
    {
      key: 'signing',
      label: '签署中',
      children: <Signing action={actions.signing} tabKey="signing" />,
      hasPermission: getPermission(SIGN_MANAGE.TABS.SIGNING),
    },
    {
      key: 'signed',
      label: '已签署',
      children: <Signed action={actions.signed} tabKey="signed" />,
      hasPermission: getPermission(SIGN_MANAGE.TABS.SIGNED),
    },
    {
      key: 'signExpired',
      label: '已失效',
      children: <SignExpired action={actions.signExpired} tabKey="signExpired" />,
      hasPermission: getPermission(SIGN_MANAGE.TABS.SIGN_EXPIRED),
    },
    {
      key: 'signVoid',
      label: '已作废',
      children: <SignVoid action={actions.signVoid} tabKey="signVoid" />,
      hasPermission: getPermission(SIGN_MANAGE.TABS.SIGN_VOID),
    },
  ]

  const menu: TabsProps['items'] = items.filter((i: any) => i.hasPermission)

  const onChange = (key) => {
    setActiveKey(key)
    actions[key].refresh()
    setSearch((prev) => {
      prev.set('activeKey', key)
      return prev
    })
  }

  useEffect(() => {
    setExtra(
      <PermissionAction code={SIGN_MANAGE.START_SIGN} permissions={permissions}>
        <Button
          type="primary"
          onClick={() => {
            dmsReport.trace(REPORT_EVENT_TYPE.SIGNING_SEND, {
              activeKey,
              label: items.find((item) => item.key === activeKey)?.label,
            })
            navigate(`${baseRoute}/contract/sign-manage/inside/launch`)
          }}
        >
          发起签署
        </Button>
      </PermissionAction>,
    )
    return () => {
      setExtra(null)
    }
  }, [permissions, activeKey])

  return (
    <div className={styles.signManage}>
      <Tabs defaultActiveKey="signing" items={menu} onChange={onChange} activeKey={activeKey} />
    </div>
  )
}

export default SignManage
