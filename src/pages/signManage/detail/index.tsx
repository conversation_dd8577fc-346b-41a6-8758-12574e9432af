import React, { useEffect, useState } from 'react'
import { useStore } from '@/stores'
import { useLocation } from 'react-router-dom'
import queryString from 'query-string'
import classNames from 'classnames'
import { getSignDetail, getSignLogApi } from '@/api/sign-manage'
import { SignLogProps } from '@/types/sign-manage'
import { SIGN_STATUS } from '@/constants/sign-manage'
import Extra from './Extra'
import BasicInfo from './BasicInfo'
import Content from './Content'

import styles from './styles.module.less'

const SignDetail = () => {
  const store = useStore((state) => state)
  const { setTitle, setExtra } = store

  const [data, setData] = useState({} as any)
  const [signLog, setSignLog] = useState<SignLogProps[]>([])
  const location = useLocation()
  const { id, tabKey } = queryString.parse(location.search)

  const getDetail = async () => {
    const { data: result } = await getSignDetail(id)
    if (result?.data) {
      setData(result.data)
    }
    return result.data
  }

  const getSignLog = (contractId) => {
    getSignLogApi(contractId).then(({ data: result }) => {
      if (result?.data) {
        setSignLog(result.data)
      }
    })
  }

  const getPageData = () => {
    getDetail().then((detail) => {
      if (detail.fddContractId) {
        getSignLog(detail.fddContractId)
      }
    })
  }

  useEffect(() => {
    setTitle(
      <span className={styles.title}>
        {data.userNum ? `${data.userNum}: ${data.userName}` : data.userName}
        <span
          className={classNames([
            styles.status,
            styles[SIGN_STATUS.find((item) => item.value === data.status)?.color || 'default'],
          ])}
        >
          {SIGN_STATUS.find((item) => item.value === data.status)?.label || '--'}
        </span>
      </span>,
    )
    setExtra(<Extra data={data} refresh={getPageData} />)
    return () => {
      setExtra(null)
      setTitle(null)
    }
  }, [data])

  useEffect(() => {
    getPageData()
  }, [id])

  return (
    <div>
      <BasicInfo data={data} />
      <Content data={data} tabKey={tabKey as string} signLogData={signLog} />
    </div>
  )
}

export default SignDetail
