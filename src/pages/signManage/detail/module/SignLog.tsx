import React from 'react'
import { message, Tooltip } from 'antd'
import { CopyOutlined } from '@ant-design/icons'
import { SchemaTable, SchemaColumnType } from '@amazebird/antd-schema-table'

import styles from './styles.module.less'

interface IProps {
  dataSource: any[]
}

// 状态
const STATUS = [
  { value: 0, label: '未开始', color: '#C7C7C7' },
  { value: 1, label: '成功', color: '#52C41A' },
  { value: 2, label: '失败', color: '#FF4D50' },
  { value: 3, label: '进行中', color: '#1890FF' },
]

const SignLog = ({ dataSource = [] }: IProps) => {
  const handleCopy = (text) => {
    try {
      // 创建输入框
      const textarea = document.createElement('textarea')
      document.body.appendChild(textarea)
      // 隐藏此输入框
      textarea.style.position = 'absolute'
      textarea.style.clip = 'rect(0 0 0 0)'
      // 赋值
      textarea.value = text
      // 选中
      textarea.select()
      // 复制
      document.execCommand('copy', true)
      document.body.removeChild(textarea)
      message.success('复制成功')
    } catch (error) {
      message.error('复制失败')
    }
  }

  const CellToolTip = ({ text }: any) => (
    <>
      <Tooltip title={text} placement="topLeft">
        {text}
      </Tooltip>
      <CopyOutlined onClick={() => handleCopy(text)} className={styles.copyIcon} />
    </>
  )

  const columns: SchemaColumnType = [
    {
      title: '序号',
      width: 30,
      key: 'index',
      align: 'center',
      render: (...props) => {
        const [, , index] = props
        return (
          <div style={{ position: 'relative' }}>
            <span>{index + 1}</span>
          </div>
        )
      },
    },
    {
      title: '发起方',
      width: 50,
      dataIndex: 'clientName',
      cell: 'Text',
    },
    {
      title: '接收方',
      width: 50,
      dataIndex: 'serverName',
      cell: 'Text',
    },
    {
      title: '日志内容',
      width: 50,
      dataIndex: 'content',
      cell: 'Text',
    },
    {
      title: '处理状态',
      dataIndex: 'status',
      width: 50,
      cell: 'Status',
      options: STATUS,
    },
    {
      title: '流转时间',
      width: 50,
      dataIndex: 'transferTime',
      cell: 'DateTime',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 120,
      ellipsis: true,
      cell: CellToolTip,
    },
  ]

  return (
    <SchemaTable
      rowKey="failMessage"
      stripe={false}
      tableClassName={styles.signLogTable}
      dataSource={dataSource}
      columns={columns}
      pagination={false}
      footer={() => null}
    />
  )
}

export default SignLog
