import React from 'react'
import { Divider, Row, Col } from 'antd'
import moment from 'moment'

// 埋点
import { SignDetailOtherProps, SignLogProps } from '@/types/sign-manage'
import SignLog from './module/SignLog'
import OtherInfo from './module/OtherInfo'
import SignInfo from './module/SignInfo'

import styles from './styles.module.less'

interface IProps {
  data: SignDetailOtherProps
  signLogData: SignLogProps[]
  tabKey: string
}

// eslint-disable-next-line max-lines-per-function
const Content = ({ data, signLogData, tabKey }: IProps) => {
  return (
    <div className={styles.content}>
      <h3>合同信息 </h3>
      <Divider />
      <div className={styles.sectionWrapper}>
        <Row gutter={16}>
          <Col className={styles.infoItem} span={8}>
            <span className={styles.key}>合同主体：</span>
            <span>{data.contractSubjectName || '--'}</span>
          </Col>
          <Col className={styles.infoItem} span={8}>
            <span className={styles.key}>合同/协议类型：</span>
            <span>{data.contractTypeName || '--'}</span>
          </Col>
          <Col className={styles.infoItem} span={8}>
            <span className={styles.key}>合同编号：</span>
            <span>{data.contractNumber || '--'}</span>
          </Col>
          <Col className={styles.infoItem} span={8}>
            <span className={styles.key}>法大大ID：</span>
            <span>{data.fddContractId || '--'}</span>
          </Col>
          <Col className={styles.infoItem} span={8}>
            <span className={styles.key}>合同开始日期：</span>
            <span>
              {data.contractStartTime ? moment(data.contractStartTime).format('YYYY-MM-DD') : '--'}
            </span>
          </Col>
          <Col className={styles.infoItem} span={8}>
            <span className={styles.key}>合同到期日期：</span>
            <span>
              {data.contractEndTime ? moment(data.contractEndTime).format('YYYY-MM-DD') : '--'}
            </span>
          </Col>
          <Col className={styles.infoItem} span={8}>
            <span className={styles.key}>合同期限：</span>
            <span>{data.contractDeadline || '--'}</span>
          </Col>
        </Row>
        {/* <Row gutter={16}>
          <Col className={styles.infoItem} span={8}>
            <span className={styles.key}>合同开始日期：</span>
            <span>
              {data.contractStartTime ? moment(data.contractStartTime).format('YYYY-MM-DD') : '--'}
            </span>
          </Col>
          <Col className={styles.infoItem} span={8}>
            <span className={styles.key}>合同到期日期：</span>
            <span>
              {data.contractEndTime ? moment(data.contractEndTime).format('YYYY-MM-DD') : '--'}
            </span>
          </Col>
          <Col className={styles.infoItem} span={8}>
            <span className={styles.key}>合同期限：</span>
            <span>{data.contractDeadline || '--'}</span>
          </Col>
        </Row> */}
      </div>
      <h3>签署信息 </h3>
      <Divider />
      <div className={styles.sectionWrapper}>
        <SignInfo data={data} tabKey={tabKey} />
      </div>
      <h3>签署日志 </h3>
      <Divider />
      <div className={styles.sectionWrapper}>
        <SignLog dataSource={signLogData} />
      </div>
      <h3>其他信息 </h3>
      <Divider />
      <div className={styles.sectionWrapper}>
        <OtherInfo data={data} />
      </div>
    </div>
  )
}

export default Content
