import React from 'react'
import moment from 'moment'
import DescriptionsWrap from '@/components/descriptionsWrap'
import { SignDetailProps } from '@/types/sign-manage'

import styles from './styles.module.less'

interface IProps {
  data: SignDetailProps
}
const BasicInfo = (props: IProps) => {
  const { data: dataSource } = props
  const {
    postLevelName,
    postName,
    entryDate,
    phoneNumber,
    idCardNumber,
    workCityName,
    departmentName,
  } = dataSource

  const data = [
    {
      label: '职位',
      value: postLevelName,
    },
    {
      label: '岗位',
      value: postName,
    },
    {
      label: '入职日期',
      value: entryDate && moment(entryDate).format('YYYY-MM-DD'),
    },
    {
      label: '手机号',
      value: phoneNumber,
    },
    {
      label: '身份证号码',
      value: idCardNumber,
    },
    {
      label: '工作城市',
      value: workCityName,
    },
    {
      label: '部门',
      value: departmentName,
    },
  ]
  return (
    <div className={styles.basicInfo}>
      <DescriptionsWrap data={data} />
    </div>
  )
}

export default BasicInfo
