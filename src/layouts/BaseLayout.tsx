import React, { useState, useEffect } from 'react'
import { useNavigate, useLocation, Outlet } from 'react-router-dom'
import { useStore } from '@/stores'
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons'
import ProLayout, { PageContainer } from '@ant-design/pro-layout'
import queryString from 'query-string'
import config from '@/config'
import WaterMark from '@/components/base/WaterMark'
import User from '@/components/base/User'
import RouteFactory from '@/router/RouteFactory'
import route from '@/router/route'
import dmsReport from '@/utils/dmsReport'

import './BaseLayout.module.less'

const { baseRoute } = config

const routeFactory = new RouteFactory(route)

const { projectName } = config

interface StateProps {
  isWechat: string
  isHideNav: string
  isHideMenu: string
}

const BaseLayout: React.FC = function () {
  const [collapsed, setCollapsed] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const {
    isHideNav = false,
    isHideMenu = false,
    isWechat = false,
  } = queryString.parse(location.search) as unknown as StateProps
  const useHideNav = sessionStorage.getItem('isHideNav') || isHideNav
  const useHideMenu = sessionStorage.getItem('isHideMenu') || isHideMenu
  const title = useStore((state) => state.title)

  const useCacheUrlParams = () => {
    isWechat && sessionStorage.setItem('isWechat', 'IS_WECHAT')
    isHideNav && sessionStorage.setItem('isHideNav', 'IS_HIDE_NAV')
    isHideMenu && sessionStorage.setItem('isHideMenu', 'IS_HIDE_MENU')
  }

  const headerConfig: { title?: React.ReactNode } = {}
  if (React.isValidElement(title)) {
    headerConfig.title = title
  }

  useEffect(() => {
    useCacheUrlParams()
  }, [])

  dmsReport.listenPageViewHook()

  return (
    <div
      style={{
        minWidth: 1200,
        overflowX: 'auto',
      }}
    >
      <ProLayout
        menuRender={useHideMenu ? false : undefined}
        breadcrumbRender={useHideNav ? false : undefined}
        headerRender={useHideNav ? false : undefined}
        route={routeFactory.route}
        fixedHeader
        fixSiderbar
        title={projectName}
        id="test-pro-layout"
        collapsed={collapsed}
        collapsedButtonRender={false}
        onCollapse={setCollapsed}
        menuHeaderRender={() => (
          <div
            id="customize_menu_header"
            onClick={() => {
              navigate(`${baseRoute}/`)
            }}
          >
            {collapsed ? (
              <span
                className="pupulogo"
                style={{
                  fontSize: '14px',
                  marginRight: '5px',
                }}
              >
                pupu
              </span>
            ) : (
              <span
                className="pupulogo"
                style={{
                  fontSize: '20px',
                  marginRight: '5px',
                }}
              >
                pupu
              </span>
            )}
            {collapsed ? '' : projectName}
          </div>
        )}
        menuItemRender={(item, dom) => (
          <a
            onClick={() => {
              if (item.path) {
                navigate(item.path)
              }
            }}
          >
            {dom}
          </a>
        )}
        headerContentRender={() => (
          <div
            onClick={() => setCollapsed(!collapsed)}
            style={{
              display: 'inline-block',
              padding: '0 10px',
              cursor: 'pointer',
              fontSize: '16px',
            }}
          >
            {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          </div>
        )}
        rightContentRender={() => (
          <div
            style={{
              marginRight: '10px',
            }}
          >
            {/* <Theme /> */}
            <User />
          </div>
        )}
      >
        <PageContainer
          header={{
            title: ''
          }}
          style={{
            minWidth: '1150px',
          }}
        >
          <div
            style={{
              minHeight: 'calc(100vh - 200px)',
            }}
          >
            <WaterMark>
              <Outlet />
            </WaterMark>
          </div>
        </PageContainer>
      </ProLayout>
    </div>
  )
}

export default BaseLayout
