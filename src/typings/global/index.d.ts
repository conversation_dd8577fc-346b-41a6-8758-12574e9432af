declare module '*.less' {
  const resource: { [key: string]: string }
  export = resource
}
declare module 'components/PageWrap/index' {
  const content: any
  export = content
}

type SvgrComponent = React.StatelessComponent<React.SVGAttributes<SVGElement>>
declare module '*.svg' {
  const content: SvgrComponent
  export default content
}

declare module '*.png' {
  const content: any
  export default content
}
declare module '*.jpg' {
  const content: any
  export default content
}
declare module '*.jpeg' {
  const content: any
  export default content
}
declare module '*.gif' {
  const content: any
  export default content
}

declare module '*.xlsx' {
  const content: any
  export default content
}

declare const NODE_ENV: string
declare interface IRoute {
  name: string
  component: any
  path: string
}
declare interface Window {
  __POWERED_BY_QIANKUN__: any
  __INJECTED_PUBLIC_PATH_BY_QIANKUN__: any
  __REDUX_DEVTOOLS_EXTENSION_COMPOSE__: any
}
