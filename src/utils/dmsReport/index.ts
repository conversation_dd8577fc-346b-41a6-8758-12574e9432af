import queryString from 'query-string'
import { Report } from '@pupu/elangsion-web'
import { useLocation } from 'react-router-dom'

import { useEffect } from 'react'
import uc from '@galaxy/uc'
import config from '@/config'
import flatRoute from './flatRoute'
import { REPORT_EVENT_TYPE } from './constants'

declare type TraceEnv = 'pro' | 'qa' | 'dev'
declare type DhrEnv = 'prod' | 'pre' | 'itPre' | 'test' | 'dev'

const { dmsConfig, environment } = config
const { appId, appVersion, idHexStr } = dmsConfig

/**
 * env环境判断dev和test都是dev环境
 * pre是qa环境
 * prod是pro环境
 */
const transformLibEnv = (libEnv: DhrEnv) => {
  let libEnvTemp = ''
  switch (libEnv) {
    case 'test':
      libEnvTemp = 'dev'
      break
    case 'pre':
      libEnvTemp = 'qa'
      break
    case 'itPre':
      libEnvTemp = 'pro' // qa
      break
    case 'prod':
      libEnvTemp = 'pro'
      break
    default:
      libEnvTemp = 'pro'
  }
  return libEnvTemp
}

type pageType = {
  from: string
  fromSearch: string
  fromMenuName: string
  current: string
  currentMenuName: string
}

class Reports {
  report: Report

  userInfo: any

  pathInstance: pageType = {
    from: '',
    fromSearch: '',
    fromMenuName: '',
    current: '',
    currentMenuName: '',
  }

  constructor() {
    this.report = new Report({
      appId,
      appVersion,
      env: transformLibEnv(environment) as TraceEnv,
      idHexStr,
    })

    this.init()
  }

  /**
   * 内部初始化完成标志
   */
  init = () => {
    this.report.done()
  }

  /**
   * 登录注册who
   */
  login = (userId: string) => {
    this.report.login(userId)
  }

  getUserNum = async () => {
    if (this.userInfo) {
      return
    }
    const res = await uc.getUserInfo()
    this.userInfo = res
    this.login(this.userInfo?.num)
  }

  setPath = (pathName: string, isFrom?: boolean) => {
    const key = isFrom ? 'from' : 'current'
    this.pathInstance[key] = pathName
    this.pathInstance[`${key}MenuName`] = flatRoute?.find((item) =>
      pathName.endsWith(item.path),
    )?.name
  }

  /**
   * @param eventName 事件名称
   * @param params 业务报文
   */
  trace = async (eventName: REPORT_EVENT_TYPE | string, params: any) => {
    await this.getUserNum()
    this.report.setStaticParams('when.time_report', Date.now())
    const locationName = this.pathInstance.from
    // tab映射
    const route = flatRoute?.find((item) => locationName.endsWith(item.path))
    let menuName = route?.name
    if (route?.dms?.tabMap) {
      // 获取 activeKey
      const query = queryString.parse(window.location.search)
      const activeKey = query?.activeKey || query?.tabKey
      if (activeKey) {
        menuName += ` - ${route?.dms?.tabMap[activeKey as string] || ''}`
      } else {
        // 没找到，从记录参数进行查找
        const newQuery = queryString.parse(this.pathInstance.fromSearch)
        menuName += ` - ${
          route?.dms?.tabMap[
            newQuery?.activeKey || newQuery?.tabKey || route?.dms?.tabMap?.default
          ] || ''
        }`
      }
    }
    this.report.trace(eventName, {
      from: {
        from_page_name: locationName, // TODO 从详情页跳转还存在问题
        pageName: menuName,
        currentName: flatRoute?.find((item) => window.location?.pathname.endsWith(item.path))?.name,
      },
      context: Array.isArray(params)
        ? {
            list: params,
          }
        : params,
    })
  }

  /**
   * 监听页面浏览事件并上报
   */
  listenPageViewHook = () => {
    const location = useLocation()
    const str = location.pathname + location.search // 需要加location.search 处理规则设置先埋点后接口跳转，出现from地址不对的情况。TODO 后期需研究更好的方案
    useEffect(() => {
      this.setPath(window.location.pathname)
      return () => {
        // 主应用的菜单切换控制不了，如何处理？
        // location?.pathname 会出现菜单切换，from使用current
        this.setPath(location.pathname, true)
        // 用于记录参数，知道从哪个tab过来的
        this.pathInstance.fromSearch = location.search
      }
    }, [str])
  }
}

const dhrReport = new Reports()
export default dhrReport
export { REPORT_EVENT_TYPE }
