/**
 * 采集匹配的事件类型(事件英文名)
 */
export const enum REPORT_EVENT_TYPE {
  // 页面浏览
  PAGE_VIEW = 'page_view',
  // 变量管理新增
  VARIABLE_CREATE = 'ContractManagement_variable_creat',
  // 变量管理查看详情
  VARIABLE_VIEW = 'ContractManagement_variable_view',
  // 变量管理编辑
  VARIABLE_EDIT = 'ContractManagement_variable_edit',
  // 变量管理停用
  VARIABLE_DEACTIVATE = 'ContractManagement_variable_deactivate',
  // 变量管理启用
  VARIABLE_ENABLE = 'ContractManagement_variable_enable',
  // 变量管理删除
  VARIABLE_DELETE = 'ContractManagement_variable_delete',
  // 变量管理查看适用模板
  VARIABLE_TEMPLATE_VIEW = 'ContractManagement_variable_template_view',
  // 模板管理新增
  TEMPLATE_CREATE = 'ContractManagement_template_creat',
  // 模板管理查看详情
  TEMPLATE_VIEW = 'ContractManagement_template_view',
  // 模板管理编辑
  TEMPLATE_EDIT = 'ContractManagement_template_edit',
  // 模板管理停用
  TEMPLATE_DEACTIVATE = 'ContractManagement_template_deactivate',
  // 模板管理启用
  TEMPLATE_ENABLE = 'ContractManagement_template_enable',
  // 模板管理删除
  TEMPLATE_DELETE = 'ContractManagement_template_delete',
  // 模板批量导出变量
  TEMPLATE_VARIABLE_BATCH_EXPORT = 'ContractManagement_template_variable_batchexport',
  // 模板单个导出变量
  TEMPLATE_VARIABLE_EXPORT = 'ContractManagement_template_variable_export',
  // 批量上传法大大
  TEMPLATE_VARIABLE_BATCH_UPLOAD = 'ContractManagement_template_variable_batchupload',
  // 单个上传法大大
  TEMPLATE_VARIABLE_UPLOAD = 'ContractManagement_template_variable_upload',
  // 模板管理查看适用合同包
  VARIABLE_PACKAGE_VIEW = 'ContractManagement_variable_package_view',
  // 参数规则保存
  CONFIG_SAVE = 'ContractManagement_config_save',
  // 合同类型规则新增
  TYPE_RULE_CREATE = 'ContractManagement_typeRule_creat',
  // 合同类型规则查看详情
  TYPE_RULE_VIEW = 'ContractManagement_typeRule_view',
  // 合同类型规则编辑
  TYPE_RULE_EDIT = 'ContractManagement_typeRule_edit',
  // 合同类型规则复制
  TYPE_RULE_COPY = 'ContractManagement_typeRule_copy',
  // 合同类型规则停用
  TYPE_RULE_DEACTIVATE = 'ContractManagement_typeRule_deactivate',
  // 合同类型规则启用
  TYPE_RULE_ENABLE = 'ContractManagement_typeRule_enable',
  // 合同类型规则删除
  TYPE_RULE_DELETE = 'ContractManagement_typeRule_delete',
  // 合同类型查看适用合同包
  TYPE_RULE_PACKAGE_VIEW = 'ContractManagement_typeRule_package_view',
  // 合同期限规则新增
  DEADLINE_RULE_CREATE = 'ContractManagement_deadlineRule_creat',
  // 合同期限规则查看详情
  DEADLINE_RULE_VIEW = 'ContractManagement_deadlineRule_view',
  // 合同期限规则编辑
  DEADLINE_RULE_EDIT = 'ContractManagement_deadlineRule_edit',
  // 合同期限规则复制
  DEADLINE_RULE_COPY = 'ContractManagement_deadlineRule_copy',
  // 合同期限规则停用
  DEADLINE_RULE_DEACTIVATE = 'ContractManagement_deadlineRule_deactivate',
  // 合同期限规则启用
  DEADLINE_RULE_ENABLE = 'ContractManagement_deadlineRule_enable',
  // 合同期限规则删除
  DEADLINE_RULE_DELETE = 'ContractManagement_deadlineRule_delete',
  // 合同期限查看适用合同包
  DEADLINE_RULE_PACKAGE_VIEW = 'ContractManagement_deadlineRule_package_view',
  // 签订工资规则新增
  SALARY_RULE_CREATE = 'ContractManagement_salaryRule_creat',
  // 签订工资规则复制
  SALARY_RULE_COPY = 'ContractManagement_salaryRule_copy', // 已废弃
  // 签订工资规则查看详情
  SALARY_RULE_VIEW = 'ContractManagement_salaryRule_view',
  // 签订工资规则编辑
  SALARY_RULE_EDIT = 'ContractManagement_salaryRule_edit',
  // 签订工资规则停用
  SALARY_RULE_DEACTIVATE = 'ContractManagement_salaryRule_deactivate',
  // 签订工资规则启用
  SALARY_RULE_ENABLE = 'ContractManagement_salaryRule_enable',
  // 签订工资规则删除
  SALARY_RULE_DELETE = 'ContractManagement_salaryRule_delete',
  // 合同包新增
  PACKAGE_CREATE = 'ContractManagement_package_creat',
  // 合同包查看详情
  PACKAGE_VIEW = 'ContractManagement_package_view',
  // 合同包编辑
  PACKAGE_EDIT = 'ContractManagement_package_edit',
  // 合同包停用
  PACKAGE_DEACTIVATE = 'ContractManagement_package_deactivate',
  // 合同包启用
  PACKAGE_ENABLE = 'ContractManagement_package_enable',
  // 合同包删除
  PACKAGE_DELETE = 'ContractManagement_package_delete',
  // 合同包批量导出变量
  PACKAGE_VARIABLE_BATCH_EXPORT = 'ContractManagement_package_variable_batchexport',
  // 合同包单个导出变量
  PACKAGE_VARIABLE_EXPORT = 'ContractManagement_package_variable_export',
  // 查看人员范围
  PACKAGE_SCOPE_VIEW = 'ContractManagement_package_scope_view',
  // 人员范围确定
  PACKAGE_SCOPE_CONFIRM = 'ContractManagement_package_scope_confirm',
  // 操作日志查询
  OPERATE_LOG_QUERY = 'ContractManagement_operatelog_query',
  // 操作日志附件下载
  OPERATE_LOG_ATTACHMENT_DOWNLOAD = 'ContractManagement_operatelog_attachment_download',
  // 签署管理重发
  SIGNING_RESEND = 'ContractManagement_signing_resend',
  // 签署管理失效
  SIGNED_FAILURE = 'ContractManagement_signed_failure',
  // 签署管理解除合同
  DEFUNCT_RESCIND = 'ContractManagement_defunct_rescind',
  // 签署管理作废
  OBSOLETE_VOID = 'ContractManagement_obsolete_void',
  // 签署管理发起签署
  SIGNING_SEND = 'ContractManagement_signing_send',
  // 签署管理导入
  SIGNED_IMPORT = 'ContractManagement_signed_import',
  // 签署管理查看详情
  SIGNING_VIEW = 'ContractManagement_signing_view',
  // 签署文件下载
  SIGN_FILE_DOWNLOAD = 'ContractManagement_signfile_download',
  // 签署文件查看
  SIGN_FILE_VIEW = 'ContractManagement_signfile_view',
}
