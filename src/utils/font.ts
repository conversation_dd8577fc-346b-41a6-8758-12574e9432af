/** 字体相关操作 */

class Detector {
  private baseFonts = ['monospace', 'sans-serif', 'serif']

  // 使用 m 和 w 作为测试字符因为它们占据最大宽度
  private testString = 'mmmmmmmmmmlli'

  // 测试字体定义为72像素
  private testSize = '72px'

  detect = (font: string): boolean => {
    const h = document.getElementsByTagName('body')[0]
    // 在文档中创建span标签放置测试字体并获取测试字体的宽度
    const s = document.createElement('span')
    s.style.fontSize = this.testSize
    s.innerHTML = this.testString
    const defaultWidth = {}
    const defaultHeight = {}

    Object.keys(this.baseFonts).forEach((index) => {
      // 获取三种默认字体的宽度及高度
      s.style.fontFamily = this.baseFonts[index]
      h.appendChild(s)
      defaultWidth[this.baseFonts[index]] = s.offsetWidth
      defaultHeight[this.baseFonts[index]] = s.offsetHeight
      h.removeChild(s)
    })

    let detected = false

    Object.keys(this.baseFonts).forEach((index) => {
      // 测试字体与基准字体拼接后获取字体宽高与基准字体宽高进行比较
      s.style.fontFamily = `${font},${this.baseFonts[index]}`
      h.appendChild(s)
      const matched =
        s.offsetWidth !== defaultWidth[this.baseFonts[index]] ||
        s.offsetHeight !== defaultHeight[this.baseFonts[index]]
      h.removeChild(s)
      detected = detected || matched
    })
    return detected
  }
}

export { Detector }
