import axios from '@galaxy/business-request'
import config from '@/config'

axios.defaults.timeout = 60000

const req = axios.inheritCreate()

const { environment } = config

export const createRequest = ({ serverUrl }) => {
  // TODO 重复请求走缓存
  const request = (reqConfig) => {
    const matchRegExp = /http|https/
    const localUrl = `${window.location.origin}/mock`
    const { isMock = false } = reqConfig
    // 仅允许本地环境使用Mock功能；
    const SERVER_URL = environment === 'local' && isMock ? localUrl : serverUrl
    const url = `${matchRegExp.test(reqConfig.url) ? '' : SERVER_URL}${reqConfig.url}${
      reqConfig.url.indexOf('?') > -1 ? '&t=' : '?t='
    }${Date.now()}`
    return {
      ...reqConfig,
      url,
    }
  }
  req.interceptors.request.use(request)
  // req.interceptors.response.use(response, responseError)

  return { req }
}
