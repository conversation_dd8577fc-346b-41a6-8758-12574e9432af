import { authHeaderRequestInterceptor, authHeaderResponseErrorInterceptor } from '@galaxy/uc' // 确保uc、rbac在请求库之前执行
import axios, { setNamespace } from '@galaxy/business-request'
import config from '@/config'
import { response, responseError } from './global'
import { apmResponse, apmResponseError } from './apmResponse'
import { TRAFFIC_FLAG_KEY } from '../traffic'

setNamespace('contract') // 暂时注释，处理集成token为空情况，等待圳尧组件库升级3.0

axios.defaults.timeout = 60000
axios.interceptors.request.use(authHeaderRequestInterceptor)
axios.interceptors.response.use(apmResponse, apmResponseError)
axios.interceptors.response.use(undefined, authHeaderResponseErrorInterceptor)

const req = axios.inheritCreate()
const basicServerReq = axios.inheritCreate()

const { environment, serverUrl, basicServerUrl } = config

/**
 * 数据隔离
 * 当全局属性（__testHeader__）存在时，设置header traffic-flag 为 test
 */
const trafficRequest = (trafficConfig) => {
  const trafficFlag = localStorage.getItem(TRAFFIC_FLAG_KEY)

  if (trafficFlag) {
    // eslint-disable-next-line no-param-reassign
    trafficConfig.headers['traffic-flag'] = 'test'
  }
  return trafficConfig
}

// TODO 重复请求走缓存
const request = (reqConfig) => {
  const matchRegExp = /http|https/
  const localUrl = `${window.location.origin}/mock`
  const { isMock = false } = reqConfig
  // 仅允许本地环境使用Mock功能；
  const SERVER_URL = environment === 'local' && isMock ? localUrl : serverUrl
  const url = `${matchRegExp.test(reqConfig.url) ? '' : SERVER_URL}${reqConfig.url}${
    reqConfig.url.indexOf('?') > -1 ? '&t=' : '?t='
  }${Date.now()}`
  return {
    ...reqConfig,
    url,
  }
}

const basicRequest = (reqConfig) => {
  const matchRegExp = /http|https/
  const SERVER_URL = basicServerUrl
  const url = `${matchRegExp.test(reqConfig.url) ? '' : SERVER_URL}${reqConfig.url}${
    reqConfig.url.indexOf('?') > -1 ? '&t=' : '?t='
  }${Date.now()}`
  return {
    ...reqConfig,
    url,
  }
}

req.interceptors.request.use(request)
req.interceptors.request.use(trafficRequest)
req.interceptors.response.use(response, responseError)
basicServerReq.interceptors.request.use(basicRequest)
basicServerReq.interceptors.response.use(response, responseError)

export { req, basicServerReq }
