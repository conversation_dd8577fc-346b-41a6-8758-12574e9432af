import { reportHttpError } from '@galaxy/utils'

const apmResponse = (response) => {
  const { code, msg } = response.data || {}
  if (typeof code === 'number' && code !== 0) {
    try {
      reportHttpError(msg, response, {})
    } catch (error) {
      /* eslint-disable */
      console.error('reportHttpError上报失败，原因：')
      console.error(error)
    }
  }
  return response
}

const apmResponseError = (error) => {
  // error 结构是 { code: number, msg: string } 是前端代码内部处理的，需要做兼容处理
  if (!error?.response && !error?.code) {
    try {
      reportHttpError('服务器开小差，请稍后重试。', error?.response, {})
    } catch (error) {
      /* eslint-disable */
      console.error('reportHttpError上报失败，原因：')
      console.error(error)
    }
    return Promise.reject(error)
  }
  const status = error?.response?.status || error?.code

  if (status !== 401) {
    try {
      reportHttpError(`服务器开小差，请稍后重试。错误状态码：${status}`, error?.response, {})
    } catch (error) {
      console.error('reportHttpError上报失败，原因：')
      console.error(error)
    }
  }
  return Promise.reject(error)
}

export { apmResponse, apmResponseError }
