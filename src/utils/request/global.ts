import { message, Modal } from 'antd'
import { logout } from '@/mainApp/services/authService'

// 处理401
function handleNoAuth() {
  message.error('认证失败，重新登录跳转中...', 3)
  setTimeout(() => {
    // 登出
    logout()
  }, 3000)
}

const response = (res) => {
  const { code, msg } = res.data
  if (typeof code === 'number' && code !== 0) {
    message.error(msg, 3)
    if (code === 401) {
      handleNoAuth()
    }
  }
  return res
}

const responseError = (error) => {
  const { status, msg, config } = error
  if (status === 401) {
    handleNoAuth()
  } else if (msg) {
    if (!config.isCustom) {
      if (config.noMessage) {
        Modal.warning({
          title: '提示', // 这个可能需要支持配置
          content: msg,
          okText: '关闭',
        })
      } else {
        message.error(`${msg}`, 3)
      }
    }
  } else {
    message.error(`服务器开小差，请稍后重试。错误状态码：${status}`, 3)
  }
  // 不要对拦截器中的数据结构做修改（error的config字段需存在）
  return Promise.reject(error)
}

export { response, responseError }
