import React from 'react'
import { trim, isUndefined, isPlainObject } from 'lodash'
import { message, Tooltip } from 'antd'
import { toUnderline } from '@galaxy/utils'
import { isNil, isString, isObject, isEmpty } from 'lodash-es'

export function getUrlParam(name, url) {
  return (
    decodeURIComponent(
      (new RegExp(`[?|&]${name}=([^&;]+?)(&|#|;|$)`).exec(url) || ['', ''])[1].replace(
        /\+/g,
        '%20',
      ),
    ) || null
  )
}

// 时间格式转换
export function formatDate(cTime, cFormat) {
  // 判空
  if (arguments.length === 0 || typeof cTime === 'undefined' || !cTime) {
    return null
  }
  let date = cTime
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  // 如果不是Date对象
  if (typeof date !== 'object') {
    if (typeof date === 'string') {
      if (/^[0-9]+$/.test(date)) {
        // support "1548221490638"
        date = Number(date)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        date = date.replace(/-/gm, '/')
      }
    }

    if (typeof cTime === 'number' && date.toString().length === 10) {
      date *= 1000
    }
    date = new Date(date)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  const timeStr = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return timeStr
}

// 获取 DPI 方法
export function getDpi() {
  for (let i = 56; i < 2000; i++) {
    if (matchMedia(`(max-resolution: ${i}dpi)`).matches === true) {
      return i
    }
  }
  return -1
}

// 毫秒转耗时（时分秒）
export const millisecondsToDate = (milliseconds) => {
  const seconds = milliseconds / 1000
  const h = Math.floor(seconds / 3600)
  const m = Math.floor((seconds / 60) % 60)
  const s = Math.floor(seconds % 60)
  return `${h < 10 ? `0${h}` : h}小时${m < 10 ? `0${m}` : m}分钟${s < 10 ? `0${s || 1}` : s}秒`
}

// 是否是本地localhost或者是本地ip
export function isLocalNetwork(hostname = window.location.hostname) {
  return (
    ['localhost', '127.0.0.1', '', '::1'].includes(hostname) ||
    hostname.startsWith('192.168.') ||
    hostname.startsWith('10.0.') ||
    hostname.endsWith('.local')
  )
}

// 将多个人员列表拼接成string
export const transformPersonString = (value) => {
  if (!value) {
    return null
  }
  const str = (value.length > 5 ? value.slice(0, 5) : value)
    .map((item) => `${item.name}（${item.num}）`)
    .join('，')
  return `${str}${value.length > 5 ? `等${value.length}个人` : ''}`
}

// 处理名称string
export const stringTransform = (string) =>
  string
    ? string
        .replace(/，/g, ',')
        .split(',')
        .map((i) => trim(i))
        .join(',')
    : undefined

// 下载文件
export const downloadExcelFile = (res) => {
  return new Promise((resolve, reject) => {
    const type = res?.data?.type
    // 后端下载失败时返回的data类型也是blob,需要解析一下获取错误信息
    if (type === 'application/json') {
      const reader = new FileReader()
      reader.readAsText(res.data, 'utf-8')
      reader.onload = function () {
        try {
          const jsonRes = typeof reader.result === 'string' && JSON.parse(reader.result) // 获取blob数据转换为json后的数据，即后台返回的原始数据
          if (jsonRes?.code !== 0) {
            message.error(jsonRes?.msg, 3)
            reject(jsonRes)
          } else {
            resolve(jsonRes)
          }
        } catch (err) {
          reject(err)
        }
      }
      reader.onerror = (err) => {
        reject(err)
      }
    } else {
      const blob = new Blob([res.data], {
        type: type || 'application/x-xls',
      }) // type是文件类，详情可以参阅blob文件类型
      // 创建新的URL并指向File对象或者Blob对象的地址
      const blobURL = window.URL.createObjectURL(blob)
      // 创建a标签，用于跳转至下载链接
      const tempLink = document.createElement('a')
      tempLink.style.display = 'none'
      tempLink.href = blobURL
      const fileName = decodeURIComponent(
        res.headers['content-disposition']?.split(';')[1].split('=')[1],
      )
      tempLink.setAttribute('download', fileName.replace(new RegExp('"', 'g'), ''))
      // 兼容：某些浏览器不支持HTML5的download属性
      if (typeof tempLink.download === 'undefined') {
        tempLink.setAttribute('target', '_blank')
      }
      // 挂载a标签
      document.body.appendChild(tempLink)
      tempLink.click()
      document.body.removeChild(tempLink)
      // 释放blob URL地址
      window.URL.revokeObjectURL(blobURL)
      resolve('')
    }
  })
}

// 多列排序，默认排序
// 升级table后sorter参数变化，需要调整
export const handleSort = (sorter: any) => {
  const sort: string[] = []
  const orderMap = {
    ascend: 'asc',
    descend: 'desc',
  }

  if (Array.isArray(sorter)) {
    sorter.forEach((item: any) => {
      if (!isUndefined(item.order)) {
        sort.push(`${toUnderline(item.field)},${orderMap[item.order]}`)
      }
    })
  } else if (isPlainObject(sorter)) {
    if (sorter.field) {
      !isUndefined(sorter.order) &&
        sort.push(`${toUnderline(sorter.field)},${orderMap[sorter.order]}`)
    } else if (Object.keys(sorter).length === 1) {
      // 默认，第一次的默认和单个排序的返回对象格式不一样
      const key = Object.keys(sorter)[0]
      !isUndefined(sorter[key]) && sort.push(`${toUnderline(key)},${orderMap[sorter[key]]}`)
    } else {
      // 默认排序
      Object.keys(sorter).forEach((key) => {
        sort.push(`${toUnderline(key)},${orderMap[sorter[key]]}`)
      })
    }
  }
  return sort.join('&')
}

const base64ToBlob = (base64String) => {
  const parts = base64String.split(';base64,')
  const contentType = parts[0].split(':')[1]
  const byteCharacters = atob(parts[1])
  const byteNumbers = new Array(byteCharacters.length)
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i)
  }
  const byteArray = new Uint8Array(byteNumbers)
  return new Blob([byteArray], {
    type: contentType,
  })
}

// 通过 base64 下载文件
export const downloadFile = (fileData, fileName) => {
  const blob = base64ToBlob(fileData)
  const url = URL.createObjectURL(blob)

  const downloadLink = document.createElement('a')
  downloadLink.download = fileName
  downloadLink.href = url
  document.body.appendChild(downloadLink)
  downloadLink.click()
  document.body.removeChild(downloadLink)
}

// 用来多选下拉，悬浮+号上的文字显示
export const maxTagPlaceholder =
  (field = 'label') =>
  (omittedValues: any[] = []) => {
    const length = omittedValues.length
    const title: any[] = []
    omittedValues.forEach((item) => {
      const value = item && isObject(item) ? item[field] : null
      if (value) {
        title.push(item[field])
      }
    })
    return <Tooltip title={title.join(',')}>+{length}</Tooltip>
  }

// 将小驼峰转化成AA_BB格式
export const transformStringType = (str) => {
  return str.replace(/([a-z])([A-Z])/g, '$1_$2').toUpperCase()
}

// 判断一些属性是否都被两个对象包含，并且值相等
export const isObjectEqual = (obj1, obj2, keys) => {
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i]
    // eslint-disable-next-line no-prototype-builtins
    if (!obj1.hasOwnProperty(key) || !obj2.hasOwnProperty(key) || obj1[key] !== obj2[key]) {
      return false
    }
  }
  return true
}

// 将数组中的某一项移动到开头
export const moveToFront = (arr, target) => {
  const index = arr.findIndex((i) => i.value === target)
  if (index !== 0) {
    arr.splice(index, 1)
    arr.unshift({
      value: target,
      label: target,
    })
  }
  return arr
}

/**
 * 判断字符串是否为 JSON 格式
 */
export const isJSON = (str) => {
  if (typeof str === 'string') {
    try {
      const obj = JSON.parse(str)
      if (typeof obj === 'object' && obj) {
        return true
      }
      return false
    } catch (e) {
      return false
    }
  }
  return false
}

export const isEmptyValue = (value) => {
  // 如果值是 null 或 undefined，返回 true
  if (isNil(value)) {
    return true
  }

  // 如果值是空字符串，返回 true
  if (isString(value) && value.trim() === '') {
    return true
  }

  // 使用 Lodash 的 isEmpty 方法检查对象、数组等是否为空
  if (isObject(value) && isEmpty(value)) {
    return true
  }

  // 如果以上条件都不符合，则返回 false
  return false
}
