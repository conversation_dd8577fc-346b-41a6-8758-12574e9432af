/* eslint-disable no-param-reassign */
import React from 'react'
import isString from 'lodash/isString'
import isArray from 'lodash/isArray'
import cloneDeep from 'lodash/cloneDeep'
import AsyncComponent from '@/utils/AsyncComponent'
import config from '@/config'
import routeMapList from '@/router/route'

export interface RouteInterface {
  path: string
  name?: string
  icon?: React.ReactNode
  component?: string | React.FC
  routes?: RouteInterface[]
  hideInMenu?: boolean
}

export type Route = RouteInterface | RouteInterface[]

class RouteFactory {
  private $route: Route

  constructor(route: Route) {
    this.$route = RouteFactory.warpRoute(RouteFactory.joinBaseName(route))
  }

  get route() {
    return cloneDeep(this.$route)
  }

  set route(value) {
    this.$route = value
  }

  // path -> baseName + path 副作用，直接改动源数组
  static joinBaseName = (routeList: Route): Route => {
    const result: Route = cloneDeep(routeList)
    const deep = (route) => {
      if (Array.isArray(route)) {
        route.forEach((item) => {
          item.path = `${config.baseRoute}${item.path}`

          if (item.routes) {
            deep(item.routes)
          }
        })
        return
      }
      route.path = `${config.baseRoute}${route.path}`
      if (route.routes) {
        deep(route.routes)
      }
    }
    deep(result)
    return result
  }

  // 路由组件地址 -> 路由组件 副作用，直接改动源数组
  static warpRoute = (routeList: Route): Route => {
    const result: Route = cloneDeep(routeList)
    const deep = (route) => {
      if (Array.isArray(route)) {
        route.forEach((item) => {
          const component = item.component
          if (isString(component)) {
            item.component = AsyncComponent(() => import(/* @vite-ignore */ `../pages${component}`))
          }

          if (item.routes) {
            deep(item.routes)
          }
        })
        return
      }
      if (isString(route.component)) {
        const component = route.component
        route.component = AsyncComponent(() => import(/* @vite-ignore */ `../pages${component}`))
      }
      if (route.routes) {
        deep(route.routes)
      }
    }
    deep(result)
    return result
  }

  transformMap(route = this.route) {
    // todo 很奇怪第一层路由无法被识别，这边先route = route.routes
    if (!isArray(route)) {
      route = route.routes || []
    }

    return route.reduce((result, item) => {
      result[item.path] = item
      if (item.routes) {
        result = {
          ...result,
          ...this.transformMap(item.routes),
        }
      }
      return result
    }, {})
  }

  transformList(route?) {
    let $routes = route || this.route
    if (!isArray($routes)) {
      $routes = $routes.routes || []
    }

    return $routes.reduce((result, item) => {
      result.push(item)
      if (item.routes) {
        result = result.concat(this.transformList(item.routes))
      }
      return result
    }, [])
  }
}

const routeFactory = new RouteFactory(routeMapList)

export { routeFactory }

export default RouteFactory
