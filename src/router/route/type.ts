export interface RouteInterface {
  path: string
  name?: string
  icon?: React.ReactNode
  component?: string | React.FC
  routes?: RouteInterface[]
  hideInMenu?: boolean
  hideChildrenInMenu?: boolean
  permission?: boolean
  breadcrumb?: Record<string, any>
  dms?: {
    tabMap: {
      default: string // 默认tab
      [key: string]: string
    }
  }
}

export type Route = RouteInterface | RouteInterface[]
