import { RouteInterface } from './type'

const basename = '/contract'

const mainRoutes: RouteInterface[] = [
  {
    path: basename,
    name: '合同管理',
    component: '/portal/portal-main/index.tsx',
    routes: [
      {
        name: '测试pdf合同模板',
        path: `${basename}/test`,
        component: '/basicConfig/template/create/index.tsx',
        hideInMenu: true,
      },
      {
        name: '签署管理',
        path: `${basename}/sign-manage`,
        permission: true,
        breadcrumb: {
          clickable: false,
        },
        dms: {
          tabMap: {
            default: 'signing',
            signing: '签署中',
            signed: '已签署',
            signExpired: '已失效',
            signVoid: '已作废',
          },
        },
        routes: [
          {
            name: '朴朴员工签署管理',
            path: `${basename}/sign-manage/inside`,
            component: '/signManage/index.tsx',
            hideChildrenInMenu: true,
            permission: true,
            routes: [
              {
                name: '签署详情',
                path: `${basename}/sign-manage/inside/detail`,
                component: '/signManage/detail/index.tsx',
                hideInMenu: true,
              },
              {
                name: '签署文件预览',
                path: `${basename}/sign-manage/inside/preview`,
                component: '/signManage/preview/index.tsx',
                hideInMenu: true,
              },
              {
                name: '发起签署',
                path: `${basename}/sign-manage/inside/launch`,
                component: '/signManage/mutipleLaunch/index.tsx',
                hideInMenu: true,
              },
              {
                name: '解除合同',
                path: `${basename}/sign-manage/inside/remove`,
                component: '/signManage/remove/index.tsx',
                hideInMenu: true,
              },
              {
                name: '作废',
                path: `${basename}/sign-manage/inside/cancel`,
                component: '/signManage/cancel/index.tsx',
                hideInMenu: true,
              },
            ],
          },
          {
            name: '自营第三方签署管理',
            path: `${basename}/sign-manage/thirdparty`,
            component: '/thirdpartySignManage/index.tsx',
            permission: true,
            hideChildrenInMenu: true,
            routes: [
              {
                name: '签署详情',
                path: `${basename}/sign-manage/thirdparty/detail`,
                component: '/thirdpartySignManage/detail/index.tsx',
                hideInMenu: true,
              },
              {
                name: '签署文件预览',
                path: `${basename}/sign-manage/thirdparty/preview`,
                component: '/thirdpartySignManage/preview/index.tsx',
                hideInMenu: true,
              },
              {
                name: '发起签署',
                path: `${basename}/sign-manage/thirdparty/launch`,
                component: '/thirdpartySignManage/mutipleLaunch/index.tsx',
                hideInMenu: true,
              },
              {
                name: '解除合同',
                path: `${basename}/sign-manage/thirdparty/remove`,
                component: '/thirdpartySignManage/remove/index.tsx',
                hideInMenu: true,
              },
              {
                name: '作废',
                path: `${basename}/sign-manage/thirdparty/cancel`,
                component: '/thirdpartySignManage/cancel/index.tsx',
                hideInMenu: true,
              },
            ],
          },
        ],
      },
      {
        name: '基础配置',
        path: `${basename}/basic-config`,
        permission: true,
        breadcrumb: {
          clickable: false,
        },
        routes: [
          {
            name: '变量管理',
            path: `${basename}/basic-config/variable-manage`,
            component: '/basicConfig/variableManage/index.tsx',
            hideChildrenInMenu: true,
            permission: true,
            routes: [
              {
                name: '变量详情',
                path: `${basename}/basic-config/variable-manage/detail`,
                component: '/basicConfig/variableManage/detail/index.tsx',
              },
            ],
          },
          {
            name: '模板管理',
            path: `${basename}/basic-config/template`,
            component: '/basicConfig/template/index.tsx',
            hideChildrenInMenu: true,
            permission: true,
            routes: [
              {
                name: '新增模板',
                path: `${basename}/basic-config/template/create`,
                component: '/basicConfig/template/create/index.tsx',
              },
              {
                name: '编辑模板',
                path: `${basename}/basic-config/template/edit`,
                component: '/basicConfig/template/create/index.tsx',
              },
              {
                name: '模板详情',
                path: `${basename}/basic-config/template/detail`,
                component: '/basicConfig/template/detail/index.tsx',
              },
            ],
          },
          {
            name: '规则设置',
            path: `${basename}/basic-config/rule-config`,
            component: '/basicConfig/ruleConfig/index.tsx',
            hideChildrenInMenu: true,
            permission: true,
            dms: {
              tabMap: {
                default: 'paramsRule',
                paramsRule: '参数规则',
                typeRule: '合同类型规则',
                deadlineRule: '合同期限规则',
                salaryRule: '签订工资规则',
              },
            },
            routes: [
              {
                name: '新增规则',
                path: `${basename}/basic-config/rule-config/create`,
                component: '/basicConfig/ruleConfig/edit/index.tsx',
              },
              {
                name: '编辑规则',
                path: `${basename}/basic-config/rule-config/edit`,
                component: '/basicConfig/ruleConfig/edit/index.tsx',
              },
              {
                name: '规则详情',
                path: `${basename}/basic-config/rule-config/detail`,
                component: '/basicConfig/ruleConfig/detail/index.tsx',
              },
            ],
          },
          {
            name: '合同包',
            path: `${basename}/basic-config/contract-package`,
            component: '/basicConfig/contractPackage/index.tsx',
            hideChildrenInMenu: true,
            permission: true,
            routes: [
              {
                path: `${basename}/basic-config/contract-package/create`,
                name: '新增合同包',
                component: '/basicConfig/contractPackage/edit/index',
              },
              {
                path: `${basename}/basic-config/contract-package/edit`,
                name: '编辑合同包',
                component: '/basicConfig/contractPackage/edit/index',
              },
              {
                path: `${basename}/basic-config/contract-package/view`,
                name: '合同包详情',
                component: '/basicConfig/contractPackage/edit/index',
              },
              {
                path: `${basename}/basic-config/contract-package/person-scope`,
                name: '人员范围',
                component: '/basicConfig/contractPackage/personScope',
              },
              {
                path: `${basename}/basic-config/contract-package/verification-personnel`,
                name: '校验人员',
                component: '/basicConfig/contractPackage/verificationPersonnel',
              },
            ],
          },
        ],
      },
      {
        name: '操作日志',
        path: `${basename}/operate-log`,
        component: '/operateLog/index.tsx',
        permission: true,
      },
      {
        name: '模板预览',
        path: `${basename}/template-preview`,
        component: '/templatePreview/index.tsx',
        hideInMenu: true,
      },
    ],
  },
]

export default mainRoutes
