import React from 'react'
import { ContainerOutlined } from '@ant-design/icons'
import mainRoutes from './mainRoutes'
import { RouteInterface, Route } from './type'

const baseRoutes: RouteInterface[] = [
  {
    name: '个人中心',
    icon: <ContainerOutlined />,
    path: '/user',
    component: '/user/index.tsx',
    hideInMenu: true,
  },
  {
    name: '403',
    path: '/403',
    icon: <ContainerOutlined />,
    component: '/exception/403/index.tsx',
    hideInMenu: true,
  },
  {
    name: '404',
    path: '/404',
    icon: <ContainerOutlined />,
    component: '/exception/404/index.tsx',
    hideInMenu: true,
  },
  {
    name: '500',
    path: '/500',
    icon: <ContainerOutlined />,
    component: '/exception/500/index.tsx',
    hideInMenu: true,
  },
]

const route: Route = {
  path: '/',
  routes: mainRoutes.concat(baseRoutes),
}

export default route
