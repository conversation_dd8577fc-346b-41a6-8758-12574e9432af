import React from 'react'
import { BrowserRouter, Route, Routes, Navigate } from 'react-router-dom'
import AsyncComponent from '@/utils/AsyncComponent'
import RouteFactory from '@/router/RouteFactory'
import route from '@/router/route'
import config from '@/config'
import authWrapper from '@/mainApp/decorators/authWrapper'
import rbacWrapper from '@/mainApp/decorators/rbacWrapper'
import { isMicroApp } from '@/constants'
import '@amazebird/antd-field'
import DhrContainer from '@/components/DhrContainer'

const { baseRoute, loginUrl } = config

const BaseLayout = authWrapper(rbacWrapper(AsyncComponent(() => import('@/layouts/BaseLayout'))))
const MicroLayout = AsyncComponent(() => import('@/layouts/MicroLayout'))
const Login = AsyncComponent(() => import('@/mainApp/login-manage/login'))

const routeFactory = new RouteFactory(route)
const routes: any[] = routeFactory.transformList()

const Router = () => {
  const Layout = isMicroApp ? <MicroLayout /> : <BaseLayout />
  return (
    <BrowserRouter>
      <Routes>
        <Route path={`${baseRoute}/login`} element={<Login />} />
        <Route path={`${baseRoute}`} element={Layout}>
          {routes.map((item) => {
            // 处理路由的 component 参数为空
            const GlobalComponent = item.component || React.Fragment

            const PageElement = (
              <DhrContainer defaultTitle={item.name}>
                <GlobalComponent />
              </DhrContainer>
            )
            const hasToken = true
            return hasToken ? (
              <Route key={`${item.path}${item.name}`} path={item.path} element={PageElement} />
            ) : (
              <Route
                path="*"
                element={
                  <Navigate
                    to={{
                      pathname: loginUrl,
                    }}
                  />
                }
              />
            )
          })}
        </Route>
      </Routes>
    </BrowserRouter>
  )
}

export default Router
