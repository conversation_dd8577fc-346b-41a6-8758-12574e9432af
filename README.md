# 人才档案

## 主体目录结构

```text
├─ build   构建脚本
├─ config  项目配置项信息
├─ docker  部署配置信息
├─ src     源码目录
│  ├─ .mock  mock接口数据目录
│  ├─ api    统一接口定义目录
│  ├─ assets 资源文件目录
│  ├─ components  公共组件目录
│  ├─ constants   常量目录
│  ├─ micro-app   微前端目录
│  ├─ pages    页面目录
│  ├─ router   路由配置目录
│  ├─ services 服务目录
│  │  └─ xxx.service.ts      业务服务
│  ├─ stores   状态管理目录
│  │  └─ xxx.store.ts      状态类
│  ├─ themes   主题样式目录
│  ├─ typings  ts声明文件目录
│  ├─ utils    工具类目录
│  │  ├─ request              统一接口请求模块
│  │  ├─ AsyncComponent.js    异步加载模块
│  │  └─ utils                其他工具方法模块
│  ├─ APP.tsx    App入口文件
│  └─ index.tsx  项目入口文件（判断是否作为子应用运行）
```

## 结构与规范说明

* 所有常量都必须声明在 `constants` 目录下
* 所有接口请求都统一定义在 `/src/services/api` 目录下
* 页面中涉及到`store` 和 `service`的，不要写在`pages` 目录下，应该相应的放置到`stores`和 `services`目录中。

## service,store和view的关系

**service**  是对领域模型的能力封装,是业务方法的集合
**store** 是联接视图和领域模型的桥接
**view**  是具体的UI展现组件，`view`作为UI组件，主要负责页面布局，动态交互效果与数据展现，而对应的业务逻辑,数据的获取与处理都应该被封装到对应业务领域的`service`中。

**service**，**store**和**view**可以类比成MVC架构中的 `model`,`controller`和`view`
但与MVC不同的是，`view`与`service`的关系并不一定需要通过`store`进行关联，只有需要保存全局状态时，才需要通过`store`层。

![service-store-view](/src/assets/service-store-view.png)
(整体分层结构)

[前端开发规范文档](https://pu5hkzsjyc.feishu.cn/wiki/wikcnKkYhuQsINrwNq6sYj0zjYb)
