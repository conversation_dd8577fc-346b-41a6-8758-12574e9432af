{"name": "contract-webapp", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"cz": "git-cz", "ready": "yarn", "lint": "eslint ./src --ext js,jsx,ts,tsx", "lint-fix": "eslint --fix --ext .js,jsx,ts,tsx src/", "clean:lib": "rimraf ./lib", "clean:dist": "rimraf ./dist", "predll": "npm run clean:lib", "----- webpack开发模式 -----": "", "local": "cross-env NODE_ENV=local mf-core serve --webpack-config ./build/dev.config.js", "dev": "cross-env NODE_ENV=dev mf-core serve--webpack-config ./build/dev.config.js", "test": "cross-env NODE_ENV=test mf-core serve  --webpack-config ./build/dev.config.js", "itPre": "cross-env NODE_ENV=itPre mf-core serve  --webpack-config ./build/dev.config.js", "prod": "cross-env NODE_ENV=prod mf-core serve --webpack-config ./build/dev.config.js", "----- vite开发模式 -----": "", "vite:local": "cross-env NODE_ENV=local vite  --config ./build/vite.config.js", "vite:dev": "cross-env NODE_ENV=test vite  --config ./build/vite.config.js", "vite:test": "cross-env NODE_ENV=test vite  --config ./build/vite.config.js", "vite:itPre": "cross-env NODE_ENV=itPre vite  --config ./build/vite.config.js", "----- 微前端本地开发模式 -----": "", "micro:local": "cross-env NODE_ENV=local mf-core server  --config ./build/micro.config.js", "micro:dev": "cross-env NODE_ENV=dev mf-core server --config ./build/micro.config.js", "micro:test": "cross-env NODE_ENV=test mf-core server   --config ./build/micro.config.js", "micro:itPre": "cross-env NODE_ENV=itPre mf-core server   --config ./build/micro.config.js", "micro:prod": "cross-env NODE_ENV=prod mf-core server  --config ./build/micro.config.js", "----- 微前端+生成前端静态资源包 -----": "", "build:testdhrnx": "cross-env NODE_ENV=test cross-env NODE_OPTIONS=--max_old_space_size=4096 mf-core build --webpack-config ./build/microBuild.config.js", "build:proddhrnx": "cross-env NODE_ENV=prod cross-env NODE_OPTIONS=--max_old_space_size=4096 mf-core build --webpack-config ./build/microBuild.config.js", "----- 微前端+it测试和集成环境+生成前端静态资源包 -----": "", "build:dev": "cross-env NODE_ENV=dev cross-env NODE_OPTIONS=--max_old_space_size=4096 mf-core build --webpack-config ./build/microBuild.config.js", "build:devdhr": "cross-env NODE_ENV=test cross-env NODE_OPTIONS=--max_old_space_size=4096 mf-core build --webpack-config ./build/microBuild.config.js", "build:test": "cross-env NODE_ENV=test cross-env NODE_OPTIONS=--max_old_space_size=4096 mf-core build --webpack-config ./build/microBuild.config.js", "build:pre": "cross-env NODE_ENV=itPre cross-env NODE_OPTIONS=--max_old_space_size=4096 mf-core build --webpack-config ./build/microBuild.config.js", "build:prod": "cross-env NODE_ENV=prod cross-env NODE_OPTIONS=--max_old_space_size=4096 mf-core build --webpack-config ./build/microBuild.config.js", "build:testctyunit": "cross-env NODE_ENV=test cross-env NODE_OPTIONS=--max_old_space_size=4096 mf-core build --webpack-config ./build/microBuild.config.js", "build:prectyunit": "cross-env NODE_ENV=itPre cross-env NODE_OPTIONS=--max_old_space_size=4096 mf-core build --webpack-config ./build/microBuild.config.js", "build:prodit": "cross-env NODE_ENV=prod cross-env NODE_OPTIONS=--max_old_space_size=4096 mf-core build --webpack-config ./build/microBuild.config.js", "----- 构建分析 -----": "", "build:analy": "cross-env TEMP_TOKEN=test NODE_ENV=prod BUILD_ENV=online mf-core build --webpack-config ./build/analy.config.js"}, "pre-commit": ["lint"], "repository": {"type": "git"}, "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-import-replacement": "^0.1.1", "@babel/plugin-proposal-class-properties": "^7.16.0", "@babel/plugin-proposal-decorators": "^7.20.13", "@babel/plugin-proposal-do-expressions": "^7.16.0", "@babel/plugin-proposal-export-default-from": "^7.16.0", "@babel/plugin-proposal-export-namespace-from": "^7.16.0", "@babel/plugin-proposal-function-bind": "^7.16.0", "@babel/plugin-proposal-function-sent": "^7.16.0", "@babel/plugin-proposal-json-strings": "^7.16.0", "@babel/plugin-proposal-logical-assignment-operators": "^7.16.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.0", "@babel/plugin-proposal-numeric-separator": "^7.16.0", "@babel/plugin-proposal-optional-chaining": "^7.16.0", "@babel/plugin-proposal-pipeline-operator": "^7.16.0", "@babel/plugin-proposal-throw-expressions": "^7.16.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.16.0", "@babel/plugin-transform-runtime": "^7.21.4", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@babel/preset-typescript": "^7.16.7", "@commitlint/cli": "^17.3.0", "@commitlint/config-conventional": "^17.3.0", "@galaxy/commit-config": "0.0.2", "@galaxy/dependency-collect": "1.0.0", "@galaxy/dhr-standard": "0.0.6", "@galaxy/mf-core": "0.3.7-beta.7", "@originjs/vite-plugin-commonjs": "^1.0.2", "@pupu/apm-upload-sourcemap-plugin": "^2.0.1", "@types/history": "^4.7.9", "@types/node": "^16.11.6", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/react-router-dom": "^5.3.2", "@types/react-transition-group": "^4.4.4", "add-asset-html-webpack-plugin": "^3.2.0", "antd-dayjs-webpack-plugin": "^1.0.6", "aws-sdk": "2.1269.0", "babel-eslint": "^9.0.0", "babel-loader": "^8.3.0", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-import": "^1.13.3", "babel-plugin-lodash": "^3.3.4", "commitizen": "^4.3.0", "compression-webpack-plugin": "^9.0.1", "copy-webpack-plugin": "^10.0.0", "cross-env": "^7.0.3", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^5.0.0", "cz-conventional-changelog": "^3.3.0", "cz-git": "^1.4.1", "eslint": "^7.32.0", "eslint-config-airbnb": "^19.0.2", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.3.0", "file-system": "^2.2.2", "fs-extra": "^11.1.1", "happypack": "^5.0.1", "html-webpack-deploy-plugin": "^3.0.0", "html-webpack-plugin": "^5.5.0", "husky": "^8.0.2", "less": "^4.1.2", "less-loader": "^10.2.0", "lint-staged": "^13.1.1", "mini-css-extract-plugin": "^2.4.5", "mocker-api": "^2.9.4", "mockjs": "^1.1.0", "pre-commit": "^1.2.2", "prettier": "^2.8.4", "rc-menu": "^9.8.4", "resolve-url-loader": "^4.0.0", "rimraf": "^3.0.2", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "terser-webpack-plugin": "^5.3.7", "thread-loader": "^4.0.1", "typescript": "^4.9.5", "vite": "^2.7.2", "vite-plugin-env-compatible": "^1.1.1", "vite-plugin-html": "^2.1.2", "webpack": "5.65.0", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "4.10.0", "webpack-dev-server": "^4.11.0", "webpack-merge": "^5.8.0"}, "dependencies": {"@amazebird/antd-business-field": "^3.5.0", "@amazebird/antd-field": "^3.5.0", "@amazebird/antd-schema-form": "3.5.0", "@amazebird/antd-schema-table": "3.5.0", "@amazebird/form": "3.5.0", "@amazebird/schema-form": "3.5.0", "@ant-design/icons": "^4.8.0", "@ant-design/pro-form": "^1.52.13", "@ant-design/pro-layout": "6.31.4", "@aws-sdk/abort-controller": "^3.272.0", "@babel/runtime": "^7.21.0", "@babel/runtime-corejs3": "^7.20.13", "@galaxy/app-updater": "^0.0.12", "@galaxy/business-request": "^3.2.0-alpha.0", "@galaxy/component": "^0.2.0", "@galaxy/config-provider": "^0.1.2", "@galaxy/dhr-style": "0.2.4-alpha.2", "@galaxy/dict": "3.8.0", "@galaxy/dict-core": "3.8.0", "@galaxy/foundation": "2.12.0-beta.4", "@galaxy/org-selector": "4.4.0", "@galaxy/rbac": "3.7.0", "@galaxy/rbac-component": "3.7.0", "@galaxy/request": "^2.0.1", "@galaxy/uc": "3.3.22", "@galaxy/uc-component": "3.2.7-alpha.1", "@galaxy/upload": "3.4.10", "@galaxy/user-selector": "4.6.0", "@galaxy/utils": "1.1.1-alpha.7", "@pupu/apm-browser": "^1.18.0", "@pupu/brick": "^1.69.3", "@pupu/elangsion-web": "^1.1.8", "@pupu/phooks": "^1.6.1", "@pupu/utils": "^1.3.1", "ahooks": "^3.5.0", "antd": "4.24.7", "axios": "^0.24.0", "babel-polyfill": "^6.26.0", "classnames": "^2.3.1", "clean-webpack-plugin": "^4.0.0", "core-js": "3", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "echarts": "^5.4.0", "immutability-helper": "^3.1.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "moment": "^2.29.4", "normalize.css": "^8.0.1", "numbro": "^2.4.0", "pako": "^2.0.4", "prop-types": "^15.7.2", "query-string": "^7.1.1", "rc-field-form": "^1.27.4", "rc-util": "^5.27.2", "re-resizable": "^6.9.9", "react": "18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "18.2.0", "react-if": "^4.1.4", "react-pdf": "^6.2.1", "react-router-dom": "^6.8.1", "react-sortable-hoc": "^2.0.0", "react-window": "^1.8.9", "regenerator-runtime": "^0.13.11", "ua-parser-js": "^1.0.35", "zustand": "^4.3.8"}, "lint-staged": {"*.{tsx,ts,jsx,js}": "eslint --fix"}, "engines": {"node": ">=20", "pnpm": "8.*.*"}, "config": {"commitizen": {"path": "./node_modules/cz-git", "czConfig": "./node_modules/@galaxy/dhr-standard/dist/cz.config.js"}}, "packageManager": "pnpm@8.15.9+sha512.499434c9d8fdd1a2794ebf4552b3b25c0a633abcee5bb15e7b5de90f32f47b513aca98cd5cfd001c31f0db454bc3804edccd578501e4ca293a6816166bbd9f81"}