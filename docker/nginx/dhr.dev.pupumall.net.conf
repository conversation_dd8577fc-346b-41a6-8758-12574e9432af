server {
  listen 80;
  server_name *.befreesh.com *.pupumall.net;

  location /static {
    root /webapp;
  }

  location /contract-webapp {
    #### no cache
    if ($request_filename ~* \.html$) {
      add_header Last-Modified $date_gmt;
      add_header Cache-Control 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
      expires off;
    }
    root /webapp;
    index index.html;
    try_files $uri /contract-webapp/index.html;
  }

  location ~* ^.+\.(jpg|jpeg|gif|png|ico|css|js)$ {
    root /webapp;
    access_log off;
    expires 30d;

    #### 不缓存模块联邦相关配置
    if ($request_filename ~* module-federation-entry\.js$) {
      add_header Last-Modified $date_gmt;
      add_header Cache-Control 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
      expires off;
    }
  }
}
