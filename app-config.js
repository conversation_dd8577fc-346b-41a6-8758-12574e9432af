const path = require('path')

const config = {
  /**
   * 远程模块的名称
   */
  name: 'contract_webapp',
  exposes: [
    {
      name: 'setPublicPath',
      path: path.resolve(process.cwd(), 'src/publicPath.ts'),
    },
    {
      name: 'Cancel',
      path: path.resolve(process.cwd(), 'src/pages/signManage/cancel/index.tsx'),
    },
  ],
  shared: {
    react: { singleton: true, requiredVersion: '18.2.0' },
    'react-dom': { singleton: true, requiredVersion: '18.2.0' },
    'react-router-dom': { singleton: true, requiredVersion: '^6.0.0' },
  },
  library: { type: 'window', name: 'contract_webapp' },
}

module.exports = config
